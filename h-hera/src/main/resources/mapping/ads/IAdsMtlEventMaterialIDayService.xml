<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yaowu.hera.domain.ads.service.batis.mapper.AdsMtlEventMaterialIDayMapper">

    <select id="aggregateEventStats" resultType="com.yaowu.hera.model.vo.ads.AdsTaskStateTempVO">
        SELECT
        material_id,
        event_id,
        SUM(uv) as uv,
        SUM(pv) as pv
        FROM ads_mtl_event_material_i_day
        WHERE material_id IN
        <foreach collection="materialIds" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
        AND event_id IN
        <foreach collection="eventIds" item="eventId" open="(" separator="," close=")">
            #{eventId}
        </foreach>
        AND ds BETWEEN #{startDate} AND #{endDate}
        GROUP BY material_id, event_id
    </select>


</mapper>
