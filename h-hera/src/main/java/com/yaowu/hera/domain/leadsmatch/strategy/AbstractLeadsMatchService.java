package com.yaowu.hera.domain.leadsmatch.strategy;

import cn.hutool.core.collection.CollectionUtil;
import com.freedom.web.model.resp.BasePage;
import com.google.common.collect.Maps;
import com.yaowu.hera.config.nacos.mtl.DeviceConfig;
import com.yaowu.hera.config.nacos.mtl.MerchantFindCustomerConfig;
import com.yaowu.hera.domain.common.service.batis.service.IAddressBookService;
import com.yaowu.hera.domain.feign.MelinaFeignBizService;
import com.yaowu.hera.domain.leads.service.batis.service.ILeadsInfoExtService;
import com.yaowu.hera.domain.leads.service.batis.service.ILeadsInfoService;
import com.yaowu.hera.enums.merchant.MerchantStoreSettleInStatusEnum;
import com.yaowu.hera.model.bo.mtl.LeadsAllBO;
import com.yaowu.hera.model.entity.leads.LeadsInfo;
import com.yaowu.hera.model.entity.leads.LeadsInfoExt;
import com.yaowu.hera.model.vo.clue.MelinaStorePageVO;
import com.yaowu.hera.utils.*;
import com.yaowu.heraapi.enums.common.AddressBookTypeEnum;
import com.yaowu.heraapi.model.pojo.common.AreaAddressModel;
import com.yaowu.melinaapi.model.dto.store.RemoteMatchStorePageDTO;
import jakarta.annotation.Resource;
import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;

/**
 * 匹配商户抽象类，提供公共方法
 *
 * <AUTHOR>
 * Created by on 2024-10-31 11:09
 */
@Slf4j
@Deprecated
public abstract class AbstractLeadsMatchService {
    @Resource
    private MelinaFeignBizService melinaFeignBizService;
    @Resource
    private DeviceConfig deviceConfig;
    @Resource
    private ILeadsInfoService leadsInfoService;
    @Resource
    private ILeadsInfoExtService extService;
    @Resource
    private IAddressBookService iAddressBookService;
    @Resource
    protected MerchantFindCustomerConfig merchantFindCustomerConfig;
    @Resource
    private DataMetricsLogUtl dataMetricsLogUtl;


    /**
     * 查询匹配的商户数量
     *
     * @param paramBO
     * @return
     */
    protected MatchMerchantResultBO storeCount(MatchMerchantParamBO paramBO) {
        RemoteMatchStorePageDTO dto = toRemoteMatchStorePageDTO(paramBO);
        Integer count = melinaFeignBizService.matchStoreCount(dto);
        return MatchMerchantResultBO.of(count, List.of());
    }

    /**
     * 查询匹配的商户列表
     *
     * @param paramBO
     * @return
     */
    protected MatchMerchantResultBO storeList(MatchMerchantParamBO paramBO) {
        RemoteMatchStorePageDTO dto = toRemoteMatchStorePageDTO(paramBO);
        BasePage<MelinaStorePageVO> basePage = melinaFeignBizService.matchStorePage(dto);
        return MatchMerchantResultBO.of(basePage.getRecords().size(), basePage.getRecords());
    }


    /**
     * 组装线索相关信息
     *
     * @param leadsId
     * @return
     */
    public LeadsAllBO getLeadsAllBO(Long leadsId) {
        if (LongUtils.isInvalid(leadsId)) {
            log.error("线索信息缺失，leadsId={}", leadsId);
            BizException.isFail(false, ErrorCode.LEADS_NOT_EXISTS);
        }
        LeadsInfo leadsInfo = leadsInfoService.getById(leadsId);
        LeadsInfoExt leadsInfoExt = extService.getByLeadsInfoId(leadsId);
        AreaAddressModel addressBook = iAddressBookService.get(leadsId, AddressBookTypeEnum.LEADS_ADDRESS);
        if (Objects.isNull(leadsInfo) || Objects.isNull(leadsInfoExt) || Objects.isNull(addressBook)) {
            log.error("线索信息缺失，leadsId={}", leadsId);
            BizException.isFail(false, ErrorCode.LEADS_NOT_EXISTS);
        }
        return LeadsAllBO.of(leadsInfo, leadsInfoExt, addressBook);
    }


    /**
     * 默认匹配数量
     *
     * @param leadsAllBO
     * @return
     */
    protected MatchMerchantParamBO caseDefaultParam(LeadsAllBO leadsAllBO) {
        MatchMerchantParamBO paramBO = this.toParamBO(leadsAllBO);
        paramBO.setDistanceWithin(new BigDecimal(merchantFindCustomerConfig.getAbcdDistanceWithin()));
        paramBO.setSettleInStatuses(Set.of());
        paramBO.setExcludeSecondCategoryId(false);
        paramBO.setCount(merchantFindCustomerConfig.getAbcdCount());

        //如果是高空车/升降车就按一级品类匹配
        if (CollectionUtil.isNotEmpty(deviceConfig.getOnlyUseFirstCatIds()) && deviceConfig.getOnlyUseFirstCatIds().contains(paramBO.getFirstCategoryId())) {
            paramBO.setSecondCategoryId(null);
        }
        return paramBO;
    }

    /**
     * 情况一的商户数量
     *
     * @param leadsAllBO
     * @return
     */
    protected MatchMerchantParamBO caseAParam(LeadsAllBO leadsAllBO) {
        MatchMerchantParamBO paramBO = this.toParamBO(leadsAllBO);
        paramBO.setDistanceWithin(new BigDecimal(merchantFindCustomerConfig.getAbcdDistanceWithin()));
        paramBO.setSettleInStatuses(Set.of(MerchantStoreSettleInStatusEnum.SETTLE_IN.getCode()));
        paramBO.setExcludeSecondCategoryId(false);
        paramBO.setCount(merchantFindCustomerConfig.getAbcdCount());
        return paramBO;
    }

    /**
     * 情况二的商户数量
     *
     * @param leadsAllBO
     * @return
     */
    protected MatchMerchantParamBO caseBParam(LeadsAllBO leadsAllBO) {
        MatchMerchantParamBO paramBO = this.toParamBO(leadsAllBO);
        paramBO.setDistanceWithin(new BigDecimal(merchantFindCustomerConfig.getAbcdDistanceWithin()));
        paramBO.setSettleInStatuses(Set.of(MerchantStoreSettleInStatusEnum.NOT_SETTLE_IN.getCode(), MerchantStoreSettleInStatusEnum.WAIT_AUDIT.getCode(), MerchantStoreSettleInStatusEnum.IN_CLAIM_AUDIT.getCode(), MerchantStoreSettleInStatusEnum.SETTLE_IN_REJECT.getCode()));
        paramBO.setExcludeSecondCategoryId(false);
        paramBO.setCount(merchantFindCustomerConfig.getAbcdCount());
        return paramBO;
    }

    /**
     * 情况三的商户数量
     *
     * @param leadsAllBO
     * @return
     */
    protected MatchMerchantParamBO caseCParam(LeadsAllBO leadsAllBO) {
        MatchMerchantParamBO paramBO = this.toParamBO(leadsAllBO);
        paramBO.setDistanceWithin(new BigDecimal(merchantFindCustomerConfig.getAbcdDistanceWithin()));
        paramBO.setSettleInStatuses(Set.of(MerchantStoreSettleInStatusEnum.SETTLE_IN.getCode()));
        paramBO.setExcludeSecondCategoryId(true);
        paramBO.setCount(merchantFindCustomerConfig.getAbcdCount());
        return paramBO;
    }

    /**
     * 情况四的商户数量
     *
     * @param leadsAllBO
     * @return
     */
    protected MatchMerchantParamBO caseDParam(LeadsAllBO leadsAllBO) {
        MatchMerchantParamBO paramBO = this.toParamBO(leadsAllBO);
        paramBO.setDistanceWithin(new BigDecimal(merchantFindCustomerConfig.getAbcdDistanceWithin()));
        paramBO.setSettleInStatuses(Set.of(MerchantStoreSettleInStatusEnum.NOT_SETTLE_IN.getCode(), MerchantStoreSettleInStatusEnum.WAIT_AUDIT.getCode(), MerchantStoreSettleInStatusEnum.IN_CLAIM_AUDIT.getCode(), MerchantStoreSettleInStatusEnum.SETTLE_IN_REJECT.getCode()));
        paramBO.setExcludeSecondCategoryId(true);
        paramBO.setCount(merchantFindCustomerConfig.getAbcdCount());
        return paramBO;
    }

    /**
     * 情况五的商户数量
     *
     * @param leadsAllBO
     * @return
     */
    protected MatchMerchantParamBO caseEParam(LeadsAllBO leadsAllBO) {
        MatchMerchantParamBO paramBO = this.toParamBO(leadsAllBO);
        paramBO.setDistanceWithin(new BigDecimal(merchantFindCustomerConfig.getEDistanceWithin()));
        paramBO.setSettleInStatuses(Set.of(MerchantStoreSettleInStatusEnum.SETTLE_IN.getCode()));
        paramBO.setExcludeSecondCategoryId(true);
        paramBO.setCount(merchantFindCustomerConfig.getECount());
        return paramBO;
    }

    /**
     * 组装基本参数
     *
     * @param leadsAllBO
     * @return
     */
    MatchMerchantParamBO toParamBO(LeadsAllBO leadsAllBO) {
        LeadsInfo leadsInfo = leadsAllBO.getLeadsInfo();
        AreaAddressModel addressBook = leadsAllBO.getLeadsAddress();

        MatchMerchantParamBO paramBO = new MatchMerchantParamBO();
        paramBO.setLeadsId(leadsInfo.getId());
        paramBO.setFirstCategoryId(leadsInfo.getFirstCategoryId());
        paramBO.setSecondCategoryId(leadsInfo.getSecondCategoryId());
        paramBO.setTestFlag(leadsInfo.getTestFlag());
        paramBO.setLng(addressBook.getLng());
        paramBO.setLat(addressBook.getLat());
        return paramBO;
    }


    /**
     * 组装远程调用参数
     *
     * @param paramBO
     * @return
     */
    private RemoteMatchStorePageDTO toRemoteMatchStorePageDTO(MatchMerchantParamBO paramBO) {
        RemoteMatchStorePageDTO dto = new RemoteMatchStorePageDTO();
        dto.setFirstScopeId(paramBO.getFirstCategoryId());
        dto.setSecondScopeIds(LongUtils.isValid(paramBO.getSecondCategoryId()) && !Objects.equals(paramBO.getExcludeSecondCategoryId(), Boolean.TRUE) ? Set.of(paramBO.getSecondCategoryId()) : null);

        dto.setExcludeSecondScopeIds(LongUtils.isValid(paramBO.getSecondCategoryId()) && Objects.equals(paramBO.getExcludeSecondCategoryId(), Boolean.TRUE) ? Set.of(paramBO.getSecondCategoryId()) : null);
        dto.setSettleInStatuses(paramBO.getSettleInStatuses());
        dto.setTestFlag(paramBO.testFlag);
        dto.setSize(paramBO.getCount());

        RemoteMatchStorePageDTO.DistanceWithinModel distanceWithinModel = new RemoteMatchStorePageDTO.DistanceWithinModel();
        distanceWithinModel.setDistanceWithin(paramBO.getDistanceWithin());
        distanceWithinModel.setLat(paramBO.getLat());
        distanceWithinModel.setLng(paramBO.getLng());
        dto.setDistanceWithinModel(distanceWithinModel);
        return dto;
    }


    /**
     * 合并匹配门店,并日志埋点
     *
     * @param strategyName 策略名称
     * @param boList       各个case匹配到的商户
     * @return
     */
    protected List<MelinaStorePageVO> combineMatchStoreVO(Boolean isGray, Long leadsId, String strategyName, List<MatchMerchantResultBO> boList) {
        if (CollectionUtil.isEmpty(boList)) {
            return Collections.emptyList();
        }
        //埋点日志
        Map<String, Object> caseMap = Maps.newHashMap();
        //商户
        List<MelinaStorePageVO> voList = new ArrayList<>();
        for (MatchMerchantResultBO bo : boList) {
            //灰度策略需要加埋点
            caseMap.putIfAbsent(bo.getCaseName() + "_count", bo.getNeedCount());
            if (isGray) {
                caseMap.putIfAbsent(bo.getCaseName(), CollectionUtil.isEmpty(bo.getStoreList()) ? List.of() : bo.getStoreList().stream().map(MelinaStorePageVO::getId).toList());
            }

            List<MelinaStorePageVO> storeList = bo.getStoreList();
            if (CollectionUtil.isEmpty(storeList)) {
                continue;
            }
            //防NPE
            int needCount = Objects.isNull(bo.needCount) ? 0 : bo.needCount;
            //取大的，避免负数，防止越界
            needCount = Math.max(needCount, 0);
            //取小的那个，防止越界
            needCount = Math.min(needCount, storeList.size());

            voList.addAll(storeList.stream().limit(needCount).toList());
        }
        Collection<MelinaStorePageVO> uniqueList = voList.stream()
                .collect(Collectors.toMap(MelinaStorePageVO::getId, obj -> obj, (existing, replacement) -> existing, LinkedHashMap::new))
                .values();
        //埋点日志
        printMetricsLog(isGray, leadsId, strategyName, caseMap);
        return new ArrayList<>(uniqueList);
    }

    /**
     * 埋点日志
     *
     * @param isGray
     * @param leadsId
     * @param strategyName
     * @param caseMap
     */
    private void printMetricsLog(Boolean isGray, Long leadsId, String strategyName, Map<String, Object> caseMap) {
        //埋点日志
        Map<String, Object> logMap = Maps.newHashMap();
        logMap.put("isGray", isGray);
        logMap.put("leadsId", leadsId);
        logMap.put("strategyName", strategyName);
        logMap.put("caseMap", JsonUtils.toJson(caseMap));
        dataMetricsLogUtl.printMetricsLog("leadsMatchStore", "leads_match_store", logMap);
    }

    /**
     * 匹配结果
     */
    @NoArgsConstructor
    @AllArgsConstructor
    @Data
    protected static class MatchMerchantResultBO {

        /**
         * case名称
         */
        private String caseName;

        /**
         * 匹配到的数量
         */
        private Integer count;

        /**
         * 取多少数量，用于合并各个case的结果
         */
        private Integer needCount;

        /**
         * 门店数据
         */
        private List<MelinaStorePageVO> storeList;


        public static MatchMerchantResultBO of(Integer count, List<MelinaStorePageVO> storeList) {
            MatchMerchantResultBO bo = new MatchMerchantResultBO();
            bo.setCount(count);
            bo.setStoreList(storeList);
            return bo;
        }

        public MatchMerchantResultBO of(String caseName, Integer needCount) {
            this.setCaseName(caseName);
            this.setNeedCount(needCount);
            return this;
        }
    }

    /**
     * 匹配参数
     */
    @Data
    protected static class MatchMerchantParamBO {
        /**
         * 线索id
         */
        private Long leadsId;

        /**
         * 查询数量
         */
        private Integer count;

        /**
         * 一级分类
         */
        private Long firstCategoryId;

        /**
         * 二级分类
         */
        private Long secondCategoryId;

        /**
         * 是否过滤二级品类
         */
        private Boolean excludeSecondCategoryId;

        /**
         * 距离范围
         */
        private BigDecimal distanceWithin;

        /**
         * 线索纬度
         */
        private String lat;

        /**
         * 线索经度
         */
        private String lng;

        /**
         * 入驻状态
         *
         * @see MerchantStoreSettleInStatusEnum
         */
        private Set<Integer> settleInStatuses;


        /**
         * 测试标识:0-正常，1-测试
         */
        private Integer testFlag;

    }
}
