package com.yaowu.hera.domain.merchant.service.batis.service.impl;


import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.freedom.toolscommon.utils.StreamTools;
import com.yaowu.hera.domain.merchant.service.batis.mapper.StoreDetailVisitRecordMapper;
import com.yaowu.hera.domain.merchant.service.batis.service.IStoreDetailVisitRecordService;
import com.yaowu.hera.model.entity.merchant.StoreDetailVisitRecord;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.time.LocalDateTime;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <p>
 * 门店详情访问记录表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-06-27
 */
@Service
public class StoreDetailVisitRecordServiceImpl extends ServiceImpl<StoreDetailVisitRecordMapper, StoreDetailVisitRecord> implements IStoreDetailVisitRecordService {

    @Override
    public Integer getVisitCountByStartDay(Long gapDays, Long storeId) {
        if (storeId == null) {
            return 0;
        }
        LambdaQueryWrapper<StoreDetailVisitRecord> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(StoreDetailVisitRecord::getStoreId, storeId);
        if (Objects.nonNull(gapDays)){
            wrapper.ge(StoreDetailVisitRecord::getVisitTime, LocalDateTime.now().minusDays(gapDays));
        }
        wrapper.groupBy(StoreDetailVisitRecord::getOpenId);
        List<StoreDetailVisitRecord> list = list(wrapper);
        return CollectionUtils.isEmpty(list) ? 0 : list.size();
    }

    public Map<Long ,List<StoreDetailVisitRecord>> getStoreVisitRecordMap(Set<Long> storeIds,Long dayBeforeNow) {
        if (CollectionUtils.isEmpty(storeIds)){
            return new HashMap<>();
        }
        LambdaQueryWrapper<StoreDetailVisitRecord> wrapper = new LambdaQueryWrapper<>();
        wrapper.in(StoreDetailVisitRecord::getStoreId, storeIds);
        if (Objects.nonNull(dayBeforeNow)){
            wrapper.ge(StoreDetailVisitRecord::getVisitTime, LocalDateTime.now().minusDays(dayBeforeNow));
        }
        Map<Long, List<StoreDetailVisitRecord>> group = StreamTools.group(list(wrapper), StoreDetailVisitRecord::getStoreId, Function.identity());
        return  group.entrySet().stream()
                .collect(Collectors.toMap(
                        Map.Entry::getKey, // 保留原来的键
                        entry -> {
                            // 对于每个值列表，通过 openid 字段去重
                            Set<String> seenOpenIds = new HashSet<>();
                            return entry.getValue().stream()
                                    .filter(record -> seenOpenIds.add(record.getOpenId()))
                                    .collect(Collectors.toList());
                        }
                ));
    }

    @Override
    public Integer getVisitCountByStartDayAndLastDay(LocalDateTime startDay, LocalDateTime lastDay, Long storeId) {
        if (storeId == null || startDay == null || lastDay == null || startDay.isAfter(lastDay)) {
            return null;
        }
        LambdaQueryWrapper<StoreDetailVisitRecord> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(StoreDetailVisitRecord::getStoreId, storeId);
        wrapper.ge(StoreDetailVisitRecord::getVisitTime, startDay);
        wrapper.le(StoreDetailVisitRecord::getVisitTime, lastDay);
        wrapper.groupBy(StoreDetailVisitRecord::getOpenId);
        List<StoreDetailVisitRecord> list = list(wrapper);
        return CollectionUtils.isEmpty(list) ? 0 : list.size();
    }
}
