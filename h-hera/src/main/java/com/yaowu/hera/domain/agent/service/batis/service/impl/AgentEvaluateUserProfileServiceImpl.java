package com.yaowu.hera.domain.agent.service.batis.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.yaowu.hera.model.dto.agent.AgentEvaluateUserProfileQueryDTO;
import com.yaowu.hera.model.entity.agent.AgentEvaluateUserProfile;
import com.yaowu.hera.domain.agent.service.batis.mapper.AgentEvaluateUserProfileMapper;
import com.yaowu.hera.domain.agent.service.batis.service.IAgentEvaluateUserProfileService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.List;

/**
 * <p>
 * 智能体测评用户画像配置 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-09-18
 */
@Service
public class AgentEvaluateUserProfileServiceImpl extends ServiceImpl<AgentEvaluateUserProfileMapper, AgentEvaluateUserProfile> implements IAgentEvaluateUserProfileService {

    @Override
    public IPage<AgentEvaluateUserProfile> pageByCondition(AgentEvaluateUserProfileQueryDTO queryDTO) {
        LambdaQueryWrapper<AgentEvaluateUserProfile> queryWrapper = buildQueryWrapper(queryDTO);
        return page(queryDTO.pageRequest(), queryWrapper);
    }

    @Override
    public List<AgentEvaluateUserProfile> listByCondition(AgentEvaluateUserProfileQueryDTO queryDTO) {
        LambdaQueryWrapper<AgentEvaluateUserProfile> queryWrapper = buildQueryWrapper(queryDTO);
        if (queryWrapper.isEmptyOfWhere()) {
            return Collections.emptyList();
        }
        return list(queryWrapper);
    }

    private LambdaQueryWrapper<AgentEvaluateUserProfile> buildQueryWrapper(AgentEvaluateUserProfileQueryDTO queryDTO) {
        return Wrappers.lambdaQuery(AgentEvaluateUserProfile.class)
                .gt(queryDTO.getGtId() != null, AgentEvaluateUserProfile::getId, queryDTO.getGtId())
                .eq(queryDTO.getAgentAppId() != null, AgentEvaluateUserProfile::getAgentAppId, queryDTO.getAgentAppId())
                .last(queryDTO.getLimit() != null, "LIMIT " + queryDTO.getLimit())
                .orderByAsc(AgentEvaluateUserProfile::getId);
    }


}
