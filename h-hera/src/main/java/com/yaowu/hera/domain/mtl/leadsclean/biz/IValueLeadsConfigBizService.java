package com.yaowu.hera.domain.mtl.leadsclean.biz;

import com.yaowu.hera.model.dto.mtl.leadsclean.ValueLeadsConfigQueryDTO;
import com.yaowu.hera.model.dto.mtl.leadsclean.ValueLeadsConfigSaveDTO;
import com.yaowu.hera.model.vo.mtl.ValueLeadsConfigVO;

/**
 * 高价值线索基础配置业务接口
 */
public interface IValueLeadsConfigBizService {
    /**
     * 获取大单线索配置
     *
     * @return 大单线索配置
     */
    ValueLeadsConfigVO getValueLeadsConfig(ValueLeadsConfigQueryDTO innerDTO);

    /**
     * 保存大单线索配置
     *
     * @param configVO 大单线索配置
     */
    void saveValueLeadsConfig(ValueLeadsConfigSaveDTO configVO);
}