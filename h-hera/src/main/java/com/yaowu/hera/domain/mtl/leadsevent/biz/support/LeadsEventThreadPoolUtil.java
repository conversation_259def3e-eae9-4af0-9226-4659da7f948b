package com.yaowu.hera.domain.mtl.leadsevent.biz.support;

import com.yaowu.hera.enums.mtl.LeadEventDomainTypeEnum;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2024/12/4 9:26
 */
@Component
public class LeadsEventThreadPoolUtil {
    @Resource
    private ThreadPoolTaskExecutor submitLeadsInfoExecutor;
    @Resource
    private ThreadPoolTaskExecutor leadsMatchExecutor;
    @Resource
    private ThreadPoolTaskExecutor leadsOrderExecutor;

    public ThreadPoolTaskExecutor getThreadPoolExecutor(LeadEventDomainTypeEnum domainType) {
        return switch (domainType) {
            case LEAD_EXPRESSION -> submitLeadsInfoExecutor;
            case LEAD_CLEAN -> submitLeadsInfoExecutor;
            case LEAD_MATCH -> leadsMatchExecutor;
            case LEAD_ORDER -> leadsOrderExecutor;
        };
    }
}
