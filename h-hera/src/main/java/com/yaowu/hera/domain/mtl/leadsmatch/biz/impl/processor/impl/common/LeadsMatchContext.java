package com.yaowu.hera.domain.mtl.leadsmatch.biz.impl.processor.impl.common;

import com.yaowu.hera.enums.mtl.LeadsMerchantMatchRuleTypeEnum;
import com.yaowu.hera.model.bo.mtl.MerchantMatchResultBO;
import com.yaowu.hera.model.entity.leads.LeadsInfo;
import com.yaowu.hera.model.entity.leads.LeadsInfoExt;
import com.yaowu.heraapi.model.pojo.common.AreaAddressModel;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Set;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2024/11/29 15:06
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class LeadsMatchContext {
    @Schema(description = "线索信息")
    private LeadsInfo leadsInfo;

    @Schema(description = "线索扩展信息")
    private LeadsInfoExt leadsInfoExt;

    @Schema(description = "线索地址")
    private AreaAddressModel leadsAddress;

    @Schema(description = "灰度标识:0-非灰度，1-灰度")
    private Boolean isGray;

    @Schema(description = "已绑定的线索商户关系列表ID")
    private Set<Long> boundLeadsMerchantRelationIds;

    /**
     * see {@link LeadsMerchantMatchRuleTypeEnum}
     */
    @Schema(description = "策略名称")
    private String strategyType;

    @Schema(description = "CASE_A规则计算结果")
    private MerchantMatchResultBO caseAMatchResult;

    @Schema(description = "CASE_B规则计算结果")
    private MerchantMatchResultBO caseBMatchResult;

    @Schema(description = "CASE_C规则计算结果")
    private MerchantMatchResultBO caseCMatchResult;

    @Schema(description = "CASE_D规则计算结果")
    private MerchantMatchResultBO caseDMatchResult;

    @Schema(description = "是否为大单抽佣下发")
    private boolean isHighValueLeadsDispatch;
}
