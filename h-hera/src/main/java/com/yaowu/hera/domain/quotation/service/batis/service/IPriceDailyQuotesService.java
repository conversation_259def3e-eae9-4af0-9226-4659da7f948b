package com.yaowu.hera.domain.quotation.service.batis.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.yaowu.hera.model.bo.pricetool.PriceDailyQuotesBO;
import com.yaowu.hera.model.entity.price.PriceDailyQuotes;

import java.time.LocalDate;
import java.util.List;

/**
 * <p>
 * 文章数据表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-11-04
 */
public interface IPriceDailyQuotesService extends IService<PriceDailyQuotes> {


    List<PriceDailyQuotes> listByQuoteTime(PriceDailyQuotesBO priceDailyQuotesBO);

    List<PriceDailyQuotes> listByQuoteTime(LocalDate fromDate, LocalDate toDate);

    List<PriceDailyQuotes> listByCondition(List<String> quoteTimeList, List<Long> categoryIds, List<String> areaCodes);

    boolean deleteHistoryData(LocalDate startTime);
}
