package com.yaowu.hera.domain.ads.biz;

import com.freedom.web.model.resp.BasePage;
import com.yaowu.heraapi.model.dto.ads.*;
import com.yaowu.heraapi.model.vo.ads.RemoteAdsLoadReqVO;
import com.yaowu.heraapi.model.vo.ads.RemoteAdsTaskVO;

import java.util.List;

/**
 * @ClassName: IAdsTaskBizService
 * @Description:
 * @Author: YangKe
 * @Date: 2023/6/16 18:01
 **/
public interface IAdsTaskBizService {
    /**
     * 添加投放任务
     *
     * @param dto RemoteAddTaskDTO
     * @return 是否添加成功
     */
    Boolean add(RemoteAdsAddTaskDTO dto);

    /**
     * 编辑投放任务
     *
     * @param dto RemoteAddTaskDTO
     * @return 是否编辑成功
     */
    Boolean edit(RemoteAdsEditTaskDTO dto);

    /**
     * 下线投放任务
     *
     * @param id 投放任务id
     * @return 下线投放任务是否成功
     */
    Boolean offline(Long id);

    /**
     * 获取投放任务详情
     *
     * @param id 投放任务id
     * @return 投放任务详情
     */
    RemoteAdsTaskVO detail(Long id);

    /**
     * 获取投放任务分页数据
     *
     * @param dto 资源位投放任务分页参数
     * @return 投放任务分页数据
     */
    BasePage<RemoteAdsTaskVO> page(RemoteAdsTaskPageDTO dto);

    /**
     * 获取投放的素材内容
     *
     * @param dto 资源位内容请求的DTO
     * @return 匹配到的投放的素材内容
     */
    RemoteAdsLoadReqVO load(RemoteAdsLoadReqDTO dto);

    /**
     * 更新投放任务的状态
     */
    void updateAllAdsTaskState();

    /**
     * 初始化投放任务信息
     */
    void initAdsTaskBaseInfo();

    /**
     * 切换投放任务优先级
     */
    Boolean switchAdsTaskPriority(RemoteSwitchAdsTaskPriorityDTO dto);



    List<RemoteAdsLoadReqVO> loadByPlacementIdsAndConditions(RemoteAdsBatchLoadReqDTO dto);
}
