package com.yaowu.hera.domain.advisory.service.batis.service.impl;

import com.yaowu.hera.model.entity.advisory.AdvisoryRecord;
import com.yaowu.hera.domain.advisory.service.batis.mapper.AdvisoryRecordMapper;
import com.yaowu.hera.domain.advisory.service.batis.service.IAdvisoryRecordService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;

/**
 * <p>
 * 咨询记录表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-10-19
 */
@Service
public class AdvisoryRecordServiceImpl extends ServiceImpl<AdvisoryRecordMapper, AdvisoryRecord> implements IAdvisoryRecordService {

}
