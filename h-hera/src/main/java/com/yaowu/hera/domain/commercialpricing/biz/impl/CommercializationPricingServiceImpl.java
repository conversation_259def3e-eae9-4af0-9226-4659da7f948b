package com.yaowu.hera.domain.commercialpricing.biz.impl;

import cn.hutool.core.date.DateUtil;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.freedom.toolscommon.utils.StreamTools;
import com.freedom.web.exception.BusinessException;
import com.freedom.web.model.resp.BasePage;
import com.yaowu.hera.config.nacos.CommercializationPricingConfig;
import com.yaowu.hera.domain.clue.biz.IDelayedMessageService;
import com.yaowu.hera.domain.commercialpricing.biz.ICommercializationPricingService;
import com.yaowu.hera.domain.commercialpricing.service.batis.service.ILeadsPricingConfigBatchService;
import com.yaowu.hera.domain.commercialpricing.service.batis.service.ILeadsPricingConfigService;
import com.yaowu.hera.domain.common.biz.ICodeBizService;
import com.yaowu.hera.domain.common.biz.WarnNoticeBizService;
import com.yaowu.hera.domain.feign.KratosFeignBizService;
import com.yaowu.hera.domain.leads.service.batis.service.ILeadsDeviceCategoryService;
import com.yaowu.hera.enums.common.EnableStatusEnum;
import com.yaowu.hera.model.BaseEvent;
import com.yaowu.hera.model.bo.commercialization.pricing.CommercializationPricingQueryBO;
import com.yaowu.hera.model.bo.delaymessage.PricingConfigEffectiveMessage;
import com.yaowu.hera.model.bo.leads.LeadsDeviceConditionBO;
import com.yaowu.hera.model.entity.commercialization.LeadsPricingConfig;
import com.yaowu.hera.model.entity.commercialization.LeadsPricingConfigBatch;
import com.yaowu.hera.model.entity.leads.LeadsDeviceCategory;
import com.yaowu.hera.model.pojo.PricingImportCheckItemModel;
import com.yaowu.hera.model.vo.common.AreaTreeVO;
import com.yaowu.hera.remote.thirdparty.IThirdPartyRemoteService;
import com.yaowu.hera.utils.mapstruct.commercialization.pricing.CommercializationPricingMapstruct;
import com.yaowu.heraapi.model.dto.commercialization.pricing.RemoteCommercialPricingBatchCancelDTO;
import com.yaowu.heraapi.model.dto.commercialization.pricing.RemoteCommercialPricingBatchPageDTO;
import com.yaowu.heraapi.model.dto.commercialization.pricing.RemoteCommercialPricingImportDTO;
import com.yaowu.heraapi.model.dto.commercialization.pricing.RemoteCommercialPricingPageDTO;
import com.yaowu.heraapi.model.pojo.commercialization.pricing.RemotePricingConfigImportData;
import com.yaowu.heraapi.model.vo.commercialization.pricing.*;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.event.EventListener;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.time.Duration;
import java.time.LocalDateTime;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.yaowu.hera.utils.constants.Constants.NEW_CUSTOMER_APPLETS;

@Slf4j
@Service
public class CommercializationPricingServiceImpl implements ICommercializationPricingService {

    @Resource
    private ILeadsPricingConfigService leadsPricingConfigService;

    @Resource
    private ILeadsPricingConfigBatchService leadsPricingConfigBatchService;

    @Resource
    private KratosFeignBizService kratosFeignBizService;

    @Resource
    private IThirdPartyRemoteService thirdPartyRemoteService;

    @Resource
    private IDelayedMessageService<PricingConfigEffectiveMessage> delayedMessageService;

    @Resource
    private WarnNoticeBizService noticeBizService;

    @Resource
    private CommercializationPricingConfig commercializationPricingConfig;

    @Resource
    private ILeadsDeviceCategoryService leadsDeviceCategoryService;

    @Resource
    private ICodeBizService iCodeBizService;

    @Transactional(rollbackFor = Exception.class)
    @Override
    public RemoteCommercializationPricingImportResultVO importBatch(RemoteCommercialPricingImportDTO dto) {
        RemoteCommercializationPricingImportResultVO resultVO = RemoteCommercializationPricingImportResultVO.builder().success(false).build();
        //校验数据
        List<RemoteCommercialPricingImportErrorVO> remoteCommercialPricingImportErrorVOS = checkAndHandelImportData(dto.getData());
        if (CollectionUtils.isNotEmpty(remoteCommercialPricingImportErrorVOS)) {
            resultVO.setErrors(remoteCommercialPricingImportErrorVOS);
            return resultVO;
        }
        //校验通过时创建批次
        LeadsPricingConfigBatch leadsPricingConfigBatch = CommercializationPricingMapstruct.INSTANCE.toLeadsPricingConfigBatch(dto, iCodeBizService.generatePricingBatchCode());
        Long batchId = leadsPricingConfigBatchService.saveAndGetId(leadsPricingConfigBatch);
        //加入延迟队列，发送企业微信消息
        batchEffectiveNotice(leadsPricingConfigBatch);
        //校验通过时插入数据
        List<LeadsPricingConfig> configs = StreamTools.toList(dto.getData(), data -> CommercializationPricingMapstruct.INSTANCE.toLeadsPricingConfig(data, batchId));
        leadsPricingConfigService.saveBatch(configs);
        resultVO.setSuccess(true);
        return resultVO;
    }


    public void batchEffectiveNotice(LeadsPricingConfigBatch leadsPricingConfigBatch) {
        if (Objects.isNull(leadsPricingConfigBatch) || leadsPricingConfigBatch.getEffectiveStartTime().isBefore(LocalDateTime.now())) {
            return;
        }
        PricingConfigEffectiveMessage message = new PricingConfigEffectiveMessage();
        message.setImportTime(LocalDateTime.now());
        message.setBatchCode(leadsPricingConfigBatch.getBatchCode());
        message.setBatchId(leadsPricingConfigBatch.getId());
        long seconds = Duration.between(LocalDateTime.now(), leadsPricingConfigBatch.getEffectiveStartTime()).toSeconds();
        log.info("batchEffectiveNotice to pushMessage message = {}", JSON.toJSON(message));
        delayedMessageService.pushMessage(seconds, TimeUnit.SECONDS, message);
    }

    @Override
    public List<LeadsPricingConfig> getCurrentNewestEffectiveLeadsPricingConfigListByCityCode(String cityCode) {
        Long newestBatchIdLeQueryEffectiveStartTime = leadsPricingConfigBatchService.getNewestBatchIdLeQueryEffectiveStartTime(LocalDateTime.now());
        Set<String> cityCodeSet = new HashSet<>();
        cityCodeSet.add(cityCode);
        //加入兜底城市
        cityCodeSet.add(commercializationPricingConfig.getNationalCode());
        CommercializationPricingQueryBO bo = CommercializationPricingQueryBO.builder()
                .batchId(newestBatchIdLeQueryEffectiveStartTime).cityCodeList(cityCodeSet).build();
        return leadsPricingConfigService.listByCondition(bo);
    }


    @EventListener(condition = "#event.getData() != null")
    public void batchEffectiveDelayMessages(BaseEvent<PricingConfigEffectiveMessage> event) {
        PricingConfigEffectiveMessage messages = event.getData();
        if (Objects.isNull(messages)) {
            return;
        }
        LeadsPricingConfigBatch byId = leadsPricingConfigBatchService.getById(messages.getBatchId());
        if (Objects.isNull(byId) || !Objects.equals(byId.getBatchEnableStatus(), EnableStatusEnum.ENABLED.getCode())) {
            return;
        }
        log.info("processDelayedMessages  messages = {}", JSON.toJSON(messages));
        String msg = getBatchEffectiveMessage(messages);

        String botKey = commercializationPricingConfig.getBatchEffectiveNoticeKey();
        List<String> phoneList = commercializationPricingConfig.getBatchEffectiveNoticePhone();

        log.info("processMessages to sendNoticeToWechat msg = {}", msg);
        noticeBizService.sendNoticeToWechat(msg, botKey, phoneList);
    }


    private static String getBatchEffectiveMessage(PricingConfigEffectiveMessage message) {
        StringBuilder messageString = new StringBuilder();
        return messageString.append("新价格配置更新 \n")
                .append("您于").append(message.getImportTime() == null ? "[error]" : DateUtil.format(message.getImportTime(), "yyyy-MM-dd HH:mm:ss")).append("导入的新配置已生效\n")
                .append("批次编号：").append(message.getBatchCode()).append("\n")
                .toString();
    }

    /**
     * 校验和处理导入数据
     * @param data
     * @return
     */
    private List<RemoteCommercialPricingImportErrorVO> checkAndHandelImportData(List<RemotePricingConfigImportData> data) {
        List<RemoteCommercialPricingImportErrorVO> errors = new ArrayList<>();
        if (CollectionUtils.isEmpty(data)) {
            RemoteCommercialPricingImportErrorVO error = RemoteCommercialPricingImportErrorVO.builder().errorMsg("数据为空").lineNum(0).build();
            errors.add(error);
            return errors;
        }
        List<LeadsDeviceCategory> leadsDeviceCategories = leadsDeviceCategoryService.listByCondition(
                LeadsDeviceConditionBO.builder().categoryIds(StreamTools.toSet(data,
                        RemotePricingConfigImportData::getSecondCategoryId)).channel(NEW_CUSTOMER_APPLETS).build());
        Map<Long, LeadsDeviceCategory> secondCatIdMap = StreamTools.toMap(leadsDeviceCategories, LeadsDeviceCategory::getCategoryId, Function.identity());
        PricingImportCheckItemModel checkItemModel = PricingImportCheckItemModel.builder()
                .cityFirstCatSecondCatMultipleKey(new HashMap<>()).build();
        for (int i = 0; i < data.size(); i++) {
            RemotePricingConfigImportData remotePricingConfigImportData = data.get(i);
            RemoteCommercialPricingImportErrorVO remoteCommercialPricingImportErrorVO = checkSingleLine(remotePricingConfigImportData, i + 1, checkItemModel, secondCatIdMap);
            handelSingleImportData(remotePricingConfigImportData, i + 1);
            if (Objects.isNull(remoteCommercialPricingImportErrorVO)) {
                continue;
            }
            errors.add(remoteCommercialPricingImportErrorVO);
        }
        return errors;
    }

    private void handelSingleImportData(RemotePricingConfigImportData data, Integer dataLineNum) {
        if (dataLineNum == 1) {
            data.setCityCode(commercializationPricingConfig.getNationalCode());
            data.setCityName("全国");
            data.setFirstCategoryId(null);
            data.setSecondCategoryId(null);
        }

    }

    private RemoteCommercialPricingImportErrorVO checkSingleLine(RemotePricingConfigImportData data,
                                                                 Integer dataLineNum,
                                                                 PricingImportCheckItemModel checkItemModel,
                                                                 Map<Long, LeadsDeviceCategory> secondCatIdMap) {
        StringBuffer errorMsg = new StringBuffer();
        //检查城市
        checkCity(data, errorMsg, dataLineNum);
        //检查价格
        checkPriceValue(data, errorMsg);
        //检查购买上限
        checkPurchaseMaxCount(data, errorMsg);
        //检查品类
        checkCategory(data, errorMsg, checkItemModel, dataLineNum,secondCatIdMap);
        if (StringUtils.isBlank(errorMsg)) {
            return null;
        }
        return RemoteCommercialPricingImportErrorVO.builder().lineNum(dataLineNum).errorMsg(errorMsg.toString()).build();
    }

    private void checkCity(RemotePricingConfigImportData data, StringBuffer errorMsg, Integer lineNum) {
        //检查输入的城市是不是为空--第一行特判
        if (lineNum > 1 && StringUtils.isBlank(data.getCityName())) {
            buildErrorMsg(errorMsg, "城市不能为空");
        }
        //检查对应的code是否为空--第一行特判
        if (lineNum > 1 && StringUtils.isBlank(data.getCityCode())) {
            buildErrorMsg(errorMsg, "找不到对应城市");
        }
    }

    private void checkPriceValue(RemotePricingConfigImportData data, StringBuffer errorMsg) {
        BigDecimal standardPrice = null;
        BigDecimal discountedPrice = null;
        BigDecimal findMerchantPrice = null;
        if (StringUtils.isBlank(data.getStandardPrice())) {
            buildErrorMsg(errorMsg, "标准价不能为空");
        }
        if (StringUtils.isBlank(data.getDiscountedPrice())) {
            buildErrorMsg(errorMsg, "折扣价不能为空");
        }
        //标准价、折扣价 数字
        try {
            standardPrice = new BigDecimal(data.getStandardPrice());
        } catch (Exception e) {
            buildErrorMsg(errorMsg, "标准价必须为数字");
        }
        try {
            discountedPrice = new BigDecimal(data.getDiscountedPrice());
        } catch (Exception e) {
            buildErrorMsg(errorMsg, "折扣价必须为数字");
        }
        try {
            findMerchantPrice = new BigDecimal(data.getC2bPrice());
        } catch (Exception e) {
            buildErrorMsg(errorMsg, "客找商定价必须为数字");
        }
        if (standardPrice == null || discountedPrice == null) {
            return;
        }

        // 需大于等于0
        if (standardPrice.compareTo(BigDecimal.ZERO) < 0) {
            buildErrorMsg(errorMsg, "标准价必须大于等于0");
        }
        if (discountedPrice.compareTo(BigDecimal.ZERO) < 0) {
            buildErrorMsg(errorMsg, "折扣价必须大于等于0");
        }
        if (findMerchantPrice == null || findMerchantPrice.compareTo(BigDecimal.ZERO) < 0) {
            buildErrorMsg(errorMsg, "客找商定价必须大于等于0");
        }
        // 标准价必须小于等于100
        BigDecimal hundred = new BigDecimal(commercializationPricingConfig.getMaxStandardPriceConfig());
        if (standardPrice.compareTo(hundred) > 0) {
            buildErrorMsg(errorMsg, "标准价必须小于等于100");
        }
        // 标准价必须大于等于折扣价
        if (standardPrice.compareTo(discountedPrice) < 0) {
            buildErrorMsg(errorMsg, "标准价必须大于等于折扣价");
        }
    }

    private void checkPurchaseMaxCount(RemotePricingConfigImportData data, StringBuffer errorMsg) {
        Integer purchaseMaxNum = null;
        if (StringUtils.isBlank(data.getPurchaseMaxNum())){
            buildErrorMsg(errorMsg, "购买上限不能为空");
            return;
        }
        //需＞0 的数字
        try {
            purchaseMaxNum = Integer.parseInt(data.getPurchaseMaxNum());
        } catch (Exception e) {
            buildErrorMsg(errorMsg, "购买上限必须为数字");
            return;
        }
        if (purchaseMaxNum <= 0) {
            buildErrorMsg(errorMsg, "购买上限必须大于0");
        }
        if (purchaseMaxNum > commercializationPricingConfig.getPurchaseMaxNumConfig()) {
            buildErrorMsg(errorMsg, "购买上限不能超过10");
        }

    }


    private void checkCategory(RemotePricingConfigImportData data,
                               StringBuffer errorMsg,
                               PricingImportCheckItemModel checkItemModel,
                               Integer lineNum,
                               Map<Long, LeadsDeviceCategory> secondCatIdMap) {
        if (lineNum == 1) {
            return;
        }
        //输入品类不能为空，以及必须在数据库存在
        if (StringUtils.isBlank(data.getFirstCategoryName())) {
            buildErrorMsg(errorMsg, "一级品类不能为空");
        } else if (Objects.isNull(data.getFirstCategoryId())) {
            buildErrorMsg(errorMsg, "一级品类不存在");
        }
        if (StringUtils.isBlank(data.getSecondCategoryName())) {
            buildErrorMsg(errorMsg, "二级品类不能为空");
        } else if (Objects.isNull(data.getSecondCategoryId())) {
            buildErrorMsg(errorMsg, "二级品类不存在");
        }
        if (Objects.nonNull(data.getFirstCategoryId()) && Objects.nonNull(data.getSecondCategoryId()) && Objects.nonNull(data.getCityCode())) {
            LeadsDeviceCategory secondCategory = secondCatIdMap.get(data.getSecondCategoryId());
            if (!Objects.equals(secondCategory == null ? null : secondCategory.getCategoryParentId(), data.getFirstCategoryId())) {
                buildErrorMsg(errorMsg, "二级品类与一级品类不匹配");
            }
            Map<String, Integer> cityFirstCatSecondCatMultipleKey = checkItemModel.getCityFirstCatSecondCatMultipleKey();
            String key = data.getCityCode() + "_" + data.getFirstCategoryId() + "_" + data.getSecondCategoryId();
            if (cityFirstCatSecondCatMultipleKey.containsKey(key)) {
                buildErrorMsg(errorMsg, "该城市对应一级、二级品类定价和第" + cityFirstCatSecondCatMultipleKey.get(key) + "行数据重复");
            } else {
                cityFirstCatSecondCatMultipleKey.put(key, lineNum + 1);
            }
        }

    }


    private void buildErrorMsg(StringBuffer totalErrorMsg, String msg) {
        if (StringUtils.isBlank(msg)) {
            return;
        }
        if (StringUtils.isBlank(totalErrorMsg)) {
            totalErrorMsg.append(msg);
        } else {
            totalErrorMsg.append(";").append(msg);
        }

    }

    @Override
    public BasePage<RemoteCommercialPricingPageVO> pagePricingConfig(RemoteCommercialPricingPageDTO dto) {
        //处理入参--城市，生效时间
        if (handelRemoteRemoteCommercialPricingPageDTO(dto)) {
            return new BasePage<>();
        }
        //分页查询
        Page<LeadsPricingConfig> leadsPricingConfigPage = leadsPricingConfigService.pageLeadsPricingConfig(dto);
        //获得这些配置的所有一级、二级品类信息，后续自行组装
        Map<Long, LeadsDeviceCategory> catIdMapByPricingConfigs = getCatIdMapByPricingConfigs(leadsPricingConfigPage.getRecords());
        return BasePage.simpleConvert(leadsPricingConfigPage, vo ->
                CommercializationPricingMapstruct.INSTANCE.toRemoteCommercialPricingPageVO(vo, catIdMapByPricingConfigs));
    }

    /**
     * 通过定价配置获得这些配置里所有品类信息map，一二级
     * @param configs
     * @return map ：key：品类id，value：RemoteDeviceCategoryDetailVO
     */
    private Map<Long, LeadsDeviceCategory> getCatIdMapByPricingConfigs(List<LeadsPricingConfig> configs) {
        Set<Long> catIds = new HashSet<>();
        catIds.addAll(StreamTools.toSet(configs, LeadsPricingConfig::getFirstCategoryId));
        catIds.addAll(StreamTools.toSet(configs, LeadsPricingConfig::getSecondCategoryId));
        if (CollectionUtils.isEmpty(catIds)) {
            return new HashMap<>();
        }
        List<LeadsDeviceCategory> leadsDeviceCategories =
                leadsDeviceCategoryService.listByCondition(LeadsDeviceConditionBO.builder().channel(NEW_CUSTOMER_APPLETS).categoryIds(catIds).build());
        return StreamTools.toMap(leadsDeviceCategories, LeadsDeviceCategory::getCategoryId, Function.identity());
    }

    /**
     * 处理入参--返回是否继续查询，true：不继续查询，false：继续查询
     * @param dto
     * @return
     */
    private boolean handelRemoteRemoteCommercialPricingPageDTO(RemoteCommercialPricingPageDTO dto) {
        boolean handelBatchIdInPricingPage = handelBatchIdInPricingPage(dto);
        boolean handelCityCodeInPricingPage = handelCityCodeInPricingPage(dto);
        return !handelCityCodeInPricingPage || !handelBatchIdInPricingPage;
    }

    private boolean handelCityCodeInPricingPage(RemoteCommercialPricingPageDTO dto) {
        List<AreaTreeVO> areaTreeVOS = thirdPartyRemoteService.areaTree();

        if (CollectionUtils.isEmpty(dto.getAreaCodes())) {
            return true;
        }
        //todo 作为常量
        //所有省
        Set<AreaTreeVO> provinceAreas = areaTreeVOS.stream().filter(areaTreeVO -> dto.getAreaCodes().contains(areaTreeVO.getCode())).collect(Collectors.toSet());
        //所有市
        Set<AreaTreeVO> cityAreas = areaTreeVOS.stream()
                .flatMap(areaTreeVO -> areaTreeVO.getChildren().stream())
                .collect(Collectors.toSet());
        Set<String> provinceAreasCodes = StreamTools.toSet(provinceAreas, AreaTreeVO::getCode);
        Set<String> cityCodeList = new HashSet<>(StreamTools.toSet(
                StreamTools.filterToList(cityAreas, c -> dto.getAreaCodes().contains(c.getCode())
                        || provinceAreasCodes.contains(c.getParentCode()))
                , AreaTreeVO::getCode));
        //全国配置
        cityCodeList.add(commercializationPricingConfig.getNationalCode());
        dto.setCityCodeList(cityCodeList);
        return true;
    }

    /**
     * 根据分野入参的查询时间处理批次id
     * @param dto
     * @return
     */
    private boolean handelBatchIdInPricingPage(RemoteCommercialPricingPageDTO dto) {
        Long newestBatchIdLeQueryEffectiveStartTime = leadsPricingConfigBatchService.getNewestBatchIdLeQueryEffectiveStartTime(dto.getEffectiveStartTimeLe());
        if (dto.getEffectiveStartTimeLe() == null && newestBatchIdLeQueryEffectiveStartTime == null) {
            return false;
        }
        dto.setBatchId(newestBatchIdLeQueryEffectiveStartTime);
        return true;

    }

    @Override
    public BasePage<RemoteCommercialPricingBatchPageVO> queryToEffective(RemoteCommercialPricingBatchPageDTO dto) {
        //查询生效时间大于查询时间的批次
        Page<LeadsPricingConfigBatch> leadsPricingConfigBatchPage = leadsPricingConfigBatchService.pageLeadsPricingConfigBatch(dto);
        return BasePage.simpleConvert(leadsPricingConfigBatchPage, CommercializationPricingMapstruct.INSTANCE::toRemoteCommercialPricingBatchPageVO);
    }

    @Override
    public Boolean ineffectiveBatch(RemoteCommercialPricingBatchCancelDTO dto) {
        //禁用批次
        LeadsPricingConfigBatch leadsPricingConfigBatch = leadsPricingConfigBatchService.checkExist(dto.getBatchId());
        BusinessException.condition(leadsPricingConfigBatch.getEffectiveStartTime().isBefore(LocalDateTime.now()), "该批次已失效，无法禁用");
        leadsPricingConfigBatch.setBatchEnableStatus(EnableStatusEnum.DISABLED.getCode());
        return leadsPricingConfigBatchService.updateById(leadsPricingConfigBatch);
    }

    @Override
    public List<RemoteCommercialPricingExportListVO> export(RemoteCommercialPricingPageDTO dto) {
        //处理入参--城市，生效时间
        if (handelRemoteRemoteCommercialPricingPageDTO(dto)) {
            return new ArrayList<>();
        }
        List<LeadsPricingConfig> leadsPricingConfigs = leadsPricingConfigService.listByCondition(CommercializationPricingQueryBO.builder()
                .batchId(dto.getBatchId())
                        .categoryIds(dto.getCategoryIds())
                .cityCodeList(dto.getCityCodeList()).build());

        //获得这些配置的所有一级、二级品类信息，后续自行组装
        Map<Long, LeadsDeviceCategory> catIdMapByPricingConfigs = getCatIdMapByPricingConfigs(leadsPricingConfigs);
        return StreamTools.toList(leadsPricingConfigs, vo -> CommercializationPricingMapstruct.INSTANCE.toRemoteCommercialPricingExportListVO(vo, catIdMapByPricingConfigs));
    }

    @Override
    public List<LeadsPricingConfig> getCurrentNewestEffectiveLeadsPricingConfig(LocalDateTime time) {
        time = time == null ? LocalDateTime.now() : time;
        Long newestBatchIdLeQueryEffectiveStartTime = leadsPricingConfigBatchService.getNewestBatchIdLeQueryEffectiveStartTime(time);
        return leadsPricingConfigService.listByCondition(
                CommercializationPricingQueryBO.builder().batchId(newestBatchIdLeQueryEffectiveStartTime).build());
    }

    @Override
    public LeadsPricingConfig getLatestEffectiveLeadsPricingConfig(Long firstCategoryId, Long secondCategoryId, String cityCode) {
        Long latestBatchId = leadsPricingConfigBatchService.getNewestBatchIdLeQueryEffectiveStartTime(LocalDateTime.now());
        return leadsPricingConfigService.getLatestEffectiveLeadsPricingConfig(latestBatchId,firstCategoryId, secondCategoryId ,cityCode);
    }
}
