package com.yaowu.hera.domain.leads.service.batis.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.yaowu.hera.model.bo.leads.LeadsDeviceConditionBO;
import com.yaowu.hera.model.entity.leads.LeadsDeviceCategory;
import com.yaowu.hera.model.entity.leads.LeadsInfo;
import com.yaowu.heraapi.model.dto.mtl.pricing.localrentprice.RemoteQueryRentalPageDTO;
import jakarta.annotation.Nullable;

import java.util.Collection;
import java.util.List;
import java.util.Map;
import java.util.Set;

public interface ILeadsDeviceCategoryService extends IService<LeadsDeviceCategory> {


    List<LeadsDeviceCategory> listByCondition(LeadsDeviceConditionBO deviceConditionBO);
    LeadsDeviceCategory getOneByCondition(LeadsDeviceConditionBO deviceConditionBO);

    LeadsDeviceCategory getCategoryByLocationTypeAndCatId(int locationType, Long categoryId, String channel);

    /**
     * 根据品类id和渠道获取品类
     * @param categoryId
     * @param channel
     * @return
     */
    LeadsDeviceCategory getAllOrHomePageCategoryByCatIdAndChannel(Long categoryId, String channel);

    /**
     * 根据品类id和渠道获取品类
     * @param categoryId
     * @param channel
     * @return
     */
    List<LeadsDeviceCategory> getAllCategoryByCatIdAndChannel(Long categoryId, String channel);


    List<LeadsDeviceCategory> searchSecondCategorySearch(Set<String> words);

    /**
     * 根据二级品类查
     * @param secondCategoryName
     * @return
     */
    @Nullable
    LeadsDeviceCategory  getCategoryByName( String secondCategoryName);


    /**
     * 根据渠道类型和二级品类id集合获取品类map
     * map的key为品类id
     * @param channelType
     * @param categoryIds
     * @return
     */
    Map<Long, LeadsDeviceCategory> getCategoryMapByTypeAndCategoryIds(String channelType, Collection<Long> categoryIds);

    Map<Long, String> getCategoryNameMapByCategoryId(Collection<Long> categoryIds);


    Map<Long, LeadsDeviceCategory> getCategoryMapByLeadsWithChannel(String channel,
                                                                           Collection<LeadsInfo> leadsInfoList);


    Set<Long> getAllCategoryIdsByLeadsList(Collection<LeadsInfo> leadsInfoList);

    Page<LeadsDeviceCategory> pageByRentalCondition(RemoteQueryRentalPageDTO dto,Set<Long> categoryIds);
}
