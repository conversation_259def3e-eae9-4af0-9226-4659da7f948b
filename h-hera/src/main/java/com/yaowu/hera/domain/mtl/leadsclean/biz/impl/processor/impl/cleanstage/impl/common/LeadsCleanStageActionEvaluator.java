package com.yaowu.hera.domain.mtl.leadsclean.biz.impl.processor.impl.cleanstage.impl.common;

import com.yaowu.hera.domain.mtl.leadsclean.biz.impl.processor.impl.cleanstage.IAsyncLeadsCleanStage;
import com.yaowu.hera.domain.mtl.leadsclean.biz.impl.processor.impl.cleanstage.ILeadsCleanStage;
import com.yaowu.hera.enums.mtl.LeadsCleanStageActionEnum;
import com.yaowu.hera.model.dto.mtl.leadsclean.LeadsAsyncStageCleanRequestDTO;
import com.yaowu.hera.model.dto.mtl.leadsclean.LeadsStageCleanRequestDTO;
import com.yaowu.hera.utils.TypeCache;
import org.springframework.stereotype.Component;

import java.lang.reflect.Type;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2025/1/2 17:36
 */
@Component
public class LeadsCleanStageActionEvaluator {

    public LeadsCleanStageActionEnum evaluate(ILeadsCleanStage leadsCleanStage, LeadsStageCleanRequestDTO cleanRequest) {
        if (leadsCleanStage instanceof IAsyncLeadsCleanStage asyncStage
                && cleanRequest instanceof LeadsAsyncStageCleanRequestDTO asyncCleanRequest) {
            if (isCompatibleTimeoutFallback(asyncStage, asyncCleanRequest.getSubmitResult())) {
                return LeadsCleanStageActionEnum.ASYNC_STAGE_TIMEOUT_HANDLE;
            }
            if (isCompatibleCallback(asyncStage, asyncCleanRequest.getCallbackResult())) {
                return LeadsCleanStageActionEnum.ASYNC_STAGE_CALLBACK_HANDLE;
            }
        }
        return LeadsCleanStageActionEnum.SYNC_STAGE_CLEAN_HANDLE;
    }

    public <T> boolean isCompatibleTimeoutFallback(IAsyncLeadsCleanStage asyncStage, T timeoutRequest) {
        if (timeoutRequest == null) {
            return false;
        }
        Type[] typeArguments = TypeCache.getActualTypeArguments(asyncStage.getClass());
        Class<?> callbackClass = (Class<?>) typeArguments[0];
        return callbackClass.isAssignableFrom(timeoutRequest.getClass()) ||
                callbackClass.isInstance(timeoutRequest);
    }

    /**
     * 判断是否异步回调结果清洗
     *
     * @param asyncStage
     * @param callbackResult
     * @param <C>
     * @return
     */
    public <C> boolean isCompatibleCallback(IAsyncLeadsCleanStage asyncStage, C callbackResult) {
        if (callbackResult == null) {
            return false;
        }
        Type[] typeArguments = TypeCache.getActualTypeArguments(asyncStage.getClass());
        Class<?> callbackClass = (Class<?>) typeArguments[1];
        return callbackClass.isAssignableFrom(callbackResult.getClass()) ||
                callbackClass.isInstance(callbackResult);
    }
}
