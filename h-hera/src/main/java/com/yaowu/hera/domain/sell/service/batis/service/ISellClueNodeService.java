package com.yaowu.hera.domain.sell.service.batis.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.yaowu.hera.model.bo.sell.SellClueNodeBO;
import com.yaowu.hera.model.entity.sell.SellClueNode;

import java.util.Collection;
import java.util.List;
import java.util.Map;

/**
 * <p>
 * 推荐线索节点表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-11-22
 */
public interface ISellClueNodeService extends IService<SellClueNode> {

    /**
     * 添加节点
     *
     * @param sellClueNodeBO
     */
    void addNode(SellClueNodeBO sellClueNodeBO);

    /**
     * 根据推荐线索id获取节点列表
     *
     * @param sellClueIds 推荐线索id
     * @return 节点列表
     */
    Map<Long, List<SellClueNode>> getNodeMap(Collection<Long> sellClueIds);

    /**
     * 根据推荐线索id获取节点列表
     *
     * @param sellClueId
     * @return
     */
    List<SellClueNode> getNodes(Long sellClueId);

    /**
     * 是否存在节点
     *
     * @param sellClueId
     * @param nodeCode
     * @return
     */
    boolean existNode(Long sellClueId, Integer nodeCode);

    /**
     * 删除节点
     *
     * @param sellClueId
     * @param nodeCodes
     * @return
     */
    void delNode(Long sellClueId, List<Integer> nodeCodes);
}
