package com.yaowu.hera.domain.sticker.biz.impl;

import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.yaowu.hera.config.nacos.CarStickerConfig;
import com.yaowu.hera.domain.common.feign.DwzServiceFeign;
import com.yaowu.hera.domain.favor.biz.IFavorGrantBizService;
import com.yaowu.hera.domain.favor.biz.IFavorStockInfoBizService;
import com.yaowu.hera.domain.favor.biz.IFavorUserRelationBizService;
import com.yaowu.hera.domain.sticker.biz.IStickStockBizService;
import com.yaowu.hera.domain.sticker.service.batis.service.IStickerPartnerInfoService;
import com.yaowu.hera.model.entity.favor.FavorStock;
import com.yaowu.hera.utils.BizException;
import com.yaowu.hera.utils.ErrorCode;
import com.yaowu.heraapi.model.dto.favor.RemoteFavorStockGrantWechatSignDTO;
import com.yaowu.heraapi.model.dto.sticker.RemoteStickerGrantStockWechatSignDTO;
import com.yaowu.heraapi.model.dto.sticker.RemoteStickerQueryPartnerStockDTO;
import com.yaowu.heraapi.model.vo.favor.RemoteFavorStockGrantWechatSignVO;
import com.yaowu.heraapi.model.vo.sticker.RemoteStickerQueryPartnerStockVO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import jakarta.annotation.Resource;

/**
 * <AUTHOR>
 * @since 2023/8/18 20:22
 **/
@Slf4j
@Service
public class StickerStockBizServiceImpl implements IStickStockBizService {

    @Resource
    private CarStickerConfig carStickerConfig;

    @Resource
    private IFavorStockInfoBizService stockInfoBizService;

    @Resource
    private IStickerPartnerInfoService stickerPartnerInfoService;

    @Resource
    private IFavorUserRelationBizService favorUserRelationBizService;

    @Resource
    private IFavorGrantBizService favorGrantBizService;

    @Override
    public RemoteStickerQueryPartnerStockVO queryStock(RemoteStickerQueryPartnerStockDTO dto) {
        BizException.condition(!stickerPartnerInfoService.checkExistByPartnerCode(dto.getPartnerCode()),
                ErrorCode.STICKER_PARTNER_CODE_EXIST);

        String stockId = carStickerConfig.getCarStickerStockId();
        BizException.condition(StringUtils.isBlank(stockId), ErrorCode.STICKER_PARTNER_CODE_EXIST);

        FavorStock stock =
                stockInfoBizService.queryRemoteIfNotExistFromSticker(stockId, carStickerConfig.getCarStickerStockSource());
        BizException.condition(null == stock, ErrorCode.STICKER_PARTNER_CODE_EXIST);

        RemoteStickerQueryPartnerStockVO vo = new RemoteStickerQueryPartnerStockVO();
        vo.setStockId(carStickerConfig.getCarStickerStockId());
        vo.setStockSource(carStickerConfig.getCarStickerStockSource());
        return vo;
    }

    @Override
    public RemoteFavorStockGrantWechatSignVO grantWechatSign(RemoteStickerGrantStockWechatSignDTO dto) {
        boolean bindOk = favorUserRelationBizService.bindForSticker(dto.getStockId(), dto.getBizUserId());
        BizException.condition(!bindOk, ErrorCode.STICKER_PARTNER_NO_STOCK);

        RemoteFavorStockGrantWechatSignDTO grantWechatSignDTO = new RemoteFavorStockGrantWechatSignDTO();
        grantWechatSignDTO.setStockId(dto.getStockId());
        grantWechatSignDTO.setBizUserId(dto.getBizUserId());
        return favorGrantBizService.grantWechatSign(grantWechatSignDTO);
    }
}
