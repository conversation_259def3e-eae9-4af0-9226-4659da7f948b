package com.yaowu.hera.domain.aicc.biz.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.yaowu.hera.domain.aicc.biz.IAiccRepairedBizService;
import com.yaowu.hera.domain.aicc.biz.IAiccTicketBizService;
import com.yaowu.hera.domain.aicc.service.batis.service.IAiccRepairedService;
import com.yaowu.hera.enums.aicc.TicketTypeEnum;
import com.yaowu.hera.model.dto.aicc.TicketRepairFollowUpDTO;
import com.yaowu.hera.model.entity.aicc.AiccRepaired;
import com.yaowu.hera.utils.convertor.aicc.AiccConvertor;
import com.yaowu.hera.utils.mapstruct.aicc.AiccTicketMapStruct;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import jakarta.annotation.Resource;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @since 2023/11/22 14:31
 **/
@Slf4j
@Service
public class AiccRepairedBizServiceImpl implements IAiccRepairedBizService {

    @Resource
    private IAiccRepairedService repairedService;

    @Resource
    private IAiccTicketBizService aiccTicketBizService;

    @Override
    public void queryRepairedAndCreateOrder(LocalDate date) {
        List<AiccRepaired> repairedList = queryRepairedList(date);
        if (CollectionUtils.isEmpty(repairedList)) {
            return;
        }

        Set<String> phones = repairedList.stream().map(AiccRepaired::getOrderContractSignedTenantPhone).collect(Collectors.toSet());
        Map<String, List<AiccRepaired>> repaireMap = repairedList.stream()
                .collect(Collectors.groupingBy(AiccRepaired::getOrderContractSignedTenantPhone));

        for (String phone : phones) {
            List<AiccRepaired> personRepariList = repaireMap.get(phone);
            TicketRepairFollowUpDTO dto = AiccTicketMapStruct.INSTANCE.toTicketRepairFollowUpDTO(personRepariList.get(0));
            dto.setRemark(AiccConvertor.createRepairReMarksSkip1(personRepariList));
            aiccTicketBizService.saveFollowUpTicket(dto, TicketTypeEnum.AICC_ORDER_REPAIR);
        }
    }

    private List<AiccRepaired> queryRepairedList(LocalDate date) {
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyyMMdd");
        LambdaQueryWrapper<AiccRepaired> wrapper = Wrappers.lambdaQuery(AiccRepaired.class).
                eq(AiccRepaired::getDs, formatter.format(date));

        return repairedService.list(wrapper);
    }
}
