//package com.yaowu.hera.domain.merchant.biz;
//
//import cn.hutool.core.collection.CollUtil;
//import com.freedom.feign.utils.FeignInvokeUtils;
//import com.freedom.web.model.resp.BaseResult;
//import com.yaowu.hera.domain.elasticsearch.biz.IElasticsearchService;
//import com.yaowu.hera.domain.feign.KratosFeignBizService;
//import com.yaowu.hera.domain.leads.biz.ILeadsDeviceBizService;
//import com.yaowu.hera.domain.leads.service.batis.service.ILeadsDeviceAssociationWordService;
//import com.yaowu.hera.model.bo.common.SpuInfo;
//import com.yaowu.hera.remote.kratos.IKratosFeignService;
//import com.yaowu.hera.remote.melina.IRemoteMelinaStoreInfoService;
//import com.yaowu.hera.remote.melina.MelinaMerchantInfoFeignService;
//import com.yaowu.hera.utils.StreamUtil;
//import com.yaowu.kratosapi.feign.device.IRemoteDeviceCategoryServiceFeign;
//import com.yaowu.kratosapi.model.dto.device.RemoteDeviceCategoryIds;
//import com.yaowu.kratosapi.model.dto.device.RemoteEnableTreeSpuDTO;
//import com.yaowu.kratosapi.model.vo.device.RemoteDeviceCategoryDetailVO;
//import com.yaowu.kratosapi.model.vo.device.RemoteDeviceCategoryTreeVO;
//import com.yaowu.melinaapi.model.vo.merchant.RemoteMerchantInfoSimpleVO;
//import com.yaowu.melinaapi.model.vo.store.RemoteMelinaStoreScopeVO;
//import com.yaowu.melinaapi.model.vo.store.RemoteStoreInfoDetailVO;
//import io.swagger.annotations.ApiModelProperty;
//import io.swagger.v3.oas.annotations.media.Schema;
//import jakarta.annotation.Resource;
//import lombok.Getter;
//import lombok.extern.slf4j.Slf4j;
//import org.springframework.beans.factory.annotation.Autowired;
//
//import java.util.*;
//import java.util.function.Function;
//import java.util.stream.Collectors;
//
///**
// * 官方文档：https://www.elastic.co/guide/en/elasticsearch/client/java-api-client/8.9/blocking-and-async.html
// * es 搜索
// *
// * <AUTHOR>
// * @date 2024/7/18-17:38
// */
//@Slf4j
//public abstract class AbstractMerchantWriteToElasticsearchService {
//
//    @Autowired
//    protected IRemoteMelinaStoreInfoService remoteMelinaStoreInfoService;
//
//    @Autowired
//    protected MelinaMerchantInfoFeignService melinaMerchantInfoFeignService;
//
//    @Autowired
//    protected IKratosFeignService kratosFeignService;
//
//    @Autowired
//    protected IRemoteDeviceCategoryServiceFeign remoteDeviceCategoryServiceFeign;
//
//    @Autowired
//    protected IElasticsearchService elasticsearchService;
//    @Resource
//    protected ILeadsDeviceAssociationWordService iLeadsDeviceAssociationWordService;
//    @Resource
//    protected ILeadsDeviceBizService iLeadsDeviceBizService;
//    @Resource
//    protected  KratosFeignBizService kratosFeignBizService;
//
//    /**
//     * 全量spu 数据
//     *
//     * @return
//     */
//    public Map<Long/*spuId or firstCategoryId*/, SpuInfo> spuInfoMap() {
//        RemoteEnableTreeSpuDTO spuDTO = new RemoteEnableTreeSpuDTO();
//        // 传0就是全部
//        spuDTO.setBizGroupCode(null);
//        spuDTO.setBizTypeCode(null);
//        //1.设备 2.属具
//        spuDTO.setCatType(1);
//        List<RemoteDeviceCategoryTreeVO> treeVOS = kratosFeignService.enableTreeSpu(spuDTO);
//        return SpuInfo.buildSpu(treeVOS);
//    }
//
//    public Map<Long/*firstCategoryId*/, List<SpuInfo>> spuCategoryMap() {
//        RemoteEnableTreeSpuDTO spuDTO = new RemoteEnableTreeSpuDTO();
//        // 传0就是全部
//        spuDTO.setBizGroupCode(0);
//        spuDTO.setBizTypeCode(0);
//        //1.设备 2.属具
//        spuDTO.setCatType(1);
//        List<RemoteDeviceCategoryTreeVO> treeVOS = kratosFeignService.enableTreeSpu(spuDTO);
//        return SpuInfo.buildCategory(treeVOS);
//    }
//
//    /**
//     * 门店数据
//     *
//     * @param merchantId 商户id
//     * @return
//     */
//    public Map<Long/*门店id*/, RemoteStoreInfoDetailVO /*门店数据*/> storeInfoMapByMerchantId(Long merchantId) {
//        List<RemoteStoreInfoDetailVO> storeBasicInfos = remoteMelinaStoreInfoService.getStoreInfoByMerchantId(merchantId);
//        return StreamUtil.of(storeBasicInfos).collect(Collectors.toMap(RemoteStoreInfoDetailVO::getId, Function.identity()));
//    }
//
//    public Map<Long/*门店id*/, RemoteStoreInfoDetailVO /*门店数据*/> storeInfoMapByStoreIds(Set<Long> storeIds) {
//        List<RemoteStoreInfoDetailVO> storeBasicInfos = remoteMelinaStoreInfoService.getStoreInfoByStoreIds(storeIds);
//        return StreamUtil.of(storeBasicInfos).collect(Collectors.toMap(RemoteStoreInfoDetailVO::getId, Function.identity()));
//    }
//
//    /**
//     * 门店经营类目
//     *
//     * @param storeIds
//     * @return
//     */
//    public Map<Long, List<StoreCategoryInfo>> storeIdCategory(Set<Long> storeIds, Map<Long/*一级类目id*/, SpuInfo> spuInfoMap) {
//        Map<Long, List<StoreCategoryInfo>> result = new HashMap<>();
//        Map<Long, List<RemoteMelinaStoreScopeVO>> map = remoteMelinaStoreInfoService.storeScopeByCondition(storeIds);
//        map.forEach((k, v) -> {
//            // 进行分组,每组至少包含 businessScopeTypes 其中一个数
//            List<StoreCategoryInfo> storeCategoryInfo = StoreCategoryInfo.fillCategoryIds(v, spuInfoMap);
//            result.put(k, storeCategoryInfo);
//        });
//        return result;
//    }
//
//    public Map<Long, RemoteDeviceCategoryDetailVO> deviceCategorys(Set<Long> ids) {
//        RemoteDeviceCategoryIds deviceCategoryIds = new RemoteDeviceCategoryIds();
//        deviceCategoryIds.setIds(ids);
//        BaseResult<Map<Long, RemoteDeviceCategoryDetailVO>> mapByIds = remoteDeviceCategoryServiceFeign.getMapByIds(deviceCategoryIds);
//        return FeignInvokeUtils.convertMap(mapByIds, Long.class, RemoteDeviceCategoryDetailVO.class);
//    }
//
//    /**
//     * 获取商户id
//     *
//     * @param merchantIds
//     * @return
//     */
//    public Map<Long, RemoteMerchantInfoSimpleVO> getMerchantByIds(Set<Long> merchantIds) {
//        if (CollUtil.isEmpty(merchantIds)) {
//            return new HashMap<>();
//        }
//        return melinaMerchantInfoFeignService.mapMerchantByIds(merchantIds);
//    }
//
//    protected static class StoreCategoryInfo {
//
//        /**
//         * 一级类目code
//         */
//        public static final Integer FIRST_CAT_CODE = 1;
//
//        /**
//         * 二级类目code
//         */
//        public static final Integer SECOND_CAT_CODE = 2;
//
//        @ApiModelProperty("#第一级别类目#")
//        @Getter
//        private Long firstCategoryId;
//
//        @ApiModelProperty("#品类id(最后一级)#")
//        @Getter
//        private Long categoryId;
//
//        @ApiModelProperty("#第一级别类目#")
//        @Getter
//        private String firstCategoryName;
//
//        @Schema(title = "#品类名称(最后一级)#")
//        @Getter
//        private String categoryName;
//
//        @Getter
//        private String firstCategoryNameAlias;
//
//        @Getter
//        private String categoryNameAlias;
//
//        @Getter
//        private Set<SpuInfo> spuInfos;
//
//        @Getter
//        private Long storeId;
//
//        public static List<StoreCategoryInfo> fillCategoryIds(List<RemoteMelinaStoreScopeVO> vos, Map<Long/*一级类目id*/, SpuInfo> spuInfoMap) {
//            List<StoreCategoryInfo> result = new ArrayList<>();
//            // 一级类目
//            StreamUtil.of(vos).filter(first -> FIRST_CAT_CODE.equals(first.getBusinessScopeType())).filter(first -> spuInfoMap.containsKey(first.getBusinessId())).forEach(first -> {
//                SpuInfo spuInfo = spuInfoMap.get(first.getBusinessId());
//                StoreCategoryInfo info = new StoreCategoryInfo();
//                info.spuInfos = new HashSet<>();
//                info.storeId = first.getStoreId();
//                info.firstCategoryId = first.getBusinessId();
//                info.firstCategoryName = spuInfo.getFirstCategoryName();
//                info.firstCategoryNameAlias = spuInfo.getFirstCategoryNameAlias();
//                // 二级类目是否存在
//                Optional<RemoteMelinaStoreScopeVO> any = StreamUtil.of(vos)
//                        .filter(m -> SECOND_CAT_CODE.equals(m.getBusinessScopeType()))
//                        .filter(m -> spuInfo.getCategoryId().equals(m.getBusinessId())).findAny();
//                if (any.isPresent()) {
//                    info.categoryId = spuInfo.getCategoryId();
//                    info.categoryName = spuInfo.getCategoryName();
//                    info.categoryNameAlias = spuInfo.getCategoryNameAlias();
//                }
//                result.add(info);
//            });
//            return result;
//        }
//    }
//}
