package com.yaowu.hera.domain.mtl.advertiser.service.batis.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.freedom.security.common.SecurityContext;
import com.google.common.collect.Lists;
import com.yaowu.hera.domain.mtl.advertiser.service.batis.mapper.AdFeeDayRecordMapper;
import com.yaowu.hera.domain.mtl.advertiser.service.batis.service.IAdFeeDayRecordService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.yaowu.heraapi.enums.mtl.advertiser.RemoteAdPlatformConfirmStatus;
import com.yaowu.hera.model.bo.mtl.AdFeeDayRecordConditionBO;
import com.yaowu.hera.model.entity.mtl.advertiser.AdFeeDayRecord;
import com.yaowu.hera.utils.mapstruct.mtl.AdPlanMapStruct;
import com.yaowu.heraapi.model.dto.mtl.advertiser.RemoteAdAdvertiserAddFeeDTO;
import com.yaowu.heraapi.model.dto.mtl.advertiser.RemoteAdAdvertiserFeePageDTO;
import com.yaowu.heraapi.model.dto.mtl.advertiser.RemoteAdvertiserConfirmFeeDTO;
import com.yaowu.heraapi.model.dto.mtl.advertiser.RemoteAdvertiserEditFeeDTO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.util.List;

/**
 * <p>
 * 广告主费用日流水 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-02-08
 */
@Service
public class AdFeeDayRecordServiceImpl extends ServiceImpl<AdFeeDayRecordMapper, AdFeeDayRecord> implements IAdFeeDayRecordService {

    @Autowired
    private AdPlanMapStruct adPlanMapStruct;

    @Override
    public List<AdFeeDayRecord> getListByCondition(AdFeeDayRecordConditionBO bo) {
        LambdaQueryWrapper<AdFeeDayRecord> queryWrapper = Wrappers.lambdaQuery(AdFeeDayRecord.class)
                .eq(bo.getAdPlatform()!=null,AdFeeDayRecord::getAdPlatform,bo.getAdPlatform())
                .ge(bo.getStartDate()!=null,AdFeeDayRecord::getDateTime,bo.getStartDate())
                .le(bo.getEndDate()!=null,AdFeeDayRecord::getDateTime,bo.getEndDate());
        return this.list(queryWrapper);
    }

    @Override
    public Long addFee(RemoteAdAdvertiserAddFeeDTO dto) {
        AdFeeDayRecord fee = adPlanMapStruct.toAdFeeDayRecord(dto);
        fee.setFeeStatus(RemoteAdPlatformConfirmStatus.CONFIRM.getCode());
        this.save(fee);
        return fee.getId();
    }

    @Override
    public Boolean confirmFee(RemoteAdvertiserConfirmFeeDTO dto) {
        List<AdFeeDayRecord> entityList = this.listByIds(dto.getIds());
        if(CollectionUtils.isEmpty(entityList)){
            return true;
        }
        return this.update(Wrappers.lambdaUpdate(AdFeeDayRecord.class)
                .set(AdFeeDayRecord::getFeeStatus, RemoteAdPlatformConfirmStatus.CONFIRM.getCode())
                .in(AdFeeDayRecord::getId,dto.getIds()));
    }

    @Override
    public Boolean editFee(RemoteAdvertiserEditFeeDTO dto) {
        AdFeeDayRecord entity = this.getById(dto.getId());
        if(entity == null){
            return true;
        }
        return this.update(Wrappers.lambdaUpdate(AdFeeDayRecord.class)
                .set(AdFeeDayRecord::getCost, dto.getCost())
                .set(dto.getOperatorUserId()!=null, AdFeeDayRecord::getOperatorUserId,dto.getOperatorUserId())
                .set(StringUtils.hasText(dto.getOperatorUserName()),AdFeeDayRecord::getOperatorUserName,dto.getOperatorUserName())
                .eq(AdFeeDayRecord::getId,dto.getId()));
    }

    @Override
    public Boolean removeFee(Long id) {
        AdFeeDayRecord entity = this.getById(id);
        if(entity == null){
            return true;
        }
        return this.update(Wrappers.lambdaUpdate(AdFeeDayRecord.class)
                .set(AdFeeDayRecord::getFeeStatus, RemoteAdPlatformConfirmStatus.DELETE.getCode())
                .eq(AdFeeDayRecord::getId,id));
    }

    @Override
    public Page<AdFeeDayRecord> feePage(RemoteAdAdvertiserFeePageDTO dto) {
        LambdaQueryWrapper<AdFeeDayRecord> queryWrapper = Wrappers.lambdaQuery(AdFeeDayRecord.class);
        queryWrapper
                .eq(dto.getAdPlatform()!=null, AdFeeDayRecord::getAdPlatform, dto.getAdPlatform())
                .eq(dto.getAdvertiserId()!=null, AdFeeDayRecord::getAdvertiserId, dto.getAdvertiserId())
                .eq(dto.getFeeStatus()!=null, AdFeeDayRecord::getFeeStatus, dto.getFeeStatus())
                .ge(dto.getStartDateTime()!=null, AdFeeDayRecord::getDateTime, dto.getStartDateTime())
                .le(dto.getEndDateTime()!=null, AdFeeDayRecord::getDateTime, dto.getEndDateTime())
                .in(CollectionUtil.isNotEmpty(dto.getAdvertiserIds()), AdFeeDayRecord::getAdvertiserId, dto.getAdvertiserIds())
                .ne(AdFeeDayRecord::getFeeStatus, RemoteAdPlatformConfirmStatus.DELETE.getCode())
                .orderByDesc(AdFeeDayRecord::getDateTime);
        return this.page(dto.pageRequest(), queryWrapper);
    }


}
