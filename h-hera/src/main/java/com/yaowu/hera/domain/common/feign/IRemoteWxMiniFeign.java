package com.yaowu.hera.domain.common.feign;

import com.freedom.feign.configuration.GeneralServiceFeignConfiguration;
import com.freedom.web.model.resp.BaseResult;
import com.yaowu.hera.domain.common.feign.fallback.RemoteWxMiniServiceFeignFallbackFactory;
import com.yaowu.hera.model.dto.common.wxmini.GenerateUrlLinkDTO;
import io.swagger.v3.oas.annotations.Operation;
import jakarta.validation.constraints.NotEmpty;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

/**
 * <AUTHOR>
 * @since 2023/8/18 21:53
 **/
@FeignClient(value = "g-third-party", url = "${remote.service.third-party}",
        contextId = "RemoteWxMiniFeign",
        fallbackFactory = RemoteWxMiniServiceFeignFallbackFactory.class,
        configuration = GeneralServiceFeignConfiguration.class)
public interface IRemoteWxMiniFeign {

    @PostMapping("/v1/api/wxmini/generate-url-link")
    @Operation(summary = "生成微信链接")
    BaseResult<String> generateUrlLink(@RequestBody @Validated GenerateUrlLinkDTO dto);

    @GetMapping("/v1/api/wxwork/user/get-userid-by-mobile")
    @Operation(summary = "通过手机号获取userId")
    BaseResult<String> getUserIdByMobile(@RequestParam @NotEmpty(message = "手机号不能为空") String mobile);

}
