package com.yaowu.hera.domain.log.service.batis.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.yaowu.hera.model.entity.log.CallLogs;

import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * <AUTHOR>
 */
public interface CallLogsMapper extends BaseMapper<CallLogs> {

    boolean saveFullHistoricalCallLogs(List<CallLogs> callLogs);

    List<Map<String, Object>> selectUser(Set<String> phoneNumbers);

}
