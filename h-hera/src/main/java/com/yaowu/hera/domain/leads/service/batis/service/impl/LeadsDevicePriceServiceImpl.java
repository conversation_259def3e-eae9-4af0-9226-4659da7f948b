package com.yaowu.hera.domain.leads.service.batis.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.yaowu.hera.domain.leads.service.batis.mapper.LeadsDevicePriceMapper;
import com.yaowu.hera.domain.leads.service.batis.service.ILeadsDevicePriceService;
import com.yaowu.hera.enums.mtl.LeadsChargingUnitEnum;
import com.yaowu.hera.model.bo.leads.LeadsDevicePriceDetailBO;
import com.yaowu.hera.model.bo.leads.LeadsDevicePriceQueryBO;
import com.yaowu.hera.model.entity.leads.LeadsDevicePrice;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.tuple.Pair;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.math.BigDecimal;
import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 */
@Slf4j
@Service
public class LeadsDevicePriceServiceImpl extends ServiceImpl<LeadsDevicePriceMapper, LeadsDevicePrice>
        implements ILeadsDevicePriceService {

    @Override
    public LeadsDevicePriceDetailBO queryDevicePrice(LeadsDevicePriceQueryBO queryBO) {
        return doQueryDevicePrice(queryBO.getProvinceCode(), queryBO.getCityCode(),
                queryBO.getFirstCategoryId(), queryBO.getSecondCategoryId(), queryBO.getSpuId(),
                queryBO.getChargingUnit());
    }


    private LeadsDevicePriceDetailBO doQueryDevicePrice(String provinceCode, String cityCode,
                                                        Long firstCategoryId, Long secondCategoryId, Long spuId,
                                                        LeadsChargingUnitEnum chargingUnit) {
        if ((!StringUtils.hasText(provinceCode) && !StringUtils.hasText(cityCode)) || spuId == null) {
            return null;
        }

        List<LeadsDevicePrice> prices = this.lambdaQuery()
                .eq(LeadsDevicePrice::getSpuId, spuId)
                .eq(Objects.nonNull(firstCategoryId), LeadsDevicePrice::getFirstCategoryId, firstCategoryId)
                .eq(Objects.nonNull(secondCategoryId), LeadsDevicePrice::getSecondCategoryId, secondCategoryId)
                .and(q -> q.eq(StringUtils.hasText(provinceCode), LeadsDevicePrice::getProvinceCode, provinceCode)
                        .or()
                        .eq(StringUtils.hasText(cityCode), LeadsDevicePrice::getCityCode, cityCode))
                .list();
        if (CollectionUtils.isEmpty(prices)) {
            return null;
        }
        LeadsDevicePrice cityPrice = prices.stream().filter(v -> StringUtils.hasText(v.getCityCode())).findFirst().orElse(null);
        LeadsDevicePriceDetailBO priceDetail;
        if ((priceDetail = getValidPriceDetail(cityPrice, chargingUnit)) != null) {
            return priceDetail;
        }
        log.info("LeadsDevicePriceConfig NoCityPrice, city: {}, try province: {}", cityCode, provinceCode);
        LeadsDevicePrice provincePrice = prices.stream()
                .filter(v -> StringUtils.hasText(v.getProvinceCode())).findFirst().orElse(null);
        if ((priceDetail = getValidPriceDetail(provincePrice, chargingUnit)) != null) {
            return priceDetail;
        }
        log.error("LeadsDevicePriceConfig SpuCityPriceNotFoundError, provinceCode: {}, cityCode: {}, " +
                        "firstCategoryId: {}, secondCategoryId: {}, spuId: {}, chargingUnit: {}",
                provinceCode, cityCode, firstCategoryId, secondCategoryId, spuId, chargingUnit);
        return null;
    }

    private LeadsDevicePriceDetailBO getValidPriceDetail(LeadsDevicePrice price, LeadsChargingUnitEnum chargingUnit) {
        if (price == null) {
            return null;
        }

        String addressCode = StringUtils.hasText(price.getProvinceCode()) ? price.getProvinceCode() : price.getCityCode();
        Pair<BigDecimal, BigDecimal> pair;
        switch (chargingUnit) {
            case DAILY:
                pair = makePricePair(price.getDailyMin(), price.getDailyMax());
                break;
            case MONTHLY:
                pair = makePricePair(price.getMonthlyMin(), price.getMonthlyMax());
                break;
            case MACHINE_TEAM:
                pair = makePricePair(price.getMachineTeamMin(), price.getMachineTeamMax());
                break;
            case HALF_MACHINE_TEAM:
                pair = makePricePair(price.getHalfMachineTeamMin(), price.getHalfMachineTeamMax());
                break;
            case TRIP:
                pair = makePricePair(price.getTripMin(), price.getTripMax());
                break;
            default:
                pair = null;
        }
        if (pair == null) {
            return null;
        }
        return LeadsDevicePriceDetailBO.of(addressCode, price.getSpuId(), price.getSpuName(), pair.getLeft(), pair.getRight());
    }

    private Pair<BigDecimal, BigDecimal> makePricePair(BigDecimal minPrice, BigDecimal maxPrice) {
        if (minPrice == null || minPrice.compareTo(BigDecimal.ZERO) <= 0
                || maxPrice == null || maxPrice.compareTo(BigDecimal.ZERO) <= 0) {
            return null;
        }
        return Pair.of(minPrice, maxPrice);
    }

}
