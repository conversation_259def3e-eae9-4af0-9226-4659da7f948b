package com.yaowu.hera.domain.mtl.leadsorder.refund.impl;

import com.aliyun.tea.utils.StringUtils;
import com.yaowu.hera.domain.mtl.leadsorder.refund.BaseOrderRefund;
import com.yaowu.hera.model.bo.mtl.SubmitOrderRefundContext;
import com.yaowu.hera.model.entity.leads.LeadsOrderInfo;
import com.yaowu.hera.model.entity.leads.LeadsOrderInfoRefund;
import com.yaowu.hera.utils.BizException;
import com.yaowu.hera.utils.ErrorCode;
import com.yaowu.heraapi.enums.mtl.order.LeadsRefundStatusEnum;
import com.yaowu.heraapi.enums.mtl.order.LeadsRefundTypeEnum;
import com.yaowu.heraapi.model.dto.mtl.order.HeraApproveOrderRefundDTO;
import com.yaowu.heraapi.model.dto.mtl.order.HeraOrderRefundDTO;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;
import java.util.Objects;

import static com.yaowu.hera.utils.ErrorCode.INNER_ERROR;
import static com.yaowu.heraapi.model.dto.mtl.order.HeraApproveOrderRefundDTO.SubmitTypeEnum.PERMIT;
import static com.yaowu.heraapi.model.dto.mtl.order.HeraApproveOrderRefundDTO.SubmitTypeEnum.REJECT;

/**
 * <AUTHOR> Weiyang
 * @email <EMAIL>
 * @creatTime 2024.11.18 10:43:00
 * @description:
 */

@Component
public class AdminOrderRefund extends BaseOrderRefund {


    @Override
    public boolean supports(LeadsRefundTypeEnum leadsRefundTypeEnum) {
        return LeadsRefundTypeEnum.ADMIN_INITIATED == leadsRefundTypeEnum;
    }

    @Override
    public boolean doSubmitRefund(SubmitOrderRefundContext context) {
        LeadsOrderInfo leadsOrderInfo = context.getLeadsOrderInfo();
        HeraOrderRefundDTO submitDto = context.getSubmitDto();
        LeadsOrderInfoRefund init = leadsOrderInfoRefundMapstruct.init(submitDto, leadsOrderInfo, LeadsRefundTypeEnum.ADMIN_INITIATED);
        BizException.isFail(leadsOrderInfoRefundService.save(init), ErrorCode.INNER_ERROR);
        context.setLeadsOrderInfoRefund(init);
        return true;
    }


    @Override
    public boolean judgeAutoRefund(LeadsOrderInfo leadsOrderInfo) {
        // 默认自动审核
        return true;
    }

    @Override
    public LeadsOrderInfoRefund doApprove(HeraApproveOrderRefundDTO dto) {
        LeadsOrderInfoRefund infoRefund = leadsOrderInfoRefundService.getById(dto.getOrderRefundId());
        BizException.condition(infoRefund == null, ErrorCode.INNER_ERROR);
        BizException.isFail(LeadsRefundStatusEnum.codeOf(infoRefund.getRefundStatus()).approveRefundEnable(), ErrorCode.INNER_ERROR);

        infoRefund.setReviewTime(LocalDateTime.now());
        infoRefund.setReviewerId(dto.getReviewerId());
        infoRefund.setReviewerName(dto.getReviewerName());
        infoRefund.setRefundRemark(dto.getRefundRemark());
        infoRefund.setRefundStatus(dto.getSubmitType() == REJECT ? LeadsRefundStatusEnum.REJECTED.getCode() : LeadsRefundStatusEnum.COMPLETED.getCode());
        if(dto.getSubmitType() == PERMIT){
            BizException.condition(Objects.isNull(dto.getApproveReasonCode()), ErrorCode.LEADS_ORDER_REFUND_APPROVE_REASON_NULL);
            infoRefund.setApproveReasonCode(dto.getApproveReasonCode());
        }
        BizException.isFail(leadsOrderInfoRefundService.updateById(infoRefund), INNER_ERROR);

        if (dto.getSubmitType() == PERMIT){
            checkOrderRefund(infoRefund.getLeadsOrderId());
            leadsOrderInfoBizService.doOrderRefund(infoRefund.getLeadsOrderId(),
                    infoRefund.getRefundRemark(), infoRefund.getReasonDesc(), infoRefund.getReviewTime());
        }
        return infoRefund;
    }


    public static void main(String[] args) {
        BizException.isFail(LeadsRefundStatusEnum.codeOf(0).approveRefundEnable(), ErrorCode.INNER_ERROR);
    }

    @Override
    protected void validate(SubmitOrderRefundContext context) {
        super.validate(context);
        LeadsOrderInfo leadsOrderInfo = context.getLeadsOrderInfo();
        Long orderId = leadsOrderInfo.getId();
        LeadsOrderInfoRefund leadsOrderInfoRefund = leadsOrderInfoRefundService.getLatestByOrderId(orderId).orElse(null);
        if(leadsOrderInfoRefund == null)
            return;
        BizException.condition(LeadsRefundStatusEnum.PENDING.getCode().equals(leadsOrderInfoRefund.getRefundStatus()), ErrorCode.REFUND_STATUS_PENDING_NOT_RESUBMIT);


    }
}


