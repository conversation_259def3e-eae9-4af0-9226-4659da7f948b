package com.yaowu.hera.domain.mtl.leadsorder.biz.impl.processor.impl;

import com.freedom.web.exception.BusinessException;
import com.yaowu.hera.domain.mtl.leadsorder.biz.impl.processor.impl.support.DefaultLeadsPurchaseHandler;
import com.yaowu.hera.domain.mtl.leadsorder.biz.impl.processor.impl.support.LeadsPurchaseContext;
import com.yaowu.hera.model.dto.mtl.LeadsPurchaseDTO;
import com.yaowu.hera.model.entity.leads.LeadsInfo;
import com.yaowu.hera.model.entity.leads.LeadsOrderInfo;
import com.yaowu.hera.model.vo.mtl.LeadsPurchaseResultVO;
import com.yaowu.hera.utils.ErrorCode;
import com.yaowu.hera.utils.ValidatorUtil;
import com.yaowu.hera.utils.constants.RedisConstants;
import com.yaowu.heraapi.enums.mtl.order.LeadsOrderTransactionSceneEnum;
import com.yaowu.heraapi.enums.mtl.order.OrderContactSceneEnum;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.Objects;

/**
 * 接单宝场景的默认线索购买处理器
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2024/12/4 15:11
 */
@Component
@Slf4j
public class CustomerContactRecommendMerchantPurchaseProcessor extends AbstractLeadsPurchaseProcessor<DefaultLeadsPurchaseHandler>  {

    @Override
    public boolean support(LeadsPurchaseDTO dto) {
        return Objects.equals(dto.getOrderSource(), OrderContactSceneEnum.CUSTOMER_CALLBACK_FROM_RECOMMEND_MERCHANT_LIST.getCode());
    }

    @Override
    public int getOrder() {
        return 2;
    }

    @Override
    protected void preProcess(LeadsPurchaseDTO dto, LeadsPurchaseContext context) {
        if (context.getLeadsOrderInfo() != null) {
            return;
        }
        // 校验参数是否合法
        ValidatorUtil.validate(dto);
        // 校验余额是否充足
        leadsPurchaseValidateUtil.validateAccountBalance(dto);
    }

    @Override
    protected LeadsPurchaseResultVO doProcess(LeadsPurchaseDTO dto, LeadsPurchaseContext context) {

        try {
            // 分布式锁执行，防止重复购买
            String distributedLockKey = RedisConstants.LEADS_ORDER_PURCHASE_LOCK_PREFIX + dto.getLeadsInfoId();
            return distributedLockUtil.executeWithLock(distributedLockKey, 10, 30, () -> {
                // 真正处理线索购买逻辑
                return processOrder(dto, context);
            });
        } catch (BusinessException e) {
            // 抢锁失败，直接返回成功
            if (Objects.equals(ErrorCode.ACQUIRE_LOCK_FAILED.getCode(), e.getCode())) {
                return LeadsPurchaseResultVO.success();
            }
            throw e;
        }
    }

    private LeadsPurchaseResultVO processOrder(LeadsPurchaseDTO dto, LeadsPurchaseContext context) {
        LeadsInfo leadsInfo = context.getLeadsInfo();
        LeadsOrderInfo leadsOrderInfo = context.getLeadsOrderInfo();
        //接单宝的支付价格
        dto.setPaymentPrice(leadsInfo.getC2bPrice());

        // 处理新订单的情况, 只创建订单，不购买
        if (leadsOrderInfo == null) {
            Long orderId = leadsPurchaseHandler.createNewOrder(dto,  context);
            return LeadsPurchaseResultVO.success(orderId);
        }

        // 处理已有订单的情况且参数指定了订单ID，进行购买
        if (dto.getOrderId() != null) {
            return leadsPurchaseHandler.handlePurchaseWithExistingOrder(dto, context);
        }
        // 否则返回原来的订单
        return LeadsPurchaseResultVO.success(leadsOrderInfo.getId());
    }
}
