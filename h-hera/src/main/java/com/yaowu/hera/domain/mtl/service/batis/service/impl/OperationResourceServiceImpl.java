package com.yaowu.hera.domain.mtl.service.batis.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.yaowu.hera.domain.mtl.service.batis.mapper.OperationResourceMapper;
import com.yaowu.hera.domain.mtl.service.batis.service.IOperationResourceService;
import com.yaowu.hera.enums.audio.TranscribedBizTypeEnum;
import com.yaowu.hera.model.entity.audio.TranscribedBizTraceData;
import com.yaowu.hera.model.entity.business.BusinessInfo;
import com.yaowu.hera.model.entity.mtl.OperationResource;
import com.yaowu.hera.model.pojo.OperationResourceQueryModel;
import com.yaowu.hera.utils.BizException;
import com.yaowu.hera.utils.ErrorCode;
import com.yaowu.hera.utils.LongUtils;
import com.yaowu.hera.utils.StringUtil;
import com.yaowu.heraapi.enums.device.ChannelTypeEnum;
import com.yaowu.heraapi.enums.mtl.OperationResourceTypeEnum;
import io.swagger.annotations.ApiModelProperty;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Objects;
import java.util.Set;

@Service
public class OperationResourceServiceImpl extends ServiceImpl<OperationResourceMapper, OperationResource> implements IOperationResourceService {

    @Override
    public List<OperationResource> listByCondition(OperationResourceQueryModel queryModel) {
        LambdaQueryWrapper<OperationResource> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.notIn(CollectionUtil.isNotEmpty(queryModel.getNotInIds()), OperationResource::getId, queryModel.getNotInIds());
        queryWrapper.eq(queryModel.getResourceType() != null, OperationResource::getResourceType, queryModel.getResourceType());
        queryWrapper.in(CollectionUtil.isNotEmpty(queryModel.getResourceTypes()), OperationResource::getResourceType, queryModel.getResourceTypes());
        queryWrapper.eq(queryModel.getChannelType() != null, OperationResource::getChannelType, queryModel.getChannelType());
        queryWrapper.in(CollectionUtil.isNotEmpty(queryModel.getChannelTypes()), OperationResource::getChannelType, queryModel.getChannelTypes());
        queryWrapper.eq(queryModel.getTestFlag() != null, OperationResource::getTestFlag, queryModel.getTestFlag());
        //城市code
        if (StringUtil.isNotBlank(queryModel.getIncludeCityCode())) {
            queryWrapper.and(wrapper -> {
                // 条件 1: 限定类型且城市 code 包含指定值
                wrapper.apply("JSON_CONTAINS(city_config->'$.cityInfos[*].cityCodes', JSON_ARRAY({0})) = 1", queryModel.getIncludeCityCode());
                // 条件 2: 不限定类型
                wrapper.or().apply("city_config->'$.cityUsedScopeType' = 'NOT_LIMIT'");
            });
        }
        queryWrapper.in(CollectionUtil.isNotEmpty(queryModel.getIds()), OperationResource::getId, queryModel.getIds());
        queryWrapper.orderByAsc(OperationResource::getSort);
        queryWrapper.orderByDesc(OperationResource::getId);
        queryWrapper.last(queryModel.getLimitCount()!=null && queryModel.getLimitCount()>0,"limit "+queryModel.getLimitCount());
        return list(queryWrapper);
    }

}
