package com.yaowu.hera.domain.mtl.leadsclean.service.batis.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.yaowu.hera.model.dto.mtl.leadsclean.ValueLeadsConfigQueryDTO;
import com.yaowu.hera.model.entity.mtl.leadsclean.ValueLeadsConfig;

import java.util.List;

/**
 * 高价值线索基础配置服务接口
 * <AUTHOR>
 * @version 1.0
 * @date 2024/01/04
 */
public interface IValueLeadsConfigService extends IService<ValueLeadsConfig> {
    /**
     * 获取并校验配置信息
     * @param configId 配置ID
     * @return 配置信息
     */
    ValueLeadsConfig getAndCheck(Long configId);

    /**
     * 根据条件查询单个配置
     * @param dto 查询参数
     * @return 配置信息
     */
    ValueLeadsConfig getByCondition(ValueLeadsConfigQueryDTO dto);

    /**
     * 根据条件查询配置列表
     * @param dto 查询参数
     * @return 配置列表
     */
    List<ValueLeadsConfig> listByCondition(ValueLeadsConfigQueryDTO dto);

    /**
     * 根据条件分页查询配置列表
     * @param dto 查询参数
     * @return 分页配置列表
     */
    IPage<ValueLeadsConfig> pageByCondition(ValueLeadsConfigQueryDTO dto);
}