package com.yaowu.hera.domain.log.biz.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.freedom.mybatis.plugin.encrypt.Encrypt;
import com.yaowu.hera.domain.log.biz.ICallLogsBizService;
import com.yaowu.hera.domain.log.biz.IOperationLogBizService;
import com.yaowu.hera.domain.log.service.batis.service.ICallLogsService;
import com.yaowu.hera.model.entity.log.CallLogs;
import com.yaowu.hera.utils.mapstruct.log.CallLogsMapStruct;
import com.yaowu.heraapi.model.dto.log.RemoteAddBatchCallLogsDTO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

import static com.yaowu.hera.utils.constants.BizConfigConstants.CALL_LOGS_QUANTITY_LIMIT;

/**
 * <AUTHOR>
 */
@Slf4j
@Service
public class CallLogsBizServiceImpl implements ICallLogsBizService {

    @Autowired
    private CallLogsMapStruct callLogsMapStruct;

    @Autowired
    private ICallLogsService callLogsService;

    @Autowired
    private Encrypt encrypt;

    @Autowired
    @Qualifier("commonExecutor")
    private ThreadPoolTaskExecutor commonExecutor;

    @Autowired
    private IOperationLogBizService operationLogBizService;


    @Override
    public Boolean saveFullHistoricalCallLogs(RemoteAddBatchCallLogsDTO dto) {
        //打印开始时间
        log.info("saveFullHistoricalCallLogs“ start time: {}", System.currentTimeMillis());
        if (CollectionUtils.isEmpty(dto.getAddBatchCallLogDtoList())) {
            return true;
        }
        if (dto.getAddBatchCallLogDtoList().size() > CALL_LOGS_QUANTITY_LIMIT) {
            log.warn("CallLogsIsTooLargeError, size: {}", dto.getAddBatchCallLogDtoList().size());
            return false;
        }

        List<CallLogs> callLogs = callLogsMapStruct.toCallLogsList(dto.getAddBatchCallLogDtoList());

        commonExecutor.submit(() -> {
            try {
                //遍历通话记录保存对应电销线索的电话跟进的操作记录
                operationLogBizService.saveOperationLogByCallLogs(callLogs);
                //保存通话记录
                callLogsService.saveFullHistoricalCallLogs(callLogs);
                //打印结束时间
                log.info("saveFullHistoricalCallLogs“ end time: {}", System.currentTimeMillis());
            } catch (Exception e) {
                log.warn("CallLogsSaveFullHistoricalCallLogsError", e);
            }
        });
        return true;
    }

    @Override
    public List<CallLogs> listLogsByExcludeUniqueKey(List<CallLogs> callLogs) {
        if (com.baomidou.mybatisplus.core.toolkit.CollectionUtils.isEmpty(callLogs)){
            return Collections.emptyList();
        }
        List<String> localPhoneNumbers = new ArrayList<>();
        List<String> contactPhoneNumbers = new ArrayList<>();
        List<LocalDateTime> callStartTimes = new ArrayList<>();

        for (CallLogs log : callLogs) {
            localPhoneNumbers.add(encrypt.encrypt(log.getLocalPhoneNumberCrypto()));
            contactPhoneNumbers.add(encrypt.encrypt(log.getContactPhoneNumberCrypto()));
            callStartTimes.add(log.getCallStartTime());
        }

        // 使用 QueryWrapper 构建批量查询条件
        QueryWrapper<CallLogs> queryWrapper = new QueryWrapper<>();
        queryWrapper.in("local_phone_number_crypto", localPhoneNumbers)
                .in("contact_phone_number_crypto", contactPhoneNumbers)
                .in("call_start_time", callStartTimes);

        // 执行批量查询，获取数据库中已存在的记录列表
        List<CallLogs> existingLogs = callLogsService.list(queryWrapper);

        // 过滤出不存在于数据库中的记录
        Set<String> existingKeys = existingLogs.stream()
                .map(log -> log.getLocalPhoneNumberCrypto() + log.getContactPhoneNumberCrypto() + log.getCallStartTime())
                .collect(Collectors.toSet());

        return callLogs.stream()
                .filter(log1 -> !existingKeys.contains(log1.getLocalPhoneNumberCrypto() + log1.getContactPhoneNumberCrypto() + log1.getCallStartTime()))
                .collect(Collectors.toList());
    }

}
