package com.yaowu.hera.domain.tools.service.batis.service.impl;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.yaowu.hera.domain.tools.service.batis.service.IChannelGenerateLogService;
import com.yaowu.hera.domain.enterprise.service.batis.mapper.ChannelGenerateLogMapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.yaowu.hera.model.bo.mtl.ChannelGenerateLogBO;
import com.yaowu.hera.model.entity.tools.ChannelGenerateLog;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import java.util.List;

/**
 * <p>
 * 渠道海报生成记录 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-02-14
 */
@Service
public class ChannelGenerateLogServiceImpl extends ServiceImpl<ChannelGenerateLogMapper, ChannelGenerateLog> implements IChannelGenerateLogService {

    @Override
    public List<ChannelGenerateLog> listByCondition(ChannelGenerateLogBO bo){
        return this.list(Wrappers.lambdaQuery(ChannelGenerateLog.class)
                .eq(StringUtils.hasText(bo.getCompanyChannelCode()),ChannelGenerateLog::getCompanyChannelCode,bo.getCompanyChannelCode())
                .eq(StringUtils.hasText(bo.getPersonName()),ChannelGenerateLog::getPersonName,bo.getPersonName())
                .eq(StringUtils.hasText(bo.getPhone()),ChannelGenerateLog::getPhone,bo.getPhone())
        );
    }
}
