package com.yaowu.hera.domain.sticker.biz;

import com.yaowu.heraapi.model.dto.sticker.RemoteStickerGrantStockWechatSignDTO;
import com.yaowu.heraapi.model.dto.sticker.RemoteStickerQueryPartnerStockDTO;
import com.yaowu.heraapi.model.vo.favor.RemoteFavorStockGrantWechatSignVO;
import com.yaowu.heraapi.model.vo.sticker.RemoteStickerQueryPartnerStockVO;

/**
 * <AUTHOR>
 * @since 2023/8/18 20:22
 **/
public interface IStickStockBizService {

    /**
     * 通过外部合作商的渠道码，获取可领取的优惠券批次信息
     *
     * @param dto RemoteStickerQueryPartnerStockDTO
     * @return 通过外部合作商的渠道码，获取可领取的优惠券批次信息VO
     */
    RemoteStickerQueryPartnerStockVO queryStock(RemoteStickerQueryPartnerStockDTO dto);

    /**
     * 车贴，获取小程序发券的签名，用于小程序插件领券
     *
     * @param dto StickerGrantStockWechatSignDTO
     * @return 小程序发券的签名VO
     */
    RemoteFavorStockGrantWechatSignVO grantWechatSign(RemoteStickerGrantStockWechatSignDTO dto);
}
