package com.yaowu.hera.domain.mtl.leadsclean.biz.impl.processor.impl.cleanstage.impl.event;

import com.yaowu.hera.enums.mtl.LeadEventDomainTypeEnum;
import com.yaowu.hera.enums.mtl.LeadsEventTypeEnum;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Component;

import java.time.Duration;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2024/12/20 13:42
 */
@EqualsAndHashCode(callSuper = true)
@Data
@Component
@Scope("prototype")
public class TriggerAiCallLeadsCleanEvent extends AbstractLeadsCleanEvent {
    @Override
    public LeadEventDomainTypeEnum domainType() {
        return LeadEventDomainTypeEnum.LEAD_CLEAN;
    }

    @Override
    public LeadsEventTypeEnum eventType() {
        return LeadsEventTypeEnum.TRIGGER_AI_CALL_LEADS_CLEAN;
    }

    @Override
    public Duration eventExpire() {
        return Duration.ofMinutes(10);
    }
}
