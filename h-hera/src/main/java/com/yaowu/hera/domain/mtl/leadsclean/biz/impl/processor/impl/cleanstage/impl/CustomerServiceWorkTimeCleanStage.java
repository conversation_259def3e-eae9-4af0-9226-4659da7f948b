package com.yaowu.hera.domain.mtl.leadsclean.biz.impl.processor.impl.cleanstage.impl;

import com.freedom.bizlog.core.VariableContext;
import com.yaowu.hera.domain.mtl.leadsclean.biz.impl.processor.impl.cleanstage.ILeadsCleanStage;
import com.yaowu.hera.domain.mtl.leadsclean.biz.impl.processor.impl.common.LeadsCleanContext;
import com.yaowu.hera.domain.mtl.leadsclean.biz.support.LeadsRatingSupport;
import com.yaowu.hera.enums.mtl.LeadsCleanStageEnum;
import com.yaowu.hera.enums.mtl.LeadsDispatchActionEnum;
import com.yaowu.hera.model.dto.mtl.leadsclean.LeadsStageCleanRequestDTO;
import com.yaowu.hera.model.entity.leads.LeadsInfo;
import com.yaowu.hera.model.vo.mtl.LeadsStageCleanResultVO;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.time.LocalTime;
import java.util.Objects;

/**
 * 客服工作时间判断
 * <AUTHOR>
 * @version 1.0
 * @date 2024/12/16 21:11
 */
@Component
@Slf4j
public class CustomerServiceWorkTimeCleanStage implements ILeadsCleanStage<LeadsStageCleanRequestDTO, LeadsStageCleanResultVO> {
    @Resource
    private LeadsRatingSupport leadsRatingSupport;

    @Override
    public boolean support(LeadsStageCleanRequestDTO dto) {
        return Objects.equals(LeadsCleanStageEnum.CUSTOMER_SERVICE_WORK_TIME.getCode(), dto.getCleanStageType());
    }

    @Override
    public LeadsStageCleanResultVO process(LeadsStageCleanRequestDTO dto, LeadsCleanContext context) {
        boolean inWorkTime = inWorkTime(context);
        if (inWorkTime) {
            return LeadsStageCleanResultVO.create().continueProcess();
        }
        return LeadsStageCleanResultVO.create().endProcess(LeadsDispatchActionEnum.TO_RULE_STRATEGY_MERCHANT_SET);
    }

    private boolean inWorkTime(LeadsCleanContext context) {
        LeadsInfo leadsInfo = context.getLeadsInfo();
        // 执行业务逻辑
        LocalTime submitTime = leadsInfo.getSubmitTime().toLocalTime();
        // 工作时间为 8:00 到 20:00
        boolean inWorkTime = leadsRatingSupport.inWorkTime(submitTime);
        context.addCleanResultData("inWorkTime", inWorkTime);
        VariableContext.setVariable("inWorkTime", inWorkTime);
        return inWorkTime;
    }
}
