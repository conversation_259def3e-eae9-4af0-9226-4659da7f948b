package com.yaowu.hera.domain.order.biz.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.ListUtil;
import cn.hutool.core.lang.TypeReference;
import cn.hutool.json.JSONUtil;
import com.freedom.feign.utils.FeignInvokeUtils;
import com.freedom.mq.annotation.MessageHandler;
import com.freedom.mq.model.msg.MqContextMsg;
import com.freedom.web.model.resp.BasePage;
import com.freedom.web.model.resp.BaseResult;
import com.google.common.collect.Lists;
import com.yaowu.customerserviceapi.feign.customer.IRemoteCustomerPushFeign;
import com.yaowu.customerserviceapi.feign.customer.IRemoteCustomerServiceMerchantCustomerIdentityFeign;
import com.yaowu.customerserviceapi.model.dto.customer.RemoteCustomerPushDTO;
import com.yaowu.hera.config.nacos.CommonConfig;
import com.yaowu.hera.config.nacos.OnlineSalesChannelLeaderConfig;
import com.yaowu.hera.domain.business.biz.IBusinessBizService;
import com.yaowu.hera.domain.business.biz.IClueConvertTraceLogBizService;
import com.yaowu.hera.domain.business.service.batis.service.IBusinessInfoService;
import com.yaowu.hera.domain.common.biz.WarnNoticeBizService;
import com.yaowu.hera.domain.order.biz.IOrderBizService;
import com.yaowu.hera.domain.sell.biz.ISellClueBizService;
import com.yaowu.hera.domain.tag.biz.ITagBizService;
import com.yaowu.hera.model.bo.OrderCancelMqEventBO;
import com.yaowu.hera.model.bo.OrderSignFinishMqEventBO;
import com.yaowu.hera.model.bo.OrderTerminateMqEventBO;
import com.yaowu.hera.model.entity.business.BusinessInfo;
import com.yaowu.hera.model.entity.business.OnlineSalesChannelLeader;
import com.yaowu.hera.utils.constants.AppPushTemplateCodeConstants;
import com.yaowu.hera.utils.constants.TagConstants;
import com.yaowu.heraapi.enums.business.BusinessSourceEnum;
import com.yaowu.heraapi.enums.business.BusinessStatusEnum;
import com.yaowu.heraapi.enums.business.ClueConvertStatusEnum;
import com.yaowu.heraapi.model.dto.business.CreateClueConvertLogDTO;
import com.yaowu.heraapi.model.dto.business.RemoteBusinessDefeatDTO;
import com.yaowu.heraapi.model.pojo.common.RemoteOmkChannel;
import com.yaowu.omsapi.feign.ICustomerOrderFeign;
import com.yaowu.omsapi.feign.IOnlineSalesCustomerOrderFeign;
import com.yaowu.omsapi.feign.IRemoteOmsContractFeign;
import com.yaowu.omsapi.feign.IRemoteOmsOrderFeign;
import com.yaowu.omsapi.model.dto.customer.CustomerIdsDTO;
import com.yaowu.omsapi.model.dto.customerorder.OmsOnlineSalesOrderCreateDTO;
import com.yaowu.omsapi.model.dto.customerorder.RemoteCustomerOrderDeviceDTO;
import com.yaowu.omsapi.model.dto.customerorder.RemoteOmsOrderCreateDTO;
import com.yaowu.omsapi.model.dto.customerorder.RemoteOrderSimpleQryDTO;
import com.yaowu.omsapi.model.vo.common.OnlineSalesSimpleCustomerOrderVO;
import com.yaowu.omsapi.model.vo.customerorder.*;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.time.LocalDateTime;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.yaowu.heraapi.enums.business.BusinessSourceEnum.ONLINE_SALES;

/**
 * <AUTHOR>
 * @date 2023/2/12
 */
@Slf4j
@Service
public class OrderBizServiceImpl implements IOrderBizService {

    @Autowired
    private IRemoteOmsContractFeign remoteOmsContractFeign;

    @Autowired
    private IRemoteOmsOrderFeign remoteOmsOrderFeign;

    @Autowired
    private ICustomerOrderFeign customerOrderFeign;

    @Autowired
    private IOnlineSalesCustomerOrderFeign onlineSalesCustomerOrderFeign;

    @Autowired
    private IBusinessInfoService businessInfoService;

    @Autowired
    private IBusinessBizService businessBizService;

    @Autowired
    private ITagBizService tagBizService;

    @Autowired
    private ISellClueBizService sellClueBizService;

    @Autowired
    private IRemoteCustomerServiceMerchantCustomerIdentityFeign customerIdentityFeign;

    @Autowired
    private IRemoteOmsOrderFeign orderFeign;

    @Autowired
    private IClueConvertTraceLogBizService clueConvertTraceLogBizService;

    @Autowired
    private IRemoteCustomerPushFeign customerPushFeign;

    @Autowired
    private CommonConfig commonConfig;

    @Autowired
    private OnlineSalesChannelLeaderConfig onlineSalesChannelLeaderConfig;

    @Autowired
    private WarnNoticeBizService warnNoticeBizService;

    @Autowired
    private ThreadPoolTaskExecutor commonExecutor;


    /**
     * 查询合同
     *
     * @param customerSignSubjectId
     * @param merchantId
     * @param storeSignSubjectId
     */
    @Override
    public RemoteOmsContractVO getContract(Long customerSignSubjectId, Long merchantId, Long storeSignSubjectId) {
        BaseResult<List<RemoteOmsContractVO>> baseResult = remoteOmsContractFeign.getContractList(customerSignSubjectId, merchantId, storeSignSubjectId);
        List<RemoteOmsContractVO> list = FeignInvokeUtils.convertList(baseResult, RemoteOmsContractVO.class);
        return CollectionUtils.isEmpty(list) ? null : list.get(0);
    }

    /**
     * 创建订单
     *
     * @param remoteOmsOrderCreateDTO
     * @return
     */
    @Override
    public RemoteOmsOrderCreateVO createOrder(RemoteOmsOrderCreateDTO remoteOmsOrderCreateDTO) {
        log.info("添加联营订单，参数：{}", JSONUtil.toJsonStr(remoteOmsOrderCreateDTO));
        BaseResult<RemoteOmsOrderCreateVO> baseResult = remoteOmsOrderFeign.createOrder(remoteOmsOrderCreateDTO);
        return FeignInvokeUtils.convert(baseResult, RemoteOmsOrderCreateVO.class);
    }

    @Override
    public RemoteOmsOrderCreateVO createOnlineSalesOrder(OmsOnlineSalesOrderCreateDTO dto) {
        log.info("添加电销订单，参数：{}", JSONUtil.toJsonStr(dto));
        BaseResult<RemoteOmsOrderCreateVO> baseResult = onlineSalesCustomerOrderFeign.createOrder(dto);
        return FeignInvokeUtils.convert(baseResult, RemoteOmsOrderCreateVO.class);
    }

    /**
     * 删除订单
     *
     * @param orderId
     * @return
     */
    @Override
    public Boolean deleteOrder(Long orderId) {
        BaseResult<Boolean> baseResult = customerOrderFeign.deleteOrder(orderId);
        return FeignInvokeUtils.convert(baseResult, Boolean.class);
    }

    @Override
    public RemoteCustomerOrderVO getOrderBasic(Long orderId) {
        BaseResult<RemoteCustomerOrderVO> baseResult = customerOrderFeign.getOrderBasic(orderId);
        return FeignInvokeUtils.convert(baseResult, RemoteCustomerOrderVO.class);
    }

    /**
     * 根据手机号查询订单
     *
     * @param phone
     * @return
     */
    @Override
    public List<RemoteOrderSimpleVO> getByPhone(String phone) {
        BaseResult<List<Long>> baseResult = customerIdentityFeign.listMerchantCustomerIdentityIdsByPhone(phone);
        List<Long> customerIds = FeignInvokeUtils.convertList(baseResult, Long.class);
        if (CollUtil.isEmpty(customerIds)) {
            return new ArrayList<>();
        }
        CustomerIdsDTO customerIdsDTO = new CustomerIdsDTO();
        customerIdsDTO.setCustomerIds(customerIds);
        BaseResult<List<RemoteOrderSimpleVO>> orderBaseResult = orderFeign.getOrderByCustomerIds(customerIdsDTO);
        return FeignInvokeUtils.convertList(orderBaseResult, RemoteOrderSimpleVO.class);
    }

    /**
     * 根据客户id和创建时间查询订单
     *
     * @param customerIds
     * @param createTimeStart
     * @param createTimeEnd
     * @return
     */
    @Override
    public List<RemoteOrderSimpleVO> getByCustomerIdsAndCreateTime(List<Long> customerIds,
                                                                   LocalDateTime createTimeStart,
                                                                   LocalDateTime createTimeEnd) {
        RemoteOrderSimpleQryDTO dto = new RemoteOrderSimpleQryDTO();
        dto.setCustomerIds(customerIds);
        dto.setCreateTimeStart(createTimeStart);
        dto.setCreateTimeEnd(createTimeEnd);
        BaseResult<List<RemoteOrderSimpleVO>> baseResult = orderFeign.getOrderBySimpleQry(dto);
        return FeignInvokeUtils.convertList(baseResult, RemoteOrderSimpleVO.class);
    }

    /**
     * 订单签约完成 回调监听
     *
     * @param msg
     */
    @MessageHandler(topic = "h-oms", routingKey = "oms_order_sign_finish_routing_key")
    @Transactional(rollbackFor = Exception.class)
    public void orderSignFinishMonitor(MqContextMsg msg) {
        log.info("h-hera收到订单签约完成事件回调，回调信息：{}", JSONUtil.toJsonStr(msg));
        OrderSignFinishMqEventBO bo = JSONUtil.toBean(JSONUtil.toJsonStr(msg.getPayload()), new TypeReference<>() {
        }, false);
        BusinessInfo business = businessInfoService.getBusinessByOrderId(bo.getOrderId());
        if (business == null) {
            return;
        }
        BusinessStatusEnum status = business.getStatus();
        if (status != BusinessStatusEnum.WAIT_SIGN && status != BusinessStatusEnum.OMK_CREATE_ORDER) {
            log.error("订单签约完成 回调监听，商机状态不为待签约或OMK创建订单，不做处理，商机id：{}", business.getId());
            return;
        }
        // 更新商机状态为已签约
        businessBizService.updateStatus(business.getId(), status, BusinessStatusEnum.SIGNED);
        log.warn("订单签约成功回调函数商机的测试在hera中发送消息,{}",business);
        if (Objects.nonNull(business.getSource()) && BusinessSourceEnum.ONLINE_SALES == business.getSource()) {
            log.warn("订单签约成功回调函数商机的测试在hera中发送消息,{}", business);
            pushTmkBd(bo.getOrderId(), business, AppPushTemplateCodeConstants.H_WX_BOT_ORDER_SIGN_FINISH_SEND_TO_TMK_BD);
            pushSignedOrderExtraTmkBd(bo.getOrderId(), business, AppPushTemplateCodeConstants.H_WX_BOT_ORDER_SIGN_FINISH_SEND_ORDER_DETAIL_URL_TO_TMK_BD);
        }
        // 摘标（商机）
        pickBusinessTag(business);

        sellClueBizService.orderSign(bo.getOrderId(), bo.getCurrentTime());
    }

    private void pushSignedOrderExtraTmkBd(Long orderId, BusinessInfo businessInfo, String templateCode) {
        OnlineSalesSimpleCustomerOrderVO onlineSalesSimpleCustomerOrderVO = FeignInvokeUtils.convert(onlineSalesCustomerOrderFeign.getOnlineSalesOrderByBusinessId(businessInfo.getId()), OnlineSalesSimpleCustomerOrderVO.class);
        RemoteCustomerPushDTO pushDTO = new RemoteCustomerPushDTO();
        pushDTO.setCustomerId(onlineSalesSimpleCustomerOrderVO.getCustomerId());
        pushDTO.setTemplateCode(templateCode);
        Map<String, String> templateParams = new HashMap<>();
        handleTemplateParams(templateParams, orderId,onlineSalesSimpleCustomerOrderVO);
        pushDTO.setTemplateParams(templateParams);
        pushDTO.setExtParams(Collections.singletonMap("msgtype", "markdown"));
        pushDTO.setChannelManagerPhoneList(getClueTicketChannelLeaderInfo(businessInfo.getOmkChannel()));
        log.warn("订单签约成功回调函数商机的测试在hera中发送消息,{}", pushDTO);
        customerPushFeign.pushTmkBd(pushDTO);
    }

    /**
     * 推送电销BD（企微机器人）
     *
     * @param orderId
     * @param businessInfo
     * @param templateCode
     */
    private void pushTmkBd(Long orderId, BusinessInfo businessInfo, String templateCode) {
        OnlineSalesSimpleCustomerOrderVO onlineSalesSimpleCustomerOrderVO = FeignInvokeUtils.convert(onlineSalesCustomerOrderFeign.getOnlineSalesOrderByBusinessId(businessInfo.getId()), OnlineSalesSimpleCustomerOrderVO.class);
        RemoteCustomerPushDTO pushDTO = new RemoteCustomerPushDTO();
        pushDTO.setCustomerId(onlineSalesSimpleCustomerOrderVO.getCustomerId());
        pushDTO.setTemplateCode(templateCode);
        pushDTO.setTemplateParams(Collections.singletonMap("customerName", onlineSalesSimpleCustomerOrderVO.getCustomerName()));
        pushDTO.setExtParams(Collections.singletonMap("msgtype", "text"));
        pushDTO.setChannelManagerPhoneList(getClueTicketChannelLeaderInfo(businessInfo.getOmkChannel()));
        log.warn("订单签约成功回调函数商机的测试在hera中发送消息,{}", pushDTO);
        customerPushFeign.pushTmkBd(pushDTO);
    }

    private void handleTemplateParams(Map<String, String> templateParams, Long orderId, OnlineSalesSimpleCustomerOrderVO onlineSalesSimpleCustomerOrderVO) {
        StringBuilder url = new StringBuilder();
        url.append(onlineSalesChannelLeaderConfig.getOnlineSalesOrderDetailUrl()).append(orderId);
        templateParams.putIfAbsent("url", url.toString());
        templateParams.put("customerName", onlineSalesSimpleCustomerOrderVO.getCustomerName());
    }

    private List<String> getClueTicketChannelLeaderInfo(String omkChannelCode) {
        List<RemoteOmkChannel> omkChannels = commonConfig.getOmkChannels();
        Map<String, RemoteOmkChannel> omkChannelsMap = omkChannels.stream()
                .collect(Collectors.toMap(RemoteOmkChannel::getCode, Function.identity()));
        RemoteOmkChannel remoteOmkChannel = omkChannelsMap.get(omkChannelCode);
        String channel = remoteOmkChannel.getChannel();
        Map<String, List<OnlineSalesChannelLeader>> onlineSalesChannelLeaderMap = onlineSalesChannelLeaderConfig.getOnlineSalesChannelLeaderMap();
        return  onlineSalesChannelLeaderMap.getOrDefault(channel, Lists.newArrayList()).stream()
                .map(OnlineSalesChannelLeader::getPhone).collect(Collectors.toList());
    }

    /**
     * 订单取消 回调监听
     */
    @MessageHandler(topic = "h-oms", routingKey = "oms_order_cancel_routing_key")
    @Transactional(rollbackFor = Exception.class)
    public void orderCancelMonitor(MqContextMsg msg) {
        log.info("h-hera收到订单取消事件回调，回调信息：{}", JSONUtil.toJsonStr(msg));
        OrderCancelMqEventBO bo = JSONUtil.toBean(JSONUtil.toJsonStr(msg.getPayload()),
                new TypeReference<OrderCancelMqEventBO>() {}, false);
        BusinessInfo business = businessInfoService.getBusinessByOrderId(bo.getOrderId());
        if (business == null) {
            return;
        }

        // 2023-12-04 原oms订单取消订单发送mq消息，会判断订单为电销订单时，不进行发送，此次调整，把oms判断逻辑调整到hera
        if (business.getSource() != ONLINE_SALES) {
            RemoteBusinessDefeatDTO dto = new RemoteBusinessDefeatDTO();
            dto.setId(business.getId());
            dto.setDefeatId(bo.getDefeatId());
            dto.setDefeatName(bo.getDefeatName());
            dto.setDefeatSubId(bo.getDefeatSubId());
            dto.setDefeatSubName(bo.getDefeatSubName());
            dto.setDefeatCauseDesc(bo.getDefeatCauseDesc());
            dto.setOrderCancel(true);
            businessBizService.defeat(dto);
        }
        // todo 电销线索记录日志消息     电销线索
        if (Objects.equals(business.getSource(), ONLINE_SALES)){
        //电销线索 未签约订单-》 取消订单
            saveOrderToCancelOrderTraceLog(business, ClueConvertStatusEnum.AWAITING_SIGNING_ORDER, ClueConvertStatusEnum.CANCELED_ORDER);
        }
        String defeatReason = bo.getDefeatName() + "-" + bo.getDefeatSubName() + "-" + bo.getDefeatCauseDesc();
        sellClueBizService.cancelOrder(business.getOrderId(), defeatReason, bo.getCurrentTime());
    }

    /**
     * 订单作废消息监听（目前仅电销订单支持作废）
     */
    @MessageHandler(topic = "h-oms", routingKey = "oms_order_terminate_routing_key")
    @Transactional(rollbackFor = Exception.class)
    public void orderTerminateMonitor(MqContextMsg msg) {
        log.info("oms_order_terminate_routing_key, msg: {}", JSONUtil.toJsonStr(msg));
        OrderTerminateMqEventBO event = JSONUtil.toBean(JSONUtil.toJsonStr(msg.getPayload()),
                new TypeReference<OrderTerminateMqEventBO>() {}, false);

        BusinessInfo businessInfo = businessInfoService.getBusinessByOrderId(event.getOrderId());
        if (businessInfo == null) {
            return;
        }
        if (businessInfo.getSource() == ONLINE_SALES) {
            saveOrderToCancelOrderTraceLog(businessInfo, ClueConvertStatusEnum.SIGNED_ORDER, ClueConvertStatusEnum.TERMINATED_ORDER);
        }
        String defeatReason = event.getDefeatName() + "-" + event.getDefeatSubName() + "-" + event.getDefeatCauseDesc();
        sellClueBizService.cancelOrder(businessInfo.getOrderId(), defeatReason, event.getOpTime());
    }

    /**
     * 保存电销线索转化日志记录信息 待签约的订单 ——》取消订单
     */
    private void saveOrderToCancelOrderTraceLog(BusinessInfo businessInfo, ClueConvertStatusEnum source, ClueConvertStatusEnum target) {
        CreateClueConvertLogDTO remoteCreateClueConvertLogDTO = new CreateClueConvertLogDTO();
        remoteCreateClueConvertLogDTO.setBusinessInfoId(businessInfo.getId());
        remoteCreateClueConvertLogDTO.setSourceStatus(source);
        remoteCreateClueConvertLogDTO.setTargetStatus(target);
        remoteCreateClueConvertLogDTO.setSourceRecordingTime(businessInfo.getUpdateTime());
        remoteCreateClueConvertLogDTO.setTargetRecordingTime(LocalDateTime.now());
        remoteCreateClueConvertLogDTO.setSalesBdId(businessInfo.getSalesBdId());
        boolean createClueConvertLog = clueConvertTraceLogBizService.createClueConvertLog(remoteCreateClueConvertLogDTO);
        log.info("打印电销线索转化日志 from (待签约的订单) to 取消订单,参数{},结果,{}", remoteCreateClueConvertLogDTO, createClueConvertLog);
    }
    private void pickBusinessTag(BusinessInfo business) {
        Integer count = businessInfoService.count(business.getCustomerId(), business.getId(),
                ListUtil.toList(
                        BusinessStatusEnum.INCOMPLETE,
                        BusinessStatusEnum.WAIT_APPROVAL,
                        BusinessStatusEnum.APPROVED,
                        BusinessStatusEnum.APPROVAL_FAILED,
                        BusinessStatusEnum.WAIT_SIGN,
                        BusinessStatusEnum.INCOMPLETE));
        if (count == 0) {
            // 摘标
            tagBizService.pick(business.getCustomerId().toString(), TagConstants.CUSTOMER_HAVE_BUSINESS_TAG_CODE);
        }
    }

    @Override
    public BasePage<RemoteCustomerOrderDeviceVO> orderDevicePage(RemoteCustomerOrderDeviceDTO dto) {
        BaseResult<BasePage<RemoteCustomerOrderDeviceVO>> baseResult = customerOrderFeign.orderDevicePage(dto);
        return FeignInvokeUtils.convertPage(baseResult, RemoteCustomerOrderDeviceVO.class);
    }
}
