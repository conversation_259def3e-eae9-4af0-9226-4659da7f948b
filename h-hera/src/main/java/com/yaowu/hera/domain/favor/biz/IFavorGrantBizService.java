package com.yaowu.hera.domain.favor.biz;

import com.yaowu.hera.model.entity.favor.FavorUserGrantRecord;
import com.yaowu.heraapi.model.dto.favor.RemoteFavorStockGrantWechatSignDTO;
import com.yaowu.heraapi.model.dto.favor.RemoteFavorStockWechatGrantedDTO;
import com.yaowu.heraapi.model.vo.favor.RemoteFavorStockGrantWechatSignVO;
import com.yaowu.settle.api.model.dto.pay.RemotePayResultVoucherDTO;

import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * <AUTHOR>
 * @since 2023/7/29 18:22
 **/
public interface IFavorGrantBizService {

    /**
     * 获取发券的签名，用于小程序插件领券
     *
     * @param dto RemoteFavorStockGrantSignDTO
     * @return 发券的签名VO
     */
    RemoteFavorStockGrantWechatSignVO grantWechatSign(RemoteFavorStockGrantWechatSignDTO dto);

    /**
     * 小程序上报领券结果
     *
     * @param dto RemoteFavorStockWechatGrantedDTO
     * @return 小程序上报领券结果VO
     */
    boolean wechatStockGranted(RemoteFavorStockWechatGrantedDTO dto);

    /**
     * 处理优惠券消费事件
     *
     * @param vouchers 优惠券的集合
     */
    void dealCouponConsumed(List<RemotePayResultVoucherDTO> vouchers);

    /**
     * 查询绑定的人，获取了几张优惠券
     *
     * @param relatedIds 绑定的关系id
     * @return 绑定的人，获取了几张优惠券
     */
    Map<Long, Long> getGrantedRecordCount(Set<Long> relatedIds);

    /**
     * 查询绑定的人，获取了优惠券信息
     *
     * @param relatedIds 绑定的关系id
     * @return 绑定的人，获取了几张优惠券
     */
    Map<Long, List<FavorUserGrantRecord>> getGrantedRecordInfo(Set<Long> relatedIds);
}
