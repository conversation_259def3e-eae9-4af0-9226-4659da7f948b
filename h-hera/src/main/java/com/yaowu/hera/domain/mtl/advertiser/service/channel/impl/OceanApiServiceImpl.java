package com.yaowu.hera.domain.mtl.advertiser.service.channel.impl;

import com.alibaba.fastjson.JSONObject;
import com.freedom.invoke.http.HttpClientInvokeProvider;
import com.freedom.redis.utils.RedisHelper;
import com.freedom.toolscommon.utils.JacksonUtils;
import com.yaowu.hera.config.nacos.mtl.AdvertiserNoticeConfig;
import com.yaowu.hera.domain.common.biz.WarnNoticeBizService;
import com.yaowu.hera.domain.mtl.advertiser.service.channel.BaseAdvertiserChannelApiService;
import com.yaowu.hera.domain.mtl.advertiser.service.channel.IOceanApiService;
import com.yaowu.hera.enums.mtl.advertiser.AdPlatformEnum;
import com.yaowu.hera.model.pojo.mtl.advertiser.param.*;
import com.yaowu.hera.model.pojo.mtl.advertiser.response.*;
import com.yaowu.hera.model.pojo.mtl.advertiser.response.OceanTokenResponseModel.DataBean;
import com.yaowu.hera.utils.StringUtil;
import com.yaowu.hera.utils.ValidatorUtil;
import com.yaowu.hera.utils.convertor.advertiser.AdvertiserConvertor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.TimeUnit;

import static com.yaowu.hera.enums.mtl.advertiser.AdPlatformEnum.OCEAN;

/**
 * @Author: liuzhenpeng
 * @Date: 2025/1/22 11:27
 */
@Slf4j
@Service
public class OceanApiServiceImpl extends BaseAdvertiserChannelApiService implements IOceanApiService {

    @Value("${advertiser.ocean.clientId}")
    private Long appId;

    @Value("${advertiser.ocean.clientSecret}")
    private String secret;

    private final static String OCEAN_CHANNEL = "OCEAN_CHANNEL";
    private final static String OCEAN_CHANNEL_REFRESH = "OCEAN_CHANNEL_REFRESH";
    private final static String ACCESS_TOKEN = "Access-Token";
    private final static String ACCESS_TOKEN_API_URL = "https://ad.oceanengine.com/open_api/oauth2/access_token/";
    private final static String REFRESH_ACCESS_TOKEN_API_URL = "https://api.oceanengine.com/open_api/oauth2/refresh_token/";
    private final static String OAUTH2_ADVERTISER_GET_API_URL = "https://ad.oceanengine.com/open_api/oauth2/advertiser/get/";
    private final static String ADVERTISER_LIST_API_URL = "https://ad.oceanengine.com/open_api/2/customer_center/advertiser/list/";

    private final static String REPORT_ADVERTISER_API_URL = "https://ad.oceanengine.com/open_api/2/report/advertiser/get/";
    private final static String PROJECT_LIST_API_URL = "https://api.oceanengine.com/open_api/v3.0/project/list/";
    private final static String PROMOTION_LIST_API_URL = "https://api.oceanengine.com/open_api/v3.0/promotion/list/";

    @Autowired
    private HttpClientInvokeProvider httpClientInvokeProvider;

    @Autowired
    private RedisHelper redisHelper;

    @Autowired
    private WarnNoticeBizService warnNoticeBizService;

    @Autowired
    private AdvertiserNoticeConfig noticeConfig;

    @Override
    public  OceanTokenResponseModel.DataBean getToken(OceanTokenRequestModel dto){
        ValidatorUtil.validate(dto);
        String apiName = "获取token";
        OceanTokenResponseModel response = null;
        boolean success = false;
        JSONObject result = null;
        try {
            Map<String, Object> params = new HashMap<>();
            params.put("app_id",appId);
            params.put("secret",secret);
            params.put("auth_code",dto.getAuthCode());
            params.put("grant_type","auth_code");

            LinkedHashMap<String, String> headers = new LinkedHashMap<>();
            params.put("Content-Type","application/json");

            result = httpClientInvokeProvider.post().json(ACCESS_TOKEN_API_URL,params,headers).execute();
            response = JSONObject.parseObject(result.toJSONString(), OceanTokenResponseModel.class);
            if(validResult(response)) {
                success = true;
                OceanTokenResponseModel.DataBean data = JacksonUtils.convertToObj(response.getData(),OceanTokenResponseModel.DataBean.class);
                // 巨量的accessToken有效期24小时
                cacheToken(OCEAN_CHANNEL,data.getAccess_token(), 24, TimeUnit.HOURS);
                cacheToken(OCEAN_CHANNEL_REFRESH, data.getRefresh_token(), data.getRefresh_token_expires_in(), TimeUnit.SECONDS);
                return data;
            }
        } catch (Exception e) {
            log.warn(apiName+"失败",e);
        }
        finally {
            saveExtract(OCEAN,apiName,ACCESS_TOKEN_API_URL,JacksonUtils.toJsonStr(dto),JacksonUtils.toJsonStr(result),success);
        }
        return null;
    }

    public DataBean refreshToken(String refreshToken) {
        String apiName = "刷新Token";
        OceanTokenResponseModel response = null;
        boolean success = false;

        try {
            Map<String, Object> params = new HashMap<>();
            params.put("app_id", appId);
            params.put("secret", secret);
            params.put("refresh_token", refreshToken);

            LinkedHashMap<String, String> headers = new LinkedHashMap<>();
            headers.put("Content-Type", "application/json");

            JSONObject result = httpClientInvokeProvider.post()
                    .json(REFRESH_ACCESS_TOKEN_API_URL, params, headers)
                    .execute();

            response = JSONObject.parseObject(result.toJSONString(), OceanTokenResponseModel.class);
            if (validResult(response)) {
                success = true;
                OceanTokenResponseModel.DataBean data = JacksonUtils.convertToObj(
                        response.getData(),
                        OceanTokenResponseModel.DataBean.class
                );

                // 同时缓存新旧token（根据文档建议保留旧token直到新token生效）
                cacheToken(OCEAN_CHANNEL, data.getAccess_token(), data.getExpires_in(), TimeUnit.SECONDS);
                cacheToken(OCEAN_CHANNEL_REFRESH, data.getRefresh_token(), data.getRefresh_token_expires_in(), TimeUnit.SECONDS);
                return data;
            }
        } catch (Exception e) {
            log.error("{}失败 - RequestID: {}", apiName, response != null ? response : "N/A", e);
        } finally {
            // 可在此添加监控逻辑
        }
        return null;
    }

    @Override
    public OceanGetOauthAdvertisersResponseModel.DataBean getOauthAdvertisers() {
        String apiName = "获取已授权账户";
        OceanGetOauthAdvertisersResponseModel response = null;
        boolean success = false;
        try {
            Map<String, String> params = new HashMap<>();
            params.put("client_id",appId.toString());
            params.put("client_secret",secret);
            params.put("access_token",getToken(OCEAN_CHANNEL));

            JSONObject result = httpClientInvokeProvider.get().form(OAUTH2_ADVERTISER_GET_API_URL,params).execute();
            response = JSONObject.parseObject(result.toJSONString(), OceanGetOauthAdvertisersResponseModel.class);
            if(validResult(response)) {
                success = true;
                // 返回
                return JacksonUtils.convertToObj(response.getData(), OceanGetOauthAdvertisersResponseModel.DataBean.class);
            }
        } catch (Exception e) {
            log.warn(apiName+"失败",e);
        }
        finally {
            saveExtract(OCEAN,apiName,OAUTH2_ADVERTISER_GET_API_URL,JacksonUtils.toJsonStr(""),JacksonUtils.toJsonStr(response),success);
        }
        return null;
    }

    @Override
    public OceanAdvertiserListResponseModel.DataBean getAdvertiserList(OceanAdvertiserListRequestModel dto) {
        String apiName = "获取工作台下账户列表";
        OceanAdvertiserListResponseModel response = null;
        boolean success = false;
        try {
            Map<String, String> params = new HashMap<>();
            params.put("cc_account_id",dto.getCcAccountId().toString());
            params.put("account_source","AD");// 账户类型，允许值：AD 巨量广告广告主账号（默认）、ENTERPRISE企业号 、LOCAL 本地推
            params.put("page",dto.getPage().toString());
            params.put("page_size",dto.getPageSize().toString());

            LinkedHashMap<String, String> headers = getHeader(getToken(OCEAN_CHANNEL));

            JSONObject result = httpClientInvokeProvider.get().form(ADVERTISER_LIST_API_URL,params,headers).execute();
            response = JSONObject.parseObject(result.toJSONString(), OceanAdvertiserListResponseModel.class);
            if(validResult(response)) {
                success = true;
                // 返回
                return JacksonUtils.convertToObj(response.getData(), OceanAdvertiserListResponseModel.DataBean.class);
            }
        } catch (Exception e) {
            log.warn(apiName+"失败",e);
        }
        finally {
            saveExtract(OCEAN,apiName,ADVERTISER_LIST_API_URL,JacksonUtils.toJsonStr(dto),JacksonUtils.toJsonStr(response),success);
        }
        return null;
    }

    @Override
    public OceanGetProjectListResponseModel.DataBean getProjectList(OceanGetProjectListRequestModel dto) {

        String apiName = "获取项目列表";
        OceanGetProjectListResponseModel response = null;
        boolean success = false;
        try {
            Map<String, String> params = new HashMap<>();
            params.put("advertiser_id",dto.getAdvertiserId().toString());
            params.put("page",dto.getPage().toString());
            params.put("page_size",dto.getPageSize().toString());

            LinkedHashMap<String, String> headers = getHeader(getToken(OCEAN_CHANNEL));

            JSONObject result = httpClientInvokeProvider.get().form(PROJECT_LIST_API_URL,params,headers).execute();
            response = JSONObject.parseObject(result.toJSONString(), OceanGetProjectListResponseModel.class);
            if(validResult(response)) {
                success = true;
                // 返回
                return JacksonUtils.convertToObj(response.getData(), OceanGetProjectListResponseModel.DataBean.class);
            }
        } catch (Exception e) {
            log.warn(apiName+"失败",e);
        }
        finally {
            saveExtract(OCEAN,apiName,PROJECT_LIST_API_URL,JacksonUtils.toJsonStr(dto),JacksonUtils.toJsonStr(response),success);
        }
        return null;
    }

    @Override
    public OceanGetPromotionListResponseModel.DataBean getPromotionList(OceanGetPromotionListRequestModel dto) {
        // 广告列表接口已经返回了广告详情信息
        String apiName = "获取广告列表";
        OceanGetPromotionListResponseModel response = null;
        boolean success = false;
        try {
            Map<String, String> params = new HashMap<>();
            params.put("advertiser_id", dto.getAdvertiserId().toString());
            params.put("page", dto.getPage().toString());
            params.put("page_size", dto.getPageSize().toString());
            if (dto.getFiltering() != null) {
                params.put("filtering", JacksonUtils.toJsonStr(dto.getFiltering()));
            }

            LinkedHashMap<String, String> headers = getHeader(getToken(OCEAN_CHANNEL));

            JSONObject result = httpClientInvokeProvider.get().form(PROMOTION_LIST_API_URL,params,headers).execute();
            response = JSONObject.parseObject(result.toJSONString(), OceanGetPromotionListResponseModel.class);
            if(validResult(response)) {
                success = true;
                // 返回
                return JacksonUtils.convertToObj(response.getData(), OceanGetPromotionListResponseModel.DataBean.class);
            }
        } catch (Exception e) {
            log.error(apiName+"失败",e);
        }
        finally {
            saveExtract(OCEAN,apiName,PROMOTION_LIST_API_URL,JacksonUtils.toJsonStr(dto),JacksonUtils.toJsonStr(response),success);
        }
        return null;
    }

    @Override
    public OceanReportAdvertiserResponseModel.DataBean getReportAdvertiser(OceanReportAdvertiserRequestModel dto) {
        String apiName = "广告主数据";
        OceanReportAdvertiserResponseModel response = null;
        boolean success = false;
        try {
            Map<String, String> params = new HashMap<>();
            params.put("advertiser_id",dto.getAdvertiserId().toString());
            params.put("start_date",dto.getStartDate());// 账户类型，允许值：AD 巨量广告广告主账号（默认）、ENTERPRISE企业号 、LOCAL 本地推
            params.put("end_date",dto.getEndDate());
            params.put("page",dto.getPage().toString());
            params.put("page_size",dto.getPageSize().toString());

            LinkedHashMap<String, String> headers = getHeader(getToken(OCEAN_CHANNEL));

            JSONObject result = httpClientInvokeProvider.get().form(REPORT_ADVERTISER_API_URL,params,headers).execute();
            response = JSONObject.parseObject(result.toJSONString(), OceanReportAdvertiserResponseModel.class);
            if(validResult(response)) {
                success = true;
                // 返回
                return JacksonUtils.convertToObj(response.getData(), OceanReportAdvertiserResponseModel.DataBean.class);
            }
        } catch (Exception e) {
            log.warn(apiName+"失败",e);
        }
        finally {
            saveExtract(OCEAN,apiName,REPORT_ADVERTISER_API_URL,JacksonUtils.toJsonStr(dto),JacksonUtils.toJsonStr(response),success);
        }
        return null;
    }

    @Override
    public Boolean existTokenFlag() {
        String token = getToken(OCEAN_CHANNEL);
        if (StringUtil.isBlank(token)){
            sendNewAccountNotice(OCEAN);
            return false;
        }
        return true;
    }


    private boolean validResult(OceanBaseResponseModel result){
        return result.getCode() == 0;
    }

    private LinkedHashMap<String, String> getHeader(String token) {
        long timestamp = System.currentTimeMillis();
        String timestampStr = String.valueOf(timestamp);
        LinkedHashMap<String, String> headers = new LinkedHashMap<>();
        headers.put(ACCESS_TOKEN, token);
        return headers;
    }


    @Override
    protected String getToken(String channel) {
        String key = getTokenKey(channel);
        Object obj = redisHelper.strGet(key);
        String token = obj != null ? obj.toString() : null;
        if (Objects.isNull(token) || StringUtil.isBlank(token)){
            String refreshKey = getTokenKey(OCEAN_CHANNEL_REFRESH);
            Object refreshObj = redisHelper.strGet(refreshKey);
            String refreshToken = refreshObj != null ? refreshObj.toString() : null;
            DataBean dataBean = refreshToken(refreshToken);
            if (Objects.isNull(dataBean)){
                return "";
            }
            return dataBean.getAccess_token();
        }
        return token;
    }

    private void sendNewAccountNotice(AdPlatformEnum platform) {
        String msg = AdvertiserConvertor.buildTokenExpiredNoticeMsg(platform, noticeConfig.getOceanAuthUrl());
        if (StringUtils.isBlank(msg)) {
            log.info("sendNewAccountNotice, msg is empty");
            return;
        }
        warnNoticeBizService.sendNoticeToWechat(msg, noticeConfig.getSmartOutboundAlertBotCode(), List.of());
    }
}
