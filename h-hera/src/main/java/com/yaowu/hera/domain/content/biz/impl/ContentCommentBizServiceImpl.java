package com.yaowu.hera.domain.content.biz.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.freedom.toolscommon.utils.StreamTools;
import com.freedom.web.exception.BusinessException;
import com.freedom.web.model.resp.BasePage;
import com.yaowu.hera.domain.common.biz.INoticeBizService;
import com.yaowu.hera.domain.content.biz.IContentCommentBizService;
import com.yaowu.hera.domain.content.service.batis.service.IContentCommentService;
import com.yaowu.hera.domain.content.service.batis.service.IContentFlowPostService;
import com.yaowu.hera.domain.content.service.batis.service.IContentLikeRecordService;
import com.yaowu.hera.domain.content.service.batis.service.ICustomerAppletInfoService;
import com.yaowu.hera.enums.common.wxmini.WxMiniEnum;
import com.yaowu.hera.enums.mtl.ContectTypeEnum;
import com.yaowu.hera.model.bo.content.ContentCommentBO;
import com.yaowu.hera.model.bo.content.ContentCommentQueryModel;
import com.yaowu.hera.model.entity.content.ContentComment;
import com.yaowu.hera.model.entity.content.ContentFlowPost;
import com.yaowu.hera.model.entity.content.ContentLikeRecord;
import com.yaowu.hera.model.entity.content.CustomerAppletInfo;
import com.yaowu.hera.remote.passport.IRemoteOpenIdService;
import com.yaowu.hera.utils.BizException;
import com.yaowu.hera.utils.convertor.content.ContentMapStruct;
import com.yaowu.heraapi.model.dto.content.RemoteAddReplyDTO;
import com.yaowu.heraapi.model.dto.content.RemoteCommentChangeStatusDTO;
import com.yaowu.heraapi.model.dto.content.RemoteContentCommentQueryPageDTO;
import com.yaowu.heraapi.model.dto.content.RemoteShieldCommentDTO;
import com.yaowu.heraapi.model.vo.content.RemoteContentCommentPageVO;
import com.yaowu.heraapi.model.vo.content.RemoteReplyContentCommentVO;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.function.Function;

import static com.yaowu.hera.utils.ErrorCode.*;
import static com.yaowu.hera.utils.constants.PushConstants.WX_MINI_NEWS_COMMENT_REPLY_TEMPLATE_CODE;

@Service
@Slf4j
public class ContentCommentBizServiceImpl implements IContentCommentBizService {

    @Resource
    private IContentCommentService contentCommentService;

    @Resource
    private ICustomerAppletInfoService customerAppletInfoService;

    @Resource
    private IContentLikeRecordService contentLikeRecordService;

    @Resource
    private IContentFlowPostService contentFlowPostService;

    @Resource
    private INoticeBizService noticeBizService;

    @Autowired
    private IRemoteOpenIdService remoteOpenIdService;

    @Autowired
    @Qualifier("commonExecutor")
    private ThreadPoolTaskExecutor commonThreadPool;

    @Override
    public ContentComment checkExist(Long bizId) {
        ContentComment comment = contentCommentService.getById(bizId);
        BusinessException.condition(comment == null, NO_EXIST_COMMENT.getCode(), NO_EXIST_COMMENT.getMsg());
        return comment;
    }

    @Override
    public BasePage<RemoteContentCommentPageVO> pageComment(RemoteContentCommentQueryPageDTO commentQueryPageDTO) {
        ContentCommentQueryModel contentCommentQueryModel = ContentMapStruct.INSTANCE.toContentCommentQueryModel(commentQueryPageDTO);
        if (commentQueryPageDTO.getUseType() != null && commentQueryPageDTO.getUseType() == 1) {
            contentCommentQueryModel.setCommentStatus(1);
        }
        contentCommentQueryModel.setOrderByField(" order by comment_time desc,id desc");
        boolean isApplet = Objects.nonNull(commentQueryPageDTO.getUseType()) && Objects.equals(commentQueryPageDTO.getUseType(), 1);
        Page<ContentComment> contentCommentPage = contentCommentService.pageContentComment(contentCommentQueryModel, commentQueryPageDTO.pageRequest(), isApplet);
        if (contentCommentPage == null || CollectionUtils.isEmpty(contentCommentPage.getRecords())) {
            return new BasePage<>(commentQueryPageDTO.pageRequest());
        }
        List<ContentComment> records = contentCommentPage.getRecords();
        //小程序-点赞记录
        Map<Long, ContentLikeRecord> likeRecordMap = getThisUserEffectLikeRecordMap(commentQueryPageDTO, records);
        Map<Long, CustomerAppletInfo> userMap = customerAppletInfoService.mapByUserIds(StreamTools.toSet(records,
                ContentComment::getUserId));
        //根据内容id 获取内容信息<内容id，entity> 得到作者id,和内容所有评论的数量
        Long authorId = mapContentFlowPost(Set.of(commentQueryPageDTO.getContentId()))
                .get(commentQueryPageDTO.getContentId()).getAuthorId();
        long commentSize = contentCommentService.countComment(commentQueryPageDTO.getContentId());
        //根据一级评论ids 获取评论的回复<一级评论id，entity>
        Map<Long, ContentComment> replyMap = mapHasReply(StreamTools.toSet(records, ContentComment::getId));
        //根据二级评论ids 获取上级评论内容<一级评论id，entity>
        Map<Long, ContentComment> parentCommentMap = mapParentComment(StreamTools.toSet(records, ContentComment::getParentCommentId));
        //根据上级评论的用户ids 获取上级评论的用户信息<一级评论用户id，entity>
        Map<Long, CustomerAppletInfo> parentUserMap = customerAppletInfoService.mapByUserIds(StreamTools.toSet(parentCommentMap.values(),
                ContentComment::getUserId));
        return BasePage.simpleConvert(contentCommentPage, m ->
                fillRemoteContentCommentPageVO(m, userMap, likeRecordMap, commentQueryPageDTO, replyMap
                        , parentCommentMap, parentUserMap, authorId, commentSize));
    }

    private RemoteContentCommentPageVO fillRemoteContentCommentPageVO(ContentComment m, Map<Long,
            CustomerAppletInfo> userMap, Map<Long, ContentLikeRecord> likeRecordMap,
                                                                      RemoteContentCommentQueryPageDTO commentQueryPageDTO, Map<Long, ContentComment> replyMap,
                                                                      Map<Long, ContentComment> parentCommentMap, Map<Long, CustomerAppletInfo> parentUserMap, Long authorId, long commentSize) {
        RemoteContentCommentPageVO remoteContentCommentPageVO = ContentMapStruct.INSTANCE.toRemoteContentCommentPageVO(m, userMap);
        //小程序
        Integer useType = commentQueryPageDTO.getUseType();
        if (useType != null && useType == 1 && commentQueryPageDTO.getUseId() != null) {
            //点赞标识
            remoteContentCommentPageVO.setLike(likeRecordMap.containsKey(m.getBizId()));
        }
        //判断一级评论是否有回复,如果有回复，则设置为true
        remoteContentCommentPageVO.setRepliedFlag(replyMap.containsKey(m.getId()));
        //填充一级评论的回复list，目前返回1条
        if (replyMap.containsKey(m.getId())) {
            ContentComment replyBO = replyMap.get(m.getId());
            remoteContentCommentPageVO.setReplyVO(fillReplyListVO(replyBO, authorId));
        }
        //判断用户id是否是作者,是则设置为true
        remoteContentCommentPageVO.setAuthorFlag(ObjectUtil.equal(authorId, m.getUserId()));
        //如果为二级评论，则填充上级评论id和上级评论内容
        if (ObjectUtil.equals(m.getBizType(), 2) && parentCommentMap.containsKey(m.getParentCommentId())) {
            ContentComment parentComment = parentCommentMap.get(m.getParentCommentId());
            remoteContentCommentPageVO.setParentCommentUserId(parentComment.getUserId());
            remoteContentCommentPageVO.setParentCommentUserName(parentUserMap.get(parentComment.getUserId())
                    .getNickName());
            remoteContentCommentPageVO.setParentCommentId(m.getParentCommentId());
            remoteContentCommentPageVO.setParentCommentContent(parentCommentMap.get(m.getParentCommentId())
                    .getCommentContent());
        }
        remoteContentCommentPageVO.setCommentSize(commentSize);
        return remoteContentCommentPageVO;
    }

    private RemoteReplyContentCommentVO fillReplyListVO(ContentComment replyBO, Long authorId) {
        if (Objects.isNull(replyBO)) {
            return null;
        }
        Map<Long, CustomerAppletInfo> userMap = customerAppletInfoService.mapByUserIds(Collections.singleton(replyBO.getUserId()));
        BizException.condition(userMap.isEmpty(), NO_EXIST_USER);
        RemoteReplyContentCommentVO remoteReplyContentCommentVO = ContentMapStruct.INSTANCE.toRemoteReplyContentCommentVO(replyBO, userMap);
        remoteReplyContentCommentVO.setReplyAuthorFlag(ObjectUtil.equal(authorId, replyBO.getUserId()));
        remoteReplyContentCommentVO.setReplyCommentTimeDesc(buildCommentTimeDesc(replyBO.getCommentTime()));
        return remoteReplyContentCommentVO;
    }

    private String buildCommentTimeDesc(LocalDateTime commentTime) {
        // 获取今天的日期
        LocalDate today = LocalDate.now();
        LocalDate yesterday = today.minusDays(1);
        LocalDate commentDate = commentTime.toLocalDate();

        // 判断评论时间是否是今天
        if (commentDate.equals(today)) {
            return "今天";
        }

        // 判断评论时间是否是昨天
        if (commentDate.equals(yesterday)) {
            return "昨天";
        }

        // 其他时间格式化为“XX月XX日 HH:mm”
        return commentTime.format(DateTimeFormatter.ofPattern("MM月dd日 HH:mm"));
    }

    private Map<Long, ContentLikeRecord> getThisUserEffectLikeRecordMap(RemoteContentCommentQueryPageDTO commentQueryPageDTO, List<ContentComment> records) {
        Map<Long, ContentLikeRecord> likeRecordMap = new HashMap<>();
        Integer useType = commentQueryPageDTO.getUseType();
        //小程序 & 有用户登录了
        if (useType != null && useType == 1 && commentQueryPageDTO.getUseId() != null) {
            Set<Long> bizIds = StreamTools.toSet(records, ContentComment::getBizId);
            List<ContentLikeRecord> thisUserLikeRecord = contentLikeRecordService.getThisUserLikeRecord(bizIds, commentQueryPageDTO.getUseId());
            likeRecordMap.putAll(StreamTools.toMap(thisUserLikeRecord, ContentLikeRecord::getBizId, Function.identity()));
        }
        return likeRecordMap;
    }

    @Override
    public Boolean shieldComment(RemoteShieldCommentDTO shieldCommentDTO) {
        //todo 枚举
        return changeStatus(RemoteCommentChangeStatusDTO.builder().commentId(shieldCommentDTO.getCommentId()).status(2)
                .build());
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean changeStatus(RemoteCommentChangeStatusDTO commentChangeStatusDTO) {
        ContentComment contentComment = checkExist(commentChangeStatusDTO.getCommentId());
        if (commentChangeStatusDTO.getStatus() == null) {
            return false;
        }
        if (Objects.equals(contentComment.getCommentStatus(), commentChangeStatusDTO.getStatus())) {
            return true;
        }
        ContentCommentBO contentCommentBO = new ContentCommentBO();
        contentCommentBO.setParentCommentIds(Set.of(contentComment.getId()));
        List<ContentComment> contentReplyComments = contentCommentService.listByCondition(contentCommentBO);
        if(!CollectionUtils.isEmpty(contentReplyComments)){
            contentReplyComments.forEach(item -> item.setCommentStatus(commentChangeStatusDTO.getStatus()));
        }
        boolean updateReply = CollectionUtils.isEmpty(contentReplyComments) ? true : contentCommentService.updateBatchById(contentReplyComments);

        contentComment.setCommentStatus(commentChangeStatusDTO.getStatus());
        boolean updateComment = contentCommentService.updateById(contentComment);
        BizException.condition(!updateComment || !updateReply, UPDATE_COMMENT_STATUS_FAILURE);
        return true;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Long addReply(RemoteAddReplyDTO dto) {
        BizException.condition(StringUtils.isBlank(dto.getComment()), COMMENT_CAN_NOT_EMPTY);
        ContentComment contentComment = fillContentComment(dto);
        //查询一级评论是否已经被回复
        ContentCommentBO contentCommentBO = new ContentCommentBO();
        contentCommentBO.setParentCommentIds(Set.of(dto.getParentCommentId()));
        contentCommentBO.setCommentStatus(1);
        BizException.condition(!CollectionUtils.isEmpty(contentCommentService.listByCondition(contentCommentBO)), PARENT_COMMENT_ALREADY_HAS_REPLY);
        boolean save = contentCommentService.save(contentComment);
        BizException.condition(!save, INNER_ERROR);
        //异步推送消息
        appletPushCommentReplyNotify(contentComment);
        return contentComment.getId();
    }

    private void checkCommentStatus(RemoteAddReplyDTO dto) {
        ContentCommentBO contentCommentBO = new ContentCommentBO();
        contentCommentBO.setParentCommentIds(Set.of(dto.getParentCommentId()));
    }

    private void appletPushCommentReplyNotify(ContentComment contentComment) {
        //根据上级评论ids 获取上级评论内容<一级评论的id，entity>
        Map<Long, ContentComment> map = mapParentComment(Set.of(contentComment.getParentCommentId()));
        ContentComment parentComment = map.get(contentComment.getParentCommentId());
        BizException.condition(Objects.isNull(parentComment), PARENT_COMMENT_CAN_NOT_EMPTY);
        BizException.condition(Objects.isNull(parentComment.getUserId()), NO_EXIST_USER);
        String openId = remoteOpenIdService.getOpenIdByBizUserId(WxMiniEnum.NEW_CUSTOMER_APPLETS.name(), parentComment.getUserId());
        if(StringUtils.isBlank(openId)){
            return;
        }
        ContentFlowPost contentFlowPost = mapContentFlowPost(Collections.singleton(contentComment.getBizId())).get(contentComment.getBizId());
        Map<String, String> templateParams = new HashMap<>();
        templateParams.put("commentTime", DateUtil.format(contentComment.getCommentTime(), "yyyy-MM-dd"));
        templateParams.put("title", contentFlowPost.getTitle());
        templateParams.put("articleId", String.valueOf(contentFlowPost.getId()));
        templateParams.put("type", String.valueOf(contentFlowPost.getPostType().getCode()));
        //异步发送消息
        commonThreadPool.execute(() -> {
            noticeBizService.sendWeChatAppletMsg(Set.of(openId), WX_MINI_NEWS_COMMENT_REPLY_TEMPLATE_CODE, templateParams);
        });
    }


    private ContentComment fillContentComment(RemoteAddReplyDTO dto) {
        ContentCommentBO contentCommentBO = new ContentCommentBO();
        contentCommentBO.setCommentIds(Set.of(dto.getParentCommentId()));
        List<ContentComment> contentComments = contentCommentService.listByCondition(contentCommentBO);
        BizException.condition(CollectionUtils.isEmpty(contentComments), PARENT_COMMENT_CAN_NOT_EMPTY);
        //一级评论信息
        ContentComment parentContentComment = contentComments.get(0);
        ContentComment addComment = new ContentComment();
        BizException.condition(!Objects.equals(parentContentComment.getCommentStatus(), 1), CURRENT_COMMENT_STATUS_ILLEGAL);
        addComment.setBizId(parentContentComment.getBizId());
        //对于管理端的评论，用户id为发布内容的作者的id和头像（channelType渠道类型=3）
        addComment.setUserId(mapContentFlowPost(Collections.singleton(parentContentComment.getBizId()))
                .get(parentContentComment.getBizId()).getAuthorId());
        addComment.setParentCommentId(dto.getParentCommentId());
        addComment.setCommentContent(dto.getComment());
        addComment.setBizType(ContectTypeEnum.COMMENT.getCode());//2-评论
        addComment.setChannelType(3);//3-管理端
        addComment.setCommentStatus(1);//1-有效
        LocalDateTime commentTime = Objects.isNull(dto.getCommentTime()) ? LocalDateTime.now() : dto.getCommentTime();
        addComment.setCommentTime(commentTime);
        return addComment;
    }

    /**
     * 对于一级评论id来说
     * 获取下级评论：状态为1 业务类型为评论的数据的上级评论id = 评论id的数据
     * 结果集可以为空
     *
     * @param commentIds 一级评论ids
     * @return <二级评论的上级评论id，entity>
     */
    private Map<Long, ContentComment> mapHasReply(Set<Long> commentIds) {
        if (CollectionUtils.isEmpty(commentIds)) {
            return new HashMap<>();
        }
        List<ContentComment> contentComment = contentCommentService.listByCondition(ContentCommentBO.builder()
                .parentCommentIds(commentIds).commentStatus(1).bizType(2).build());
        return StreamTools.toMap(contentComment, ContentComment::getParentCommentId, Function.identity());
    }

    /**
     * 对于（状态=1 类型为2评论）z评论id
     * 根据二级评论的上级评论id 查询 主键id=上级评论id的数据（状态为1 生效 业务为1文章的上级评论数据）
     * 一级评论 结果集不能为空
     *
     * @param ids 二级评论ids
     * @return <一级评论的id，entity>
     */
    private Map<Long, ContentComment> mapParentComment(Set<Long> ids) {
        if (ids.contains(0L)) {
            ids.remove(0L);//去掉默认值
        }
        if (CollectionUtils.isEmpty(ids)) {
            return new HashMap<>();
        }
        List<ContentComment> contentComment = contentCommentService.listByCondition(ContentCommentBO.builder()
                .commentIds(ids).bizType(1).build());
        BizException.condition(CollectionUtils.isEmpty(contentComment), PARENT_COMMENT_CAN_NOT_EMPTY);
        return StreamTools.toMap(contentComment, ContentComment::getId, Function.identity());
    }

    /**
     * 获取内容发布信息
     *
     * @param contentIds
     * @return
     */
    private Map<Long, ContentFlowPost> mapContentFlowPost(Set<Long> contentIds) {
        BizException.condition(CollUtil.isEmpty(contentIds), CONTENT_IDS_CAN_NOT_EMPTY);
        //查询内容作者
        Map<Long, ContentFlowPost> contentMap = contentFlowPostService.mapByIds(contentIds);
        BizException.condition(CollUtil.isEmpty(contentMap), NO_EXIST_CONTENT);
        return contentMap;
    }

}
