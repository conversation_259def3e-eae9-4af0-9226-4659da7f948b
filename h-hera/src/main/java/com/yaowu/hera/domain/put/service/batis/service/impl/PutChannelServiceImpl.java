package com.yaowu.hera.domain.put.service.batis.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.yaowu.hera.domain.put.service.batis.mapper.PutChannelMapper;
import com.yaowu.hera.domain.put.service.batis.service.IPutChannelService;
import com.yaowu.hera.model.entity.put.PutChannel;
import java.util.List;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

/**
 * <p>
 * 渠道表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-10-18
 */
@Service
public class PutChannelServiceImpl extends ServiceImpl<PutChannelMapper, PutChannel> implements IPutChannelService {


    @Override
    public PutChannel queryByChannelKey(String channelKey) {
        LambdaQueryWrapper<PutChannel> queryWrapper = Wrappers.lambdaQuery(PutChannel.class)
                .eq(PutChannel::getChannelKey, channelKey);

        List<PutChannel> channelList = this.list(queryWrapper);
        return CollectionUtil.isEmpty(channelList) ? null : channelList.get(0);
    }

    @Override
    public List<PutChannel> likeByChannelKey(String channelKey) {
        LambdaQueryWrapper<PutChannel> queryWrapper = Wrappers.lambdaQuery(PutChannel.class)
                .like(StringUtils.isNoneBlank(channelKey), PutChannel::getChannelKey, channelKey);

        return this.list(queryWrapper);
    }
}
