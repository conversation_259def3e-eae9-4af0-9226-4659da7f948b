package com.yaowu.hera.domain.mtl.pricingtool.biz.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.freedom.web.model.resp.BasePage;
import com.yaowu.hera.config.nacos.DouYinArraignConfig;
import com.yaowu.hera.config.nacos.mtl.PriceToolConfig;
import com.yaowu.hera.domain.audio.biz.ITranscribedBusinessDataBizService;
import com.yaowu.hera.domain.common.biz.INoticeBizService;
import com.yaowu.hera.domain.content.biz.IContentSubscribeRelBizService;
import com.yaowu.hera.domain.content.service.batis.service.IContentSubscribeRelService;
import static com.yaowu.hera.domain.leads.biz.impl.LeadsInfoBizServiceImpl.compareAppVersions;
import static com.yaowu.hera.domain.leads.biz.impl.LeadsInfoBizServiceImpl.extractVersion;
import com.yaowu.hera.domain.leads.service.batis.service.ILeadsDeviceCategoryService;
import com.yaowu.hera.domain.mtl.pricingtool.biz.IPricingToolBizService;
import com.yaowu.hera.domain.quotation.cache.CacheService;
import com.yaowu.hera.domain.quotation.service.batis.service.*;
import com.yaowu.hera.enums.mtl.LeadsDeviceShowTypeEnum;
import static com.yaowu.hera.enums.mtl.LeadsDeviceShowTypeEnum.QUOTATION_CALCULATOR_PAGE;
import com.yaowu.hera.enums.mtl.StandardPriceRegionEnum;
import com.yaowu.hera.model.bo.content.ContentSubscribeRelQueryModel;
import com.yaowu.hera.model.bo.content.QueryContentSubscribeRelBO;
import com.yaowu.hera.model.bo.leads.LeadsDeviceConditionBO;
import com.yaowu.hera.model.bo.pricetool.BuildPriceDailyQuotesBO;
import com.yaowu.hera.model.bo.pricetool.PriceDailyQuotesBO;
import com.yaowu.hera.model.dto.pricetool.RemoteBaseDatePricePageDTO;
import com.yaowu.hera.model.dto.pricetool.RemotePageBaseDatePriceDTO;
import com.yaowu.hera.model.entity.content.ContentSubscribeRel;
import com.yaowu.hera.model.entity.leads.LeadsDeviceCategory;
import com.yaowu.hera.model.entity.price.PriceDailyQuotes;
import com.yaowu.hera.model.entity.price.PriceToolSpuConfig;
import com.yaowu.hera.model.entity.price.localrentprice.*;
import com.yaowu.hera.model.vo.common.AreaTreeVO;
import com.yaowu.hera.remote.common.IFileHandleBizService;
import com.yaowu.hera.remote.thirdparty.IThirdPartyRemoteService;
import com.yaowu.hera.utils.BizException;
import com.yaowu.hera.utils.ErrorCode;
import com.yaowu.hera.utils.StreamUtil;
import com.yaowu.hera.utils.StringUtil;
import static com.yaowu.hera.utils.constants.Constants.*;
import com.yaowu.hera.utils.constants.PushConstants;
import static com.yaowu.hera.utils.constants.RedisConstants.PRICE_TOOL_UPDATE_TIME;
import com.yaowu.hera.utils.convertor.price.PricingToolConvertor;
import com.yaowu.hera.utils.mapstruct.mtl.PricingToolMapStruct;
import static com.yaowu.hera.utils.mapstruct.mtl.PricingToolMapStruct.generateRandomCoefficient;
import com.yaowu.heraapi.enums.device.ChannelTypeEnum;
import static com.yaowu.heraapi.enums.pricetool.RemoteQuoteTypeType.*;
import com.yaowu.heraapi.model.dto.mtl.pricing.RemotePageDeviceCategoryDTO;
import com.yaowu.heraapi.model.dto.mtl.pricing.RemoteQuerySpuPricingInfoDTO;
import com.yaowu.heraapi.model.dto.mtl.pricing.RemoteQuerySpuPricingInfoDTO.AreaAddressModel;
import com.yaowu.heraapi.model.dto.mtl.pricing.RemoteQueryUserSubSpuPriceStatusDTO;
import com.yaowu.heraapi.model.dto.mtl.pricing.RemoteUpdateSpuPriceUserSubDTO;
import com.yaowu.heraapi.model.dto.mtl.pricing.localrentprice.*;
import com.yaowu.heraapi.model.pojo.business.SpuPriceQuotationConfig;
import com.yaowu.heraapi.model.pojo.business.SpuPriceQuotationConfig.QuotationRange;
import com.yaowu.heraapi.model.pojo.business.SpuPriceQuotationConfig.Range;
import static com.yaowu.heraapi.model.pojo.business.SpuPriceQuotationConfig.rangeEqual;
import com.yaowu.heraapi.model.vo.mtl.pricing.*;
import com.yaowu.heraapi.model.vo.mtl.pricing.RemoteSpuPriceInfoVO.PriceInfo;
import com.yaowu.heraapi.model.vo.mtl.pricing.RemoteSpuPriceInfoVO.PriceTrendInfo;
import com.yaowu.heraapi.model.vo.mtl.pricing.RemoteSpuPriceInfoVO.SpuPriceInfo;
import com.yaowu.heraapi.model.vo.mtl.pricing.localrentprice.RemoteRentalDetailVO;
import com.yaowu.heraapi.model.vo.mtl.pricing.localrentprice.RemoteRentalPageDetailVO;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.function.Consumer;
import java.util.function.Function;
import java.util.function.Predicate;
import java.util.stream.Collectors;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.redisson.api.RedissonClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

/**
 * <AUTHOR>
 */
@Service
@Slf4j
public class PricingToolBizServiceImpl implements IPricingToolBizService {

    @Autowired
    private ILeadsDeviceCategoryService leadsDeviceCategoryService;

    @Autowired
    private PricingToolMapStruct pricingToolMapStruct;

    @Autowired
    private ITranscribedBusinessDataBizService transcribedDataBizService;

    @Autowired
    private IContentSubscribeRelBizService contentSubscribeRelBizService;

    @Autowired
    private IPriceDailyQuotesService priceDailyQuotesService;


    @Autowired
    private IContentSubscribeRelService contentSubscribeRelService;

    @Autowired
    private IFileHandleBizService fileHandleBizService;

    @Resource
    private RedisTemplate<String, String> strRedisTemplate;

    @Autowired
    @Qualifier("commonExecutor")
    private ThreadPoolTaskExecutor commonThreadPool;

    @Autowired
    private INoticeBizService noticeBizService;

    @Autowired
    private PriceToolConfig priceToolConfig;

    @Autowired
    private IThirdPartyRemoteService  thirdPartyRemoteService;

    @Autowired
    private IQuotationRentalReviewService quotationRentalReviewService;

    @Autowired
    private IQuotationRentalRequireService quotationRentalRequestService;

    @Autowired
    private IQuotationRentalConfigService quotationRentalConfigService;

    @Autowired
    private IQuotationDealAvgService quotationDealAvgService;

    @Autowired
    private ILeadsDeviceCategoryService deviceCategoryService;

    @Autowired
    private DouYinArraignConfig douYinArraignConfig;
    @Resource
    private IQuotationRentalQuotaConfigService iQuotationRentalQuotaConfigService;
    @Resource
    private IQuotationStandardPriceService iQuotationStandardPriceService;
    @Resource
    private RedissonClient redissonClient;



    @Override
    public BasePage<RemoteDeviceCategoryPageVO> pageDeviceCategoryInfo(RemotePageDeviceCategoryDTO dto) {
        //查询报价计算页配置的二级品类
        LambdaQueryWrapper<LeadsDeviceCategory> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(LeadsDeviceCategory::getShowType, QUOTATION_CALCULATOR_PAGE);
        wrapper.ne(LeadsDeviceCategory::getCategoryParentId, 0);
        Page<LeadsDeviceCategory> page = leadsDeviceCategoryService.page(dto.pageRequest(), wrapper);
        if (Objects.isNull(page) || CollectionUtils.isEmpty(page.getRecords())) {
            return new BasePage<>();
        }
        return BasePage.simpleConvert(page, pricingToolMapStruct::toRemoteDeviceCategoryPageVO);
    }

    @Override
    public RemoteSpuPriceInfoVO querySpuPricingInfoByCondition(RemoteQuerySpuPricingInfoDTO dto) {
        // 查询报价单页品类配置信息
        LeadsDeviceCategory deviceCategory = getLeadsDeviceCategory(dto);
        if (Objects.isNull(deviceCategory) || Objects.isNull(deviceCategory.getSpuPriceQuotationConfig())) {
            log.error("未查询到报价计算页配置的二级品类:{}", dto.getCategoryId());
            return null;
        }
        Set<Integer> priceShowType = deviceCategory.getSpuPriceQuotationConfig().getPriceShowType();
        if (CollectionUtil.isEmpty(priceShowType)){
            log.error("未配置当前品类价格展示类型:{}",deviceCategory.getId());
            return null;
        }
        //查询当前品类要取的报价区间
        return getRemoteSpuPriceInfoVO(dto, deviceCategory,priceShowType);
    }


    private RemoteSpuPriceInfoVO getRemoteSpuPriceInfoVO(RemoteQuerySpuPricingInfoDTO dto, LeadsDeviceCategory leadsDeviceCategory, Set<Integer> priceShowType) {
        LocalDate updateTimeAsLocalDate = getUpdateTimeAsLocalDate();
        RemoteSpuPriceInfoVO build = RemoteSpuPriceInfoVO.builder()
                .secondCategoryId(leadsDeviceCategory.getCategoryId())
                .secondCategoryName(leadsDeviceCategory.getCategoryName())
                .build();

        Consumer<Integer> processPriceInfo = priceTypeCode -> {
            PriceDailyQuotesBO countryQuotesBO = buildCountryPriceDailyQuotesBO(leadsDeviceCategory, dto, priceTypeCode,updateTimeAsLocalDate);
            List<PriceDailyQuotes> countryPriceQuotes = priceDailyQuotesService.listByQuoteTime(countryQuotesBO);

            PriceDailyQuotesBO cityQuotesBO = buildCityPriceDailyQuotesBO(leadsDeviceCategory, dto, priceTypeCode,updateTimeAsLocalDate);
            List<PriceDailyQuotes> cityPriceQuotes = priceDailyQuotesService.listByQuoteTime(cityQuotesBO);

            List<PriceDailyQuotes> dailyQuotes = getPriceQuotes(cityPriceQuotes, countryPriceQuotes, dto, priceTypeCode);

            PriceDailyQuotesBO countryTrendChart = buildCountryTrendCharQuotesBO(leadsDeviceCategory, dto, priceTypeCode,updateTimeAsLocalDate);
            List<PriceDailyQuotes> countryTrendChartDailyQuotes = priceDailyQuotesService.listByQuoteTime(countryTrendChart);

            PriceDailyQuotesBO trendChart = buildTrendCharQuotesBO(leadsDeviceCategory, dto, priceTypeCode,updateTimeAsLocalDate);
            List<PriceDailyQuotes> trendChartDailyQuotes = priceDailyQuotesService.listByQuoteTime(trendChart);

            List<PriceDailyQuotes> trendChartPrices = getTrendChartQuotes(trendChartDailyQuotes, countryTrendChartDailyQuotes, dto, priceTypeCode);
            switch (priceTypeCode) {
                case 1 -> build.setShiftPriceInfo(buildRentalPriceInfo(dailyQuotes, leadsDeviceCategory, trendChartPrices));
                case 2 -> build.setMonthlyRentalPriceInfo(buildRentalPriceInfo(dailyQuotes, leadsDeviceCategory, trendChartPrices));
                case 3 -> build.setTripPriceInfo(buildRentalPriceInfo(dailyQuotes, leadsDeviceCategory, trendChartPrices));
            }
        };

        priceShowType.forEach(processPriceInfo);
        return build;
    }

    private List<PriceDailyQuotes> getPriceQuotes(List<PriceDailyQuotes> cityQuotes,
                                                  List<PriceDailyQuotes> countryQuotes,
                                                  RemoteQuerySpuPricingInfoDTO dto, int priceTypeCode) {
        return switch (priceTypeCode) {
            case 1 -> getPriceDailyQuotes(cityQuotes, countryQuotes, dto);
            case 2 -> getPriceMonthlyQuotes(cityQuotes, countryQuotes, dto);
            case 3 -> getPriceTimesQuotes(cityQuotes, countryQuotes, dto);
            default -> throw new IllegalArgumentException("Unknown price type code: " + priceTypeCode);
        };
    }

    private List<PriceDailyQuotes> getTrendChartQuotes(List<PriceDailyQuotes> trendChartQuotes,
                                                       List<PriceDailyQuotes> countryTrendQuotes,
                                                       RemoteQuerySpuPricingInfoDTO dto, int priceTypeCode) {
        return switch (priceTypeCode) {
            case 1 -> getTrendChartDailyRentPrices(trendChartQuotes, countryTrendQuotes, dto);
            case 2 -> getTrendChartMonthlyRentPrices(trendChartQuotes, countryTrendQuotes, dto);
            case 3 -> getTrendChartTimesRentPrices(trendChartQuotes, countryTrendQuotes, dto);
            default -> throw new IllegalArgumentException("Unknown price type code: " + priceTypeCode);
        };
    }


    private List<PriceDailyQuotes> getTrendChartMonthlyRentPrices(List<PriceDailyQuotes> trendChartDailyQuotes,
                                                                  List<PriceDailyQuotes> countryTrendChartDailyQuotes,
                                                                  RemoteQuerySpuPricingInfoDTO dto) {
        List<PriceDailyQuotes> dailyRentPrices = filterQuotesByType(trendChartDailyQuotes, MONTHLY_RENTAL_PRICE.getCode());
        if (CollectionUtil.isEmpty(dailyRentPrices) || Objects.isNull(dto.getAreaAddress()) || StringUtil.isBlank(dto.getAreaAddress().getCityCode())){
            dailyRentPrices =filterQuotesByType(countryTrendChartDailyQuotes, MONTHLY_RENTAL_PRICE.getCode());
        }
        return dailyRentPrices;
    }

    private List<PriceDailyQuotes> getTrendChartTimesRentPrices(List<PriceDailyQuotes> trendChartDailyQuotes,
                                                                  List<PriceDailyQuotes> countryTrendChartDailyQuotes,
                                                                  RemoteQuerySpuPricingInfoDTO dto) {
        List<PriceDailyQuotes> dailyRentPrices = filterQuotesByType(trendChartDailyQuotes, TIMES_RENTAL_PRICE.getCode());
        if (CollectionUtil.isEmpty(dailyRentPrices) || Objects.isNull(dto.getAreaAddress()) || StringUtil.isBlank(dto.getAreaAddress().getCityCode())){
            dailyRentPrices =filterQuotesByType(countryTrendChartDailyQuotes, TIMES_RENTAL_PRICE.getCode());
        }
        return dailyRentPrices;
    }

    private List<PriceDailyQuotes> getTrendChartDailyRentPrices(List<PriceDailyQuotes> trendChartDailyQuotes,
                                                                List<PriceDailyQuotes> countryTrendChartDailyQuotes,
                                                                RemoteQuerySpuPricingInfoDTO dto) {
        List<PriceDailyQuotes> dailyRentPrices = filterQuotesByType(trendChartDailyQuotes, PER_SHIFT_PRICE.getCode());
        if (CollectionUtil.isEmpty(dailyRentPrices) || Objects.isNull(dto.getAreaAddress()) || StringUtil.isBlank(dto.getAreaAddress().getCityCode())){
            dailyRentPrices =filterQuotesByType(countryTrendChartDailyQuotes, PER_SHIFT_PRICE.getCode());
        }
        return dailyRentPrices;
    }

    private PriceDailyQuotesBO buildCountryTrendCharQuotesBO(LeadsDeviceCategory leadsDeviceCategory, RemoteQuerySpuPricingInfoDTO dto, int quoteType, LocalDate updateTimeAsLocalDate) {
        return buildPriceDailyQuotesBO(leadsDeviceCategory,
                updateTimeAsLocalDate.minusDays(7),
                updateTimeAsLocalDate,
                "000000",
                4,
                quoteType);
    }

    private List<PriceDailyQuotes> getPriceMonthlyQuotes(List<PriceDailyQuotes> priceDailyQuotes,
                                                         List<PriceDailyQuotes> countryPriceDailyQuotes,
                                                         RemoteQuerySpuPricingInfoDTO dto) {
        List<PriceDailyQuotes> monthlyRentPrices = filterQuotesByType(priceDailyQuotes, MONTHLY_RENTAL_PRICE.getCode());
        if (CollectionUtil.isEmpty(monthlyRentPrices) || Objects.isNull(dto.getAreaAddress()) || StringUtil.isBlank(dto.getAreaAddress().getCityCode())){
            monthlyRentPrices =filterQuotesByType(countryPriceDailyQuotes, MONTHLY_RENTAL_PRICE.getCode());
        }
        return monthlyRentPrices;
    }

    private List<PriceDailyQuotes> getPriceTimesQuotes(List<PriceDailyQuotes> priceDailyQuotes,
                                                         List<PriceDailyQuotes> countryPriceDailyQuotes,
                                                         RemoteQuerySpuPricingInfoDTO dto) {
        List<PriceDailyQuotes> monthlyRentPrices = filterQuotesByType(priceDailyQuotes, TIMES_RENTAL_PRICE.getCode());
        if (CollectionUtil.isEmpty(monthlyRentPrices) || Objects.isNull(dto.getAreaAddress()) || StringUtil.isBlank(dto.getAreaAddress().getCityCode())){
            monthlyRentPrices =filterQuotesByType(countryPriceDailyQuotes, TIMES_RENTAL_PRICE.getCode());
        }
        return monthlyRentPrices;
    }

    private List<PriceDailyQuotes> getPriceDailyQuotes(List<PriceDailyQuotes> priceDailyQuotes,
                                                       List<PriceDailyQuotes> countryPriceDailyQuotes,
                                                       RemoteQuerySpuPricingInfoDTO dto) {
        List<PriceDailyQuotes> dailyRentPrices = filterQuotesByType(priceDailyQuotes, PER_SHIFT_PRICE.getCode());
        if (CollectionUtil.isEmpty(dailyRentPrices) || Objects.isNull(dto.getAreaAddress()) || StringUtil.isBlank(dto.getAreaAddress().getCityCode())){
            dailyRentPrices =filterQuotesByType(countryPriceDailyQuotes, PER_SHIFT_PRICE.getCode());
        }
        return dailyRentPrices;
    }

    private LeadsDeviceCategory getLeadsDeviceCategory(RemoteQuerySpuPricingInfoDTO dto) {
        if (Objects.isNull(dto)) {
            return null;
        }
        LeadsDeviceConditionBO leadsDeviceConditionBO = buildLeadsDeviceConditionBO(dto);
        List<LeadsDeviceCategory> leadsDeviceCategories = leadsDeviceCategoryService.listByCondition(leadsDeviceConditionBO);
        return StreamUtil.of(leadsDeviceCategories).findFirst().orElse(null);
    }

    private static PriceDailyQuotesBO buildTrendCharQuotesBO(LeadsDeviceCategory leadsDeviceCategory, RemoteQuerySpuPricingInfoDTO dto, int quoteType, LocalDate updateTimeAsLocalDate) {
        String cityCode = Optional.of(dto.getAreaAddress()).map(AreaAddressModel::getCityCode).orElse("");
        return buildPriceDailyQuotesBO(leadsDeviceCategory,
                updateTimeAsLocalDate.minusDays(7),
                updateTimeAsLocalDate,
                cityCode,
                2,
                quoteType);
    }


    private static PriceDailyQuotesBO buildCityPriceDailyQuotesBO(LeadsDeviceCategory leadsDeviceCategory, RemoteQuerySpuPricingInfoDTO dto, int quoteType, LocalDate updateTimeAsLocalDate) {
        String cityCode = Optional.of(dto.getAreaAddress()).map(AreaAddressModel::getCityCode).orElse("");
        return buildPriceDailyQuotesBO(leadsDeviceCategory,
                updateTimeAsLocalDate,
                updateTimeAsLocalDate,
                cityCode,
                2,
                quoteType);
    }

    private static PriceDailyQuotesBO buildCountryPriceDailyQuotesBO(LeadsDeviceCategory leadsDeviceCategory,
                                                                     RemoteQuerySpuPricingInfoDTO dto,
                                                                     Integer quoteType,
                                                                     LocalDate updateTimeAsLocalDate) {
        return buildPriceDailyQuotesBO(leadsDeviceCategory,
                updateTimeAsLocalDate,
                updateTimeAsLocalDate,
                "000000",
                4,
                quoteType);
    }

    private static PriceDailyQuotesBO buildPriceDailyQuotesBO(LeadsDeviceCategory leadsDeviceCategory,
                                                              LocalDate startQuoteDate,
                                                              LocalDate endQuoteDate,
                                                              String cityCode,
                                                              Integer areaLevel,
                                                              Integer quoteType) {
        return PriceDailyQuotesBO.builder()
                .categoryId(leadsDeviceCategory.getCategoryId())
                .cityCode(cityCode)
                .areaLevel(areaLevel)
                .startQuoteDate(startQuoteDate)
                .endQuoteDate(endQuoteDate)
                .quoteType(quoteType)
                .build();
    }

    private static LeadsDeviceConditionBO buildLeadsDeviceConditionBO(RemoteQuerySpuPricingInfoDTO dto) {
        return LeadsDeviceConditionBO.builder()
                .categoryId(dto.getCategoryId())
                .showType(QUOTATION_CALCULATOR_PAGE)
                .isSecondCategory(Boolean.TRUE)
                .channelTypes(Set.of("NEW_CUSTOMER_APPLETS"))
                .build();
    }

    private List<PriceDailyQuotes> filterQuotesByType(List<PriceDailyQuotes> quotes, int quoteType) {
        return StreamUtil.of(quotes)
                .filter(e -> e.getQuoteType() == quoteType)
                .toList();
    }

    private PriceInfo buildRentalPriceInfo(List<PriceDailyQuotes> priceDailyQuotes,
                                           LeadsDeviceCategory deviceCategory,
                                           List<PriceDailyQuotes> trendChartDailyRentPrices) {
        if (CollectionUtil.isEmpty(priceDailyQuotes) || Objects.isNull(deviceCategory)) {
            return null;
        }
        SpuPriceQuotationConfig spuPriceQuotationConfig = deviceCategory.getSpuPriceQuotationConfig();
        List<RemotePriceDailyQuotesVO> dailyQuotesVOList = convertAndSortQuotes(priceDailyQuotes);
        List<RemotePriceDailyQuotesVO> trendChartDailyQuotesVOList = convertAndSortQuotes(trendChartDailyRentPrices);

        List<SpuPriceInfo> spuPriceInfoList = StreamUtil.of(dailyQuotesVOList).map(e -> {
            return SpuPriceInfo.builder()
                    .spuTypeDesc(spuPriceQuotationConfig.getIntervalUnitDec())
                    .floatPrice(e.getFloatPrice())
                    .firstCategoryId(deviceCategory.getCategoryParentId())
                    .firstCategoryName(deviceCategory.getCategoryParentName())
                    .secondCategoryId(deviceCategory.getCategoryId())
                    .secondCategoryName(deviceCategory.getCategoryName())
                    .leftRange(e.getLeftRange())
                    .rightRange(e.getRightRange())
                    .priceTrendInfoList(computePriceTrendInfoList(trendChartDailyQuotesVOList, e))
                    .build();
        }).collect(Collectors.toList());

        PriceInfo priceInfo = new PriceInfo();
        priceInfo.setSpuPriceInfoList(spuPriceInfoList);
        return priceInfo;
    }

    private List<RemotePriceDailyQuotesVO> convertAndSortQuotes(List<PriceDailyQuotes> quotes) {
        return StreamUtil.of(quotes)
                .map(pricingToolMapStruct::toRemotePriceDailyQuotesVO)
                .filter(e->!(e.getCategoryId()==1470569621894307843L && e.getSort()==4))
                .sorted(Comparator.comparing(RemotePriceDailyQuotesVO::getSort, Comparator.nullsLast(Comparator.naturalOrder())))
                .toList();
    }

    private List<PriceTrendInfo> computePriceTrendInfoList(List<RemotePriceDailyQuotesVO> trendChartDailyQuotesVOList, RemotePriceDailyQuotesVO remotePriceDailyQuotesVO) {
        return StreamUtil.of(trendChartDailyQuotesVOList)
                .filter(e->!(e.getCategoryId()==1470569621894307843L && e.getSort()==4))
                .filter(e -> rangeEqual(e.getLeftRange(), remotePriceDailyQuotesVO.getLeftRange()) && rangeEqual(e.getRightRange(), remotePriceDailyQuotesVO.getRightRange()))
                .map(pricingToolMapStruct::toPriceTrendInfo)
                .sorted(Comparator.comparing(PriceTrendInfo::getDate, Comparator.nullsLast(Comparator.naturalOrder())))
                .collect(Collectors.toList());
    }


    @Override
    public RemoteQueryUserSubscriptionStatusVO queryUserSubSpuPriceStatus(RemoteQueryUserSubSpuPriceStatusDTO dto) {

        QueryContentSubscribeRelBO contentSubscribeRelBO = QueryContentSubscribeRelBO.builder().userId(dto.getBizUserId()).bizType(3)
                .bizId(dto.getCategoryId()).build();
        List<ContentSubscribeRel> contentSubscribeRel = contentSubscribeRelBizService.queryUserSubSpuPriceStatus(contentSubscribeRelBO);

        RemoteQueryUserSubscriptionStatusVO statusVO = new RemoteQueryUserSubscriptionStatusVO();
        statusVO.setBizUserId(dto.getBizUserId());
        statusVO.setStatusDesc("未订阅");
        statusVO.setStatus(2);

        StreamUtil.of(contentSubscribeRel).findFirst().ifPresent(rel -> {
            if (rel.getSubscribeStatus() == 1) {
                statusVO.setStatus(1);
                statusVO.setStatusDesc("已订阅");
            }
        });
        return statusVO;
    }

    @Override
    public Boolean updateUserSubSpuPriceStatus(RemoteUpdateSpuPriceUserSubDTO dto) {
        QueryContentSubscribeRelBO contentSubscribeRelBO = QueryContentSubscribeRelBO.builder().userId(dto.getBizUserId()).bizType(3)
                .bizId(dto.getCategoryId()).build();
        List<ContentSubscribeRel> contentSubscribeRel = contentSubscribeRelBizService.queryUserSubSpuPriceStatus(contentSubscribeRelBO);
        ContentSubscribeRel oldContentSubscribeRel = StreamUtil.of(contentSubscribeRel).findFirst().orElse(null);
        if (Objects.nonNull(oldContentSubscribeRel)) {
            oldContentSubscribeRel.setSubscribeStatus(dto.getStatus());
            if (dto.getStatus()==2){
                oldContentSubscribeRel.setCancelSubscribeTime(LocalDateTime.now());
            }
            return contentSubscribeRelService.updateById(oldContentSubscribeRel);
        }
        ContentSubscribeRel newContentSubscribeRel = new ContentSubscribeRel();
        newContentSubscribeRel.setBizId(dto.getCategoryId());
        newContentSubscribeRel.setBizType(3);
        newContentSubscribeRel.setUserId(dto.getBizUserId());
        newContentSubscribeRel.setOpenId(dto.getOpenId());
        newContentSubscribeRel.setSubscribeStatus(dto.getStatus());
        return contentSubscribeRelService.save(newContentSubscribeRel);
    }


    @Override
    public void processLocalRentalTransData(LocalDate fromTime, LocalDate toTime) {
        if (Objects.isNull(fromTime) || Objects.isNull(toTime)) {
            log.error("处理数据时间参数不能为空");
            return;
        }
        List<LocalDate> datesBetween = getDatesBetween(fromTime, toTime);
        datesBetween.forEach(curDate -> {
            log.info("开始处理{}的报价数据", curDate);
            processLocalRentalCurDatePriceData(curDate);
        });
        //定时任务执行完毕，更新对应的执行时间
        log.info("定时任务执行完毕，更新对应的执行时间");
    }

    @Override
    public void processTranscribedData(LocalDate fromTime, LocalDate toTime) {
        if (Objects.isNull(fromTime) || Objects.isNull(toTime)) {
            log.error("处理数据时间参数不能为空");
            return;
        }
        List<LeadsDeviceCategory> leadsDeviceCategories = getLeadsDeviceCategories();
        List<LocalDate> datesBetween = getDatesBetween(fromTime, toTime);
        datesBetween.forEach(curDate -> {
            log.info("开始处理{}的报价数据", curDate);
            processCurDatePriceData(curDate, leadsDeviceCategories);
        });
        if (LocalDate.now().equals(toTime)){
            strRedisTemplate.opsForValue().set(PRICE_TOOL_UPDATE_TIME, LocalDate.now().toString());
        }
        //删除七天外的历史数据
        priceDailyQuotesService.deleteHistoryData(fromTime);
        //定时任务执行完毕，更新对应的执行时间
        log.info("定时任务执行完毕，更新对应的执行时间");
    }

    @Override
    public Boolean getRentalFlagByCategoryId(RemoteQueryRentalFlagDTO dto) {
        //查询当前渠道二级品类是否存在租价配置信息
        List<QuotationRentalConfig> allConfigs = quotationRentalConfigService.queryBySecondCategoryId(dto.getCategoryId());

        LeadsDeviceConditionBO leadsDeviceConditionBO = LeadsDeviceConditionBO.builder()
                .channel(dto.getChannelType())
                .showType(LeadsDeviceShowTypeEnum.ALL_PAGE)
                .isSecondCategory(Boolean.TRUE)
                .categoryId(dto.getCategoryId())
                .build();
        List<LeadsDeviceCategory> leadsDeviceCategories = deviceCategoryService.listByCondition(leadsDeviceConditionBO);

        return CollectionUtil.isNotEmpty(allConfigs) && CollectionUtil.isNotEmpty(leadsDeviceCategories);
    }

    /**
     * 处理当前时间的报价数据
     */
    private void processCurDatePriceData(LocalDate curDate, List<LeadsDeviceCategory> leadsDeviceCategories) {
        List<PriceDailyQuotes> results = new ArrayList<>();
        List<AreaTreeVO> cityAreas = StreamUtil.of(thirdPartyRemoteService.areaTree())
                .flatMap(areaTreeVO -> areaTreeVO.getChildren().stream())
                .toList();
        //获取报价小工具配置的二级品类
        for (LeadsDeviceCategory deviceCategory : leadsDeviceCategories) {
            RemotePageBaseDatePriceDTO dto = buildPagePageBaseDatePriceDTO(curDate, deviceCategory);
            List<RemoteTranscribedDataVO> dataVOList = transcribedDataBizService.listBaseDatePriceVO(dto);
            SpuPriceQuotationConfig spuDeviceConfig = deviceCategory.getSpuPriceQuotationConfig();
            Map<String, List<RemoteTranscribedDataVO>> cityCode2DataList = StreamUtil.of(dataVOList)
                    .collect(Collectors.groupingBy(RemoteTranscribedDataVO::getCityCode, Collectors.toList()));

            //更新全国报价信息
            List<PriceDailyQuotes> countryPriceQuotes =getCountryPriceQuotes(deviceCategory, dataVOList, spuDeviceConfig, curDate);
            if(CollectionUtil.isNotEmpty(countryPriceQuotes)){
                results.addAll(countryPriceQuotes);
            }
            //更新城市报价信息
            List<PriceDailyQuotes> cityPriceQuotes = getCityPriceQuotes(deviceCategory, cityCode2DataList, spuDeviceConfig, curDate, cityAreas);
            if (CollectionUtil.isNotEmpty(cityPriceQuotes)){
                results.addAll(cityPriceQuotes);
            }
        }
        if (CollectionUtil.isEmpty(results)){
            return;
        }
        priceDailyQuotesService.saveOrUpdateBatch(results);
    }

    private List<PriceDailyQuotes> getCountryPriceQuotes(LeadsDeviceCategory deviceCategory,
                                                         List<RemoteTranscribedDataVO> dataVOList,
                                                         SpuPriceQuotationConfig spuDeviceConfig,
                                                         LocalDate curDate) {
        BuildPriceDailyQuotesBO countryBO = BuildPriceDailyQuotesBO.builder()
                .quotationRange(spuDeviceConfig.getQuotationRange()).categoryId(deviceCategory.getCategoryId())
                .intervalUnitDec(spuDeviceConfig.getIntervalUnitDec()).isCountry(Boolean.TRUE).currentDate(curDate)
                .areaName("全国").areaCode("000000")
                .build();
        List<PriceDailyQuotes> countryPriceDailyQuotes = buildPriceDailyQuotes(countryBO, dataVOList);
        if (CollectionUtil.isEmpty(countryPriceDailyQuotes)){
            return Collections.emptyList();
        }
        return getBatchProcessXxlJobData(countryPriceDailyQuotes);
    }

    private List<PriceDailyQuotes> getCityPriceQuotes(LeadsDeviceCategory deviceCategory,
                                                      Map<String, List<RemoteTranscribedDataVO>> cityCode2DataList,
                                                      SpuPriceQuotationConfig spuDeviceConfig,
                                                      LocalDate curDate, List<AreaTreeVO> cityAreas) {
        List<PriceDailyQuotes> results = new ArrayList<>();
        StreamUtil.of(cityAreas).forEach(areaTreeVO -> {
            //取每一个城市的价格
            List<RemoteTranscribedDataVO> cityDataVOList = cityCode2DataList.get(areaTreeVO.getCode());
            BuildPriceDailyQuotesBO cityBO = BuildPriceDailyQuotesBO.builder()
                    .quotationRange(spuDeviceConfig.getQuotationRange()).categoryId(deviceCategory.getCategoryId())
                    .intervalUnitDec(spuDeviceConfig.getIntervalUnitDec()).isCountry(Boolean.FALSE).currentDate(curDate)
                    .areaCode(areaTreeVO.getCode()).areaName(areaTreeVO.getName())
                    .build();

            List<PriceDailyQuotes> cityPriceDailyQuotes = buildPriceDailyQuotes(cityBO, cityDataVOList);
            if (CollectionUtil.isEmpty(cityPriceDailyQuotes)) {
                return;
            }
            results.addAll(cityPriceDailyQuotes);
        });
        return getBatchProcessXxlJobData(results);
    }

    @Override
    public void asyncUpdatePriceWeiXinPushNotify(List<LeadsDeviceCategory> deviceCategories) {
        if (CollectionUtil.isEmpty(deviceCategories)) {
            return;
        }
        ContentSubscribeRelQueryModel build = ContentSubscribeRelQueryModel.builder()
                .bizType(3)
                .subscribeStatus(1)
                .build();
        List<ContentSubscribeRel> contentSubscribeRelList = contentSubscribeRelService.listByCondition(build);
        Map<Long, LeadsDeviceCategory> categoryIdToCategory = StreamUtil.of(deviceCategories)
                .collect(Collectors.toMap(LeadsDeviceCategory::getCategoryId, Function.identity(),(a,b)->a));
        commonThreadPool.execute(() -> {
            Map<Long, List<ContentSubscribeRel>> categoryIdToSub = StreamUtil.of(contentSubscribeRelList)
                    .collect(Collectors.groupingBy(ContentSubscribeRel::getBizId));
            categoryIdToSub.forEach((categoryId, subList) -> {
                Optional.ofNullable(categoryIdToCategory.get(categoryId)).ifPresent(deviceCategory -> {
                    Map<String, String> templateParams = buildTemplateParams(deviceCategory);
                    Set<String> openIds = StreamUtil.of(subList).map(ContentSubscribeRel::getOpenId).filter(Objects::nonNull).filter(StringUtil::isNotBlank)
                            .collect(Collectors.toSet());
                    if (CollectionUtil.isEmpty(openIds)) {
                        log.info("没有需要推送的用户");
                        return;
                    }
                    //根据用户订阅的二级品类不一样的
                    noticeBizService.sendWeChatAppletMsg(openIds,
                            PushConstants.H_HERA_UPDATE_PRICE_TOOL_PRICE_SUCCESS_NOTICE_TEMPLATE,
                            templateParams);
                });
            });
        });
    }


    private Map<String, String> buildTemplateParams(LeadsDeviceCategory deviceCategory) {
        Map<String, String> templateParams = new HashMap<>();
        //将时间转换为LocalDate类型的时间
        templateParams.put("updateTime",LocalDate.now().toString());
        templateParams.put("categoryName",Optional.ofNullable(deviceCategory).map(LeadsDeviceCategory::getCategoryName).orElse(""));
        return templateParams;
    }

    /**
     * 处理当前时间的报价数据
     */
    private void processLocalRentalCurDatePriceData(LocalDate curDate) {
        RemoteBaseDatePricePageDTO dto = buildPageBaseDatePriceDTO(curDate);
        dto.setPage(1);
        dto.setSize(100);
        BasePage<RemoteTranscribedDataVO> basePage = null;
        while (basePage == null || CollectionUtil.isNotEmpty(basePage.getRecords())) {
            basePage = transcribedDataBizService.pageBaseDatePriceVO(dto);
            if (CollectionUtil.isNotEmpty(basePage.getRecords())) {
                processCurPageDailyPriceData(basePage.getRecords(),curDate);
            }
            if (basePage.isLast()) {
                break;
            }
            dto.setPage(dto.getPage() + 1);
        }
    }

    /**
     * 处理当前页每日报价数据信息
     */
    private void processCurPageDailyPriceData(List<RemoteTranscribedDataVO> records, LocalDate curDate) {
        List<PriceDailyQuotes> resultList =new ArrayList<>();
        for (RemoteTranscribedDataVO record : records) {
            //当前中间号转录数据对应的日租价格 月租价格 列表集合
            List<PriceDailyQuotes> priceDailyQuotes = PricingToolConvertor.buildPriceDailyQuotes(record,curDate);
            if (CollectionUtil.isNotEmpty(priceDailyQuotes)){
                resultList.addAll(priceDailyQuotes);
            }
        }
        List<PriceDailyQuotes> listByFilter = StreamUtil.toListByFilter(resultList, Objects::nonNull);
        if (CollectionUtil.isEmpty(listByFilter)){
            return;
        }
        priceDailyQuotesService.saveOrUpdateBatch(listByFilter);
    }

    private RemoteBaseDatePricePageDTO buildPageBaseDatePriceDTO(LocalDate curDate) {
        return RemoteBaseDatePricePageDTO.builder()
                .fromTime(curDate)
                .toTime(curDate.plusDays(1))
                .build();
    }

    public static List<LocalDate> getDatesBetween(LocalDate fromTime, LocalDate toTime) {
        if (fromTime.isAfter(toTime)) {
            throw new IllegalArgumentException("fromTime must be before or equal to toTime");
        }

        List<LocalDate> dates = new ArrayList<>();
        for (LocalDate date = fromTime; !date.isAfter(toTime); date = date.plusDays(1)) {
            dates.add(date);
        }
        return dates;
    }

    private static RemotePageBaseDatePriceDTO buildPagePageBaseDatePriceDTO(LocalDate curDate,
                                                                            LeadsDeviceCategory deviceCategory) {
        return RemotePageBaseDatePriceDTO.builder()
                .fromTime(curDate.minusDays(60))
                .toTime(curDate).categoryName(deviceCategory.getCategoryName())
                .build();
    }

    private List<LeadsDeviceCategory> getLeadsDeviceCategories() {
        LeadsDeviceConditionBO leadsDeviceConditionBO = LeadsDeviceConditionBO.builder()
                .showType(QUOTATION_CALCULATOR_PAGE)
                .isSecondCategory(Boolean.TRUE)
                .channelTypes(Set.of("NEW_CUSTOMER_APPLETS"))
                .build();
        return leadsDeviceCategoryService.listByCondition(leadsDeviceConditionBO);
    }

    @Override
    public List<LeadsDeviceCategory> getLeadsDeviceCategories(LeadsDeviceShowTypeEnum showType) {
        LeadsDeviceConditionBO leadsDeviceConditionBO = LeadsDeviceConditionBO.builder()
                .showType(showType)
                .isSecondCategory(Boolean.TRUE)
                .channelTypes(Set.of("NEW_CUSTOMER_APPLETS"))
                .build();
        return leadsDeviceCategoryService.listByCondition(leadsDeviceConditionBO);
    }



    @Override
    public RemoteQuotationToolHomeVO getQuotationToolHomepageData() {
        LeadsDeviceConditionBO leadsDeviceConditionBO = LeadsDeviceConditionBO.builder()
                .showType(QUOTATION_CALCULATOR_PAGE).isSecondCategory(Boolean.TRUE)
                .build();
        List<LeadsDeviceCategory> categories = leadsDeviceCategoryService.listByCondition(leadsDeviceConditionBO);
        List<RemoteDeviceCategoryPageVO> categoryPageVOList = StreamUtil.of(categories)
                .map(pricingToolMapStruct::toRemoteDeviceCategoryPageVO)
                .toList();
        return RemoteQuotationToolHomeVO.builder().categoryPageVOList(categoryPageVOList).updateTime(getUpdateTimeAsLocalDate()).build();
    }

    public LocalDate getUpdateTimeAsLocalDate() {
        String updateTimeStr = strRedisTemplate.opsForValue().get(PRICE_TOOL_UPDATE_TIME);
        if (updateTimeStr == null || updateTimeStr.isEmpty()) {
            return null; // 或者返回一个默认值
        }
        return LocalDate.parse(updateTimeStr,  DateTimeFormatter.ofPattern("yyyy-MM-dd"));
    }

    private List<PriceDailyQuotes> getBatchProcessXxlJobData(List<PriceDailyQuotes> priceDailyQuotes) {
        if (CollectionUtil.isEmpty(priceDailyQuotes)) {
            return Collections.emptyList();
        }

        // 提取查询条件
        List<String> quoteTimeList = StreamUtil.of(priceDailyQuotes).map(PriceDailyQuotes::getQuoteTime).distinct().toList();
        List<Long> categoryIds = StreamUtil.of(priceDailyQuotes).map(PriceDailyQuotes::getCategoryId).distinct().toList();
        List<String> areaCodes = StreamUtil.of(priceDailyQuotes).map(PriceDailyQuotes::getAreaCode).distinct().toList();

        log.info("查询已存在的报价数据，条件：时间[{}]，品类[{}]，区域数量[{}]",
                quoteTimeList, categoryIds, areaCodes.size());

        // 使用优化后的查询方法，自动处理分批查询
        List<PriceDailyQuotes> oldPriceDailyQuotes = priceDailyQuotesService.listByConditionOptimized(
                quoteTimeList, categoryIds, areaCodes);

        log.info("查询到已存在的报价数据数量：{}", oldPriceDailyQuotes.size());

        return processSaveOrUpdateData(priceDailyQuotes, oldPriceDailyQuotes);
    }


    /**
     * 更新报价小工具数据信息
     */
    private boolean saveOrUpdateBatchXxlJob(List<PriceDailyQuotes> priceDailyQuotes) {
        if (CollectionUtil.isEmpty(priceDailyQuotes)) {
            return true;
        }
        List<String> quoteTimeList = StreamUtil.of(priceDailyQuotes).map(PriceDailyQuotes::getQuoteTime).distinct().toList();
        List<Long> categoryIds = StreamUtil.of(priceDailyQuotes).map(PriceDailyQuotes::getCategoryId).distinct().toList();
        List<String> areaCodes = StreamUtil.of(priceDailyQuotes).map(PriceDailyQuotes::getAreaCode).distinct().toList();
        //判断这点数据在数据库中是否存在，不存在则插入，存在则更新
        List<PriceDailyQuotes>  oldPriceDailyQuotes=  priceDailyQuotesService.listByCondition(quoteTimeList,categoryIds,areaCodes);
        List<PriceDailyQuotes> newPriceDailyQuotes = processSaveOrUpdateData(priceDailyQuotes,oldPriceDailyQuotes);
        return priceDailyQuotesService.saveOrUpdateBatch(newPriceDailyQuotes);
    }

    private List<PriceDailyQuotes> processSaveOrUpdateData(List<PriceDailyQuotes> priceDailyQuotes,
                                                           List<PriceDailyQuotes> oldPriceDailyQuotes) {
        return StreamUtil.of(priceDailyQuotes).map(e -> {
            PriceDailyQuotes oldPriceDailyQuote = StreamUtil.of(oldPriceDailyQuotes).filter(f -> {
                return f.getQuoteType() == e.getQuoteType() && f.getCategoryId().equals(e.getCategoryId())
                        && f.getAreaCode().equals(e.getAreaCode()) && f.getQuoteTime().equals(e.getQuoteTime())
                        && isEqualSpuConfig(f.getSpuConfig(),e.getSpuConfig());
            }).findFirst().orElse(null);
            if (Objects.nonNull(oldPriceDailyQuote)) {
                PricingToolMapStruct.resetPriceDailyQuotes(oldPriceDailyQuote, e);
                return oldPriceDailyQuote;
            }
            return e;
        }).filter(Objects::nonNull).collect(Collectors.toList());
    }

    private  boolean isEqualSpuConfig(PriceToolSpuConfig oldSpuConfig,PriceToolSpuConfig newSpuConfig){
        if (oldSpuConfig == null || newSpuConfig == null) {
            return false;
        }

        return Objects.equals(oldSpuConfig.getIntervalUnitDec(), newSpuConfig.getIntervalUnitDec()) &&
                Objects.equals(oldSpuConfig.getLeftRange(), newSpuConfig.getLeftRange()) &&
                Objects.equals(oldSpuConfig.getRightRange(), newSpuConfig.getRightRange()) &&
                Objects.equals(oldSpuConfig.getSort(), newSpuConfig.getSort());
    }
    private List<PriceDailyQuotes> buildPriceDailyQuotes(BuildPriceDailyQuotesBO buildCountryPriceBo,
                                                         List<RemoteTranscribedDataVO> dataList) {
        List<PriceDailyQuotes> allPriceDailyQuotesList = new ArrayList<>();
        //趟租
        List<PriceDailyQuotes> tripsPriceDailyQuotesList = buildTripPriceDailyQuotes(buildCountryPriceBo, dataList);
        if (CollectionUtil.isNotEmpty(tripsPriceDailyQuotesList)) {
            allPriceDailyQuotesList.addAll(tripsPriceDailyQuotesList);
        }

        //天租
        List<PriceDailyQuotes> dailyPriceDailyQuotesList = buildDailyPriceDailyQuotes(buildCountryPriceBo, dataList);
        if (CollectionUtil.isNotEmpty(dailyPriceDailyQuotesList)) {
            allPriceDailyQuotesList.addAll(dailyPriceDailyQuotesList);
        }
        //月租
        List<PriceDailyQuotes> monthlyPriceDailyQuotesList = buildMonthlyPriceDailyQuotes(buildCountryPriceBo, dataList);
        if (CollectionUtil.isNotEmpty(monthlyPriceDailyQuotesList)) {
            allPriceDailyQuotesList.addAll(monthlyPriceDailyQuotesList);
        }

        return allPriceDailyQuotesList;
    }


    private Predicate<RemoteTranscribedDataVO> createFilterPredicate(Range leftRange, Range rightRange) {
        if (Objects.isNull(leftRange) && Objects.nonNull(rightRange)) {
            return e -> (Objects.nonNull(e.getAudioMeters()) && e.getAudioMeters().compareTo(BigDecimal.ZERO) > 0
                    && e.getAudioMeters().compareTo(rightRange.getRangeValue()) <= 0)
                    || (Objects.nonNull(e.getAudioTonnage()) && e.getAudioTonnage().compareTo(BigDecimal.ZERO) > 0
                    && e.getAudioTonnage().compareTo(rightRange.getRangeValue()) <= 0);
        }

        if (Objects.nonNull(leftRange) && Objects.nonNull(rightRange)) {
            if (leftRange.getRangeType() == 1 && rightRange.getRangeType() == 2) {
                return e -> (Objects.nonNull(e.getAudioMeters()) && e.getAudioMeters().compareTo(leftRange.getRangeValue()) > 0
                        && e.getAudioMeters().compareTo(rightRange.getRangeValue()) <= 0)
                        || (Objects.nonNull(e.getAudioTonnage()) && e.getAudioTonnage().compareTo(leftRange.getRangeValue()) > 0
                        && e.getAudioTonnage().compareTo(rightRange.getRangeValue()) <= 0);
            }

            if (leftRange.getRangeType() == 2 && rightRange.getRangeType() == 1) {
                return e -> (Objects.nonNull(e.getAudioMeters()) && e.getAudioMeters().compareTo(leftRange.getRangeValue()) >= 0
                        && e.getAudioMeters().compareTo(rightRange.getRangeValue()) < 0)
                        || (Objects.nonNull(e.getAudioTonnage()) && e.getAudioTonnage().compareTo(leftRange.getRangeValue()) >= 0
                        && e.getAudioTonnage().compareTo(rightRange.getRangeValue()) < 0);
            }
        }

        if (Objects.nonNull(leftRange) && Objects.isNull(rightRange)) {
            return e -> (Objects.nonNull(e.getAudioMeters()) && e.getAudioMeters().compareTo(leftRange.getRangeValue()) > 0)
                    || (Objects.nonNull(e.getAudioTonnage()) && e.getAudioTonnage().compareTo(leftRange.getRangeValue()) > 0);
        }

        return e -> false; // 默认情况下不匹配任何数据
    }

    private List<PriceDailyQuotes> buildDailyPriceDailyQuotes(BuildPriceDailyQuotesBO countryPriceBO,
                                                              List<RemoteTranscribedDataVO> dailyDataList) {
        if (CollectionUtil.isEmpty(countryPriceBO.getQuotationRange()) || Objects.isNull(countryPriceBO.getCategoryId())
                || StringUtil.isBlank(countryPriceBO.getIntervalUnitDec())) {
            return Collections.emptyList();
        }

        if (CollUtil.isEmpty(dailyDataList) || dailyDataList.size() < 3) {
            return getCityCompensateDailyDataList(countryPriceBO);
        }

        List<PriceDailyQuotes> result = new ArrayList<>();
        for (QuotationRange range : countryPriceBO.getQuotationRange()) {
            PriceDailyQuotes dailyPriceDailyQuote = findDailyPriceDailyQuote(countryPriceBO,range, dailyDataList);
            if (Objects.isNull(dailyPriceDailyQuote) ) {
                dailyPriceDailyQuote = getCityCompensateDailyData(countryPriceBO, range);
            }
            result.add(dailyPriceDailyQuote);
        }
        return StreamUtil.of(result).filter(Objects::nonNull).toList();
    }

    private PriceDailyQuotes getCityCompensateDailyData(BuildPriceDailyQuotesBO countryPriceBO, QuotationRange range) {
        if (Objects.isNull(countryPriceBO) || Objects.isNull(range)) {
            return null;
        }
        PriceDailyQuotes quotes = new PriceDailyQuotes();
        quotes.setCategoryId(countryPriceBO.getCategoryId());
        quotes.setAreaLevel(2);
        quotes.setAreaCode(countryPriceBO.getAreaCode());
        quotes.setAreaName(countryPriceBO.getAreaName());
        quotes.setQuoteTime(String.valueOf(countryPriceBO.getCurrentDate()));
        quotes.setGenerateTime(LocalDateTime.now());
        PriceToolSpuConfig spuConfig = new PriceToolSpuConfig();
        spuConfig.setIntervalUnitDec(countryPriceBO.getIntervalUnitDec());
        spuConfig.setLeftRange(range.getLeftRange());
        spuConfig.setRightRange(range.getRightRange());
        spuConfig.setSort(range.getSort());
        quotes.setSpuConfig(spuConfig);
        BigDecimal generateRandomCoefficient = generateRandomCoefficient();
        BigDecimal sum = generateRandomCoefficient.add(BigDecimal.ONE).setScale(4, RoundingMode.HALF_UP);
        quotes.setCityFloatCoefficient(sum);
        quotes.setQuoteTime(String.valueOf(countryPriceBO.getCurrentDate()));
        if (countryPriceBO.isCountry()) {
            quotes.setAreaLevel(4);
            quotes.setAreaName("全国");
            quotes.setAreaCode("000000");
        }
        //剪刀车
        if (countryPriceBO.getCategoryId() == 1470569621894307843L) {
            quotes.setQuoteType(1);
            if (Objects.isNull(range.getLeftRange()) && Objects.nonNull(range.getRightRange())) {
                quotes.setCityBasePrice(new BigDecimal(100));
                quotes.setCityFloatPrice(quotes.getCityBasePrice().multiply(quotes.getCityFloatCoefficient()));
                return quotes;
            }
            if (Objects.nonNull(range.getLeftRange()) && Objects.nonNull(range.getRightRange())) {
                if (range.getLeftRange().getRangeValue().compareTo(new BigDecimal(6)) == 0) {
                    quotes.setCityBasePrice(new BigDecimal(110));
                    quotes.setCityFloatPrice(quotes.getCityBasePrice().multiply(quotes.getCityFloatCoefficient()));
                    return quotes;
                }
                if (range.getLeftRange().getRangeValue().compareTo(new BigDecimal(8)) == 0) {
                    quotes.setCityBasePrice(new BigDecimal(140));
                    quotes.setCityFloatPrice(quotes.getCityBasePrice().multiply(quotes.getCityFloatCoefficient()));
                    return quotes;
                }
            }
            if (Objects.nonNull(range.getLeftRange()) && Objects.isNull(range.getRightRange())) {
                quotes.setCityBasePrice(new BigDecimal(440));
                quotes.setCityFloatPrice(quotes.getCityBasePrice().multiply(quotes.getCityFloatCoefficient()));
                return quotes;
            }
        }
        return null;
    }

    private List<PriceDailyQuotes> getCityCompensateDailyDataList(BuildPriceDailyQuotesBO buildCountryPriceBo) {
        if (Objects.isNull(buildCountryPriceBo)) {
            return Collections.emptyList();
        }
        List<PriceDailyQuotes> result = new ArrayList<>();
        buildCountryPriceBo.getQuotationRange().forEach(range -> {
            PriceDailyQuotes quotes = new PriceDailyQuotes();
            quotes.setCategoryId(buildCountryPriceBo.getCategoryId());
            quotes.setAreaLevel(2);
            quotes.setAreaCode(buildCountryPriceBo.getAreaCode());
            quotes.setAreaName(buildCountryPriceBo.getAreaName());
            quotes.setQuoteTime(String.valueOf(buildCountryPriceBo.getCurrentDate()));
            quotes.setGenerateTime(LocalDateTime.now());
            PriceToolSpuConfig spuConfig = new PriceToolSpuConfig();
            spuConfig.setIntervalUnitDec(buildCountryPriceBo.getIntervalUnitDec());
            spuConfig.setLeftRange(range.getLeftRange());
            spuConfig.setRightRange(range.getRightRange());
            spuConfig.setSort(range.getSort());
            quotes.setSpuConfig(spuConfig);
            BigDecimal generateRandomCoefficient = generateRandomCoefficient();
            BigDecimal sum = generateRandomCoefficient.add(BigDecimal.ONE).setScale(4, RoundingMode.HALF_UP);
            quotes.setCityFloatCoefficient(sum);
            if (buildCountryPriceBo.isCountry()) {
                quotes.setAreaLevel(4);
                quotes.setAreaName("全国");
                quotes.setAreaCode("000000");
            }
            //剪刀车
            if (buildCountryPriceBo.getCategoryId() == 1470569621894307843L) {
                quotes.setQuoteType(1);
                if (Objects.isNull(range.getLeftRange()) && Objects.nonNull(range.getRightRange())) {
                    quotes.setCityBasePrice(new BigDecimal(100));
                    quotes.setCityFloatPrice(quotes.getCityBasePrice().multiply(quotes.getCityFloatCoefficient()));
                    result.add(quotes);
                    return;
                }
                if (Objects.nonNull(range.getLeftRange()) && Objects.nonNull(range.getRightRange())) {
                    if (range.getLeftRange().getRangeValue().compareTo(new BigDecimal(6)) == 0) {
                        quotes.setCityBasePrice(new BigDecimal(110));
                        quotes.setCityFloatPrice(quotes.getCityBasePrice().multiply(quotes.getCityFloatCoefficient()));
                        result.add(quotes);
                        return;
                    }
                    if (range.getLeftRange().getRangeValue().compareTo(new BigDecimal(8)) == 0) {
                        quotes.setCityBasePrice(new BigDecimal(140));
                        quotes.setCityFloatPrice(quotes.getCityBasePrice().multiply(quotes.getCityFloatCoefficient()));
                        result.add(quotes);
                        return;
                    }
                }
                if (Objects.nonNull(range.getLeftRange()) && Objects.isNull(range.getRightRange())) {
                    quotes.setCityBasePrice(new BigDecimal(440));
                    quotes.setCityFloatPrice(quotes.getCityBasePrice().multiply(quotes.getCityFloatCoefficient()));
                    result.add(quotes);
                    return;
                }
            }

        });
        return result;
    }


    private List<PriceDailyQuotes> getCityCompensateMonthlyDataList(BuildPriceDailyQuotesBO buildCountryPriceBo) {
        if (Objects.isNull(buildCountryPriceBo)) {
            return Collections.emptyList();
        }
        List<PriceDailyQuotes> result = new ArrayList<>();
        buildCountryPriceBo.getQuotationRange().forEach(range -> {
            PriceDailyQuotes quotes = new PriceDailyQuotes();
            quotes.setCategoryId(buildCountryPriceBo.getCategoryId());
            quotes.setAreaLevel(2);
            quotes.setAreaCode(buildCountryPriceBo.getAreaCode());
            quotes.setAreaName(buildCountryPriceBo.getAreaName());
            quotes.setQuoteTime(String.valueOf(buildCountryPriceBo.getCurrentDate()));
            quotes.setGenerateTime(LocalDateTime.now());
            PriceToolSpuConfig spuConfig = new PriceToolSpuConfig();
            spuConfig.setIntervalUnitDec(buildCountryPriceBo.getIntervalUnitDec());
            spuConfig.setLeftRange(range.getLeftRange());
            spuConfig.setRightRange(range.getRightRange());
            spuConfig.setSort(range.getSort());
            quotes.setSpuConfig(spuConfig);
            BigDecimal generateRandomCoefficient = generateRandomCoefficient();
            BigDecimal sum = generateRandomCoefficient.add(BigDecimal.ONE).setScale(4, RoundingMode.HALF_UP);
            quotes.setCityFloatCoefficient(sum);
            if (buildCountryPriceBo.isCountry()) {
                quotes.setAreaLevel(4);
                quotes.setAreaName("全国");
                quotes.setAreaCode("000000");
            }
            //剪刀车
            if (buildCountryPriceBo.getCategoryId() == 1470569621894307843L) {
                quotes.setQuoteType(2);
                if (Objects.isNull(range.getLeftRange()) && Objects.nonNull(range.getRightRange())) {
                    quotes.setCityBasePrice(new BigDecimal(1320));
                    quotes.setCityFloatPrice(quotes.getCityBasePrice().multiply(quotes.getCityFloatCoefficient()));
                    result.add(quotes);
                    return;
                }
                if (Objects.nonNull(range.getLeftRange()) && Objects.nonNull(range.getRightRange())) {
                    if (range.getLeftRange().getRangeValue().compareTo(new BigDecimal(6)) == 0) {
                        quotes.setCityBasePrice(new BigDecimal(1390));
                        quotes.setCityFloatPrice(quotes.getCityBasePrice().multiply(quotes.getCityFloatCoefficient()));
                        result.add(quotes);
                        return;
                    }
                    if (range.getLeftRange().getRangeValue().compareTo(new BigDecimal(8)) == 0) {
                        quotes.setCityBasePrice(new BigDecimal(1410));
                        quotes.setCityFloatPrice(quotes.getCityBasePrice().multiply(quotes.getCityFloatCoefficient()));
                        result.add(quotes);
                        return;
                    }
                }
                if (Objects.nonNull(range.getLeftRange()) && Objects.isNull(range.getRightRange())) {
                    quotes.setCityBasePrice(new BigDecimal(1860));
                    quotes.setCityFloatPrice(quotes.getCityBasePrice().multiply(quotes.getCityFloatCoefficient()));
                    result.add(quotes);
                    return;
                }
            }
        });
        return result;
    }





    private PriceDailyQuotes findDailyPriceDailyQuote(BuildPriceDailyQuotesBO countryPriceBO, QuotationRange range,
                                                      List<RemoteTranscribedDataVO> dailyRemoteTranscribedDataList) {
        List<RemoteTranscribedDataVO> remoteTranscribedDataVOList = StreamUtil.of(dailyRemoteTranscribedDataList)
                .filter(createFilterPredicate(range.getLeftRange(), range.getRightRange()))
                .sorted(Comparator.comparing(RemoteTranscribedDataVO::getDelDailyPrice))
                .toList();
        if (CollectionUtil.isEmpty(remoteTranscribedDataVOList)){
            return null;
        }
        RemoteTranscribedDataVO remoteTranscribedDataVO = remoteTranscribedDataVOList
                .stream()
                .skip((long) Math.ceil((remoteTranscribedDataVOList.size() - 1) / 2.0))
                .findFirst()
                .orElse(null);
        PriceDailyQuotes dailyPriceDailyQuotes = pricingToolMapStruct.toDailyPriceDailyQuotes(remoteTranscribedDataVO, countryPriceBO, range);
        if (Objects.isNull(dailyPriceDailyQuotes)){
            return findCurrentDailyRangeMockData(countryPriceBO,range);
        }
        return dailyPriceDailyQuotes;
    }

    private PriceDailyQuotes findCurrentDailyRangeMockData(BuildPriceDailyQuotesBO countryPriceBO, QuotationRange range) {
        return null;
    }

    private List<PriceDailyQuotes> buildMonthlyPriceDailyQuotes(BuildPriceDailyQuotesBO monthlyPriceBO,
                                                                List<RemoteTranscribedDataVO> monthlyDataList) {
        if (CollectionUtil.isEmpty(monthlyPriceBO.getQuotationRange()) || Objects.isNull(monthlyPriceBO.getCategoryId())
        || StringUtil.isBlank(monthlyPriceBO.getIntervalUnitDec())) {
            return Collections.emptyList();
        }

        if (CollUtil.isEmpty(monthlyDataList) || monthlyDataList.size() < 3) {
            return getCityCompensateMonthlyDataList(monthlyPriceBO);
        }

        List<PriceDailyQuotes> result = new ArrayList<>();
        for (QuotationRange range : monthlyPriceBO.getQuotationRange()) {
            PriceDailyQuotes monthlyPriceDailyQuote = findMonthlyPriceDailyQuote(range,monthlyPriceBO, monthlyDataList);
            if (Objects.isNull(monthlyPriceDailyQuote)) {
                monthlyPriceDailyQuote = getCityCompensateMonthlyData(monthlyPriceBO, range);
            }
            result.add(monthlyPriceDailyQuote);
        }
        return StreamUtil.of(result).filter(Objects::nonNull).toList();
    }

    private PriceDailyQuotes getCityCompensateMonthlyData(BuildPriceDailyQuotesBO monthlyPriceBO, QuotationRange range) {
        if (Objects.isNull(monthlyPriceBO) || Objects.isNull(range)) {
            return null;
        }
        PriceDailyQuotes quotes = new PriceDailyQuotes();
        quotes.setCategoryId(monthlyPriceBO.getCategoryId());
        quotes.setAreaLevel(2);
        quotes.setAreaCode(monthlyPriceBO.getAreaCode());
        quotes.setAreaName(monthlyPriceBO.getAreaName());
        quotes.setQuoteTime(String.valueOf(monthlyPriceBO.getCurrentDate()));
        quotes.setGenerateTime(LocalDateTime.now());
        PriceToolSpuConfig spuConfig = new PriceToolSpuConfig();
        spuConfig.setIntervalUnitDec(monthlyPriceBO.getIntervalUnitDec());
        spuConfig.setLeftRange(range.getLeftRange());
        spuConfig.setRightRange(range.getRightRange());
        spuConfig.setSort(range.getSort());
        quotes.setSpuConfig(spuConfig);
        BigDecimal generateRandomCoefficient = generateRandomCoefficient();
        BigDecimal sum = generateRandomCoefficient.add(BigDecimal.ONE).setScale(4, RoundingMode.HALF_UP);
        quotes.setCityFloatCoefficient(sum);
        quotes.setQuoteTime(String.valueOf(monthlyPriceBO.getCurrentDate()));
        if (monthlyPriceBO.isCountry()) {
            quotes.setAreaLevel(4);
            quotes.setAreaName("全国");
            quotes.setAreaCode("000000");
        }
        //剪刀车
        if (monthlyPriceBO.getCategoryId() == 1470569621894307843L) {
            quotes.setQuoteType(2);
            if (Objects.isNull(range.getLeftRange()) && Objects.nonNull(range.getRightRange())) {
                quotes.setCityBasePrice(new BigDecimal(1320));
                quotes.setCityFloatPrice(quotes.getCityBasePrice().multiply(quotes.getCityFloatCoefficient()));
                return quotes;
            }
            if (Objects.nonNull(range.getLeftRange()) && Objects.nonNull(range.getRightRange())) {
                if (range.getLeftRange().getRangeValue().compareTo(new BigDecimal(6)) == 0) {
                    quotes.setCityBasePrice(new BigDecimal(1390));
                    quotes.setCityFloatPrice(quotes.getCityBasePrice().multiply(quotes.getCityFloatCoefficient()));
                    return quotes;
                }
                if (range.getLeftRange().getRangeValue().compareTo(new BigDecimal(8)) == 0) {
                    quotes.setCityBasePrice(new BigDecimal(1410));
                    quotes.setCityFloatPrice(quotes.getCityBasePrice().multiply(quotes.getCityFloatCoefficient()));
                    return quotes;
                }
            }
            if (Objects.nonNull(range.getLeftRange()) && Objects.isNull(range.getRightRange())) {
                quotes.setCityBasePrice(new BigDecimal(1860));
                quotes.setCityFloatPrice(quotes.getCityBasePrice().multiply(quotes.getCityFloatCoefficient()));
                return quotes;
            }
        }
        return null;
    }

    private List<PriceDailyQuotes> buildTripPriceDailyQuotes(BuildPriceDailyQuotesBO dailyBo,
                                                             List<RemoteTranscribedDataVO> tripDataList) {
        if (CollectionUtil.isEmpty(dailyBo.getQuotationRange()) || Objects.isNull(dailyBo.getCategoryId()) ||
                StringUtil.isBlank(dailyBo.getIntervalUnitDec())) {
            return Collections.emptyList();
        }

        if (CollUtil.isEmpty(tripDataList) || tripDataList.size() < 3) {
            return getCityCompensateTripDataList(dailyBo);
        }

        List<PriceDailyQuotes> result = new ArrayList<>();
        for (QuotationRange range : dailyBo.getQuotationRange()) {
            PriceDailyQuotes tripDailyQuote = findTripPriceDailyQuote(range, dailyBo, tripDataList);
            if (Objects.isNull(tripDailyQuote)) {
                //返回底线数据
                tripDailyQuote = getCityCompensateTripData(dailyBo, range);
            }
            result.add(tripDailyQuote);
        }
        return StreamUtil.of(result).filter(Objects::nonNull).toList();
    }

    private List<PriceDailyQuotes> getCityCompensateTripDataList(BuildPriceDailyQuotesBO buildCountryPriceBo) {
        if (Objects.isNull(buildCountryPriceBo)) {
            return Collections.emptyList();
        }
        List<PriceDailyQuotes> result = new ArrayList<>();
        buildCountryPriceBo.getQuotationRange().forEach(range -> {
            PriceDailyQuotes quotes = new PriceDailyQuotes();
            quotes.setCategoryId(buildCountryPriceBo.getCategoryId());
            quotes.setAreaLevel(2);
            quotes.setAreaCode(buildCountryPriceBo.getAreaCode());
            quotes.setAreaName(buildCountryPriceBo.getAreaName());
            quotes.setQuoteTime(String.valueOf(buildCountryPriceBo.getCurrentDate()));
            quotes.setGenerateTime(LocalDateTime.now());
            PriceToolSpuConfig spuConfig = new PriceToolSpuConfig();
            spuConfig.setIntervalUnitDec(buildCountryPriceBo.getIntervalUnitDec());
            spuConfig.setLeftRange(range.getLeftRange());
            spuConfig.setRightRange(range.getRightRange());
            spuConfig.setSort(range.getSort());
            quotes.setSpuConfig(spuConfig);
            BigDecimal generateRandomCoefficient = generateRandomCoefficient();
            BigDecimal sum = generateRandomCoefficient.add(BigDecimal.ONE).setScale(4, RoundingMode.HALF_UP);
            quotes.setCityFloatCoefficient(sum);
            if (buildCountryPriceBo.isCountry()) {
                quotes.setAreaLevel(4);
                quotes.setAreaName("全国");
                quotes.setAreaCode("000000");
            }
            //汽车吊
            if (buildCountryPriceBo.getCategoryId() == 1709021490515589742L) {
                quotes.setQuoteType(3);
                if (Objects.isNull(range.getLeftRange()) && Objects.nonNull(range.getRightRange())) {
                    quotes.setCityBasePrice(new BigDecimal(600));
                    quotes.setCityFloatPrice(quotes.getCityBasePrice().multiply(quotes.getCityFloatCoefficient()));
                    result.add(quotes);
                    return;
                }
                if (Objects.nonNull(range.getLeftRange()) && Objects.nonNull(range.getRightRange())) {
                    if (range.getLeftRange().getRangeValue().compareTo(new BigDecimal(10)) == 0) {
                        quotes.setCityBasePrice(new BigDecimal(700));
                        quotes.setCityFloatPrice(quotes.getCityBasePrice().multiply(quotes.getCityFloatCoefficient()));
                        result.add(quotes);
                        return;
                    }
                    if (range.getLeftRange().getRangeValue().compareTo(new BigDecimal(20)) == 0) {
                        quotes.setCityBasePrice(new BigDecimal(1100));
                        quotes.setCityFloatPrice(quotes.getCityBasePrice().multiply(quotes.getCityFloatCoefficient()));
                        result.add(quotes);
                        return;
                    }
                }
            }
            //随车吊
            if (buildCountryPriceBo.getCategoryId() == 1709021490515589743L) {
                quotes.setQuoteType(3);
                if (Objects.isNull(range.getLeftRange()) && Objects.nonNull(range.getRightRange())) {
                    quotes.setCityBasePrice(new BigDecimal(800));
                    quotes.setCityFloatPrice(quotes.getCityBasePrice().multiply(quotes.getCityFloatCoefficient()));
                    result.add(quotes);
                    return;
                }
            }
        });
        return result;
    }


    private PriceDailyQuotes getCityCompensateTripData(BuildPriceDailyQuotesBO dailyBO, QuotationRange range) {
        if (Objects.isNull(dailyBO) || Objects.isNull(range)) {
            return null;
        }
        PriceDailyQuotes quotes = new PriceDailyQuotes();
        quotes.setCategoryId(dailyBO.getCategoryId());
        quotes.setAreaLevel(2);
        quotes.setAreaCode(dailyBO.getAreaCode());
        quotes.setAreaName(dailyBO.getAreaName());
        quotes.setQuoteTime(String.valueOf(dailyBO.getCurrentDate()));
        quotes.setGenerateTime(LocalDateTime.now());
        PriceToolSpuConfig spuConfig = new PriceToolSpuConfig();
        spuConfig.setIntervalUnitDec(dailyBO.getIntervalUnitDec());
        spuConfig.setLeftRange(range.getLeftRange());
        spuConfig.setRightRange(range.getRightRange());
        spuConfig.setSort(range.getSort());
        quotes.setSpuConfig(spuConfig);
        BigDecimal generateRandomCoefficient = generateRandomCoefficient();
        BigDecimal sum = generateRandomCoefficient.add(BigDecimal.ONE).setScale(4, RoundingMode.HALF_UP);
        quotes.setCityFloatCoefficient(sum);
        quotes.setQuoteTime(String.valueOf(dailyBO.getCurrentDate()));
        if (dailyBO.isCountry()) {
            quotes.setAreaLevel(4);
            quotes.setAreaName("全国");
            quotes.setAreaCode("000000");
        }
        //汽车吊
        if (dailyBO.getCategoryId() == 1709021490515589742L) {
            quotes.setQuoteType(3);
            if (Objects.isNull(range.getLeftRange()) && Objects.nonNull(range.getRightRange())) {
                quotes.setCityBasePrice(new BigDecimal(600));
                quotes.setCityFloatPrice(quotes.getCityBasePrice().multiply(quotes.getCityFloatCoefficient()));
                return quotes;
            }
            if (Objects.nonNull(range.getLeftRange()) && Objects.nonNull(range.getRightRange())) {
                if (range.getLeftRange().getRangeValue().compareTo(new BigDecimal(10)) == 0) {
                    quotes.setCityBasePrice(new BigDecimal(700));
                    quotes.setCityFloatPrice(quotes.getCityBasePrice().multiply(quotes.getCityFloatCoefficient()));
                    return quotes;
                }
                if (range.getLeftRange().getRangeValue().compareTo(new BigDecimal(20)) == 0) {
                    quotes.setCityBasePrice(new BigDecimal(1100));
                    quotes.setCityFloatPrice(quotes.getCityBasePrice().multiply(quotes.getCityFloatCoefficient()));
                    return quotes;
                }
            }
        }
        //随车吊
        if (dailyBO.getCategoryId() == 1709021490515589743L) {
            quotes.setQuoteType(3);
            if (Objects.isNull(range.getLeftRange()) && Objects.nonNull(range.getRightRange())) {
                quotes.setCityBasePrice(new BigDecimal(800));
                quotes.setCityFloatPrice(quotes.getCityBasePrice().multiply(quotes.getCityFloatCoefficient()));
                return quotes;
            }
        }
        return null;
    }


    private PriceDailyQuotes findMonthlyPriceDailyQuote(QuotationRange range,
                                                        BuildPriceDailyQuotesBO countryPriceBo,
                                                        List<RemoteTranscribedDataVO> monthlyRemoteTranscribedDataList) {
        List<RemoteTranscribedDataVO> remoteTranscribedDataVOList = StreamUtil.of(monthlyRemoteTranscribedDataList)
                .filter(createFilterPredicate(range.getLeftRange(), range.getRightRange()))
                .sorted(Comparator.comparing(RemoteTranscribedDataVO::getDelMonthlyPrice))
                .toList();

        if (CollectionUtil.isEmpty(remoteTranscribedDataVOList)){
            return null;
        }

        RemoteTranscribedDataVO remoteTranscribedDataVO = remoteTranscribedDataVOList
                .stream()
                .skip((long) Math.ceil((remoteTranscribedDataVOList.size() - 1) / 2.0))
                .findFirst()
                .orElse(null);
        return pricingToolMapStruct.toMonthlyPriceDailyQuotes(remoteTranscribedDataVO, countryPriceBo, range);
    }


    private PriceDailyQuotes findTripPriceDailyQuote(QuotationRange range,
                                                     BuildPriceDailyQuotesBO countryPriceBo,
                                                     List<RemoteTranscribedDataVO> tripRemoteTranscribedDataList) {
        List<RemoteTranscribedDataVO> remoteTranscribedDataVOList = StreamUtil.of(tripRemoteTranscribedDataList)
                .filter(createFilterPredicate(range.getLeftRange(), range.getRightRange()))
                .sorted(Comparator.comparing(RemoteTranscribedDataVO::getDelDailyPrice))
                .toList();
        if (CollectionUtil.isEmpty(remoteTranscribedDataVOList)) {
            return null;
        }
        RemoteTranscribedDataVO remoteTranscribedDataVO = remoteTranscribedDataVOList
                .stream()
                .skip((long) Math.ceil((remoteTranscribedDataVOList.size() - 1) / 2.0))
                .findFirst()
                .orElse(null);
        return pricingToolMapStruct.toTripPriceDailyQuotes(remoteTranscribedDataVO, countryPriceBo, range);
    }

    private Set<Integer> calculatePeriodNosByPeriodQuantity(int periodNumber, int periodNo) {
        if (periodNumber <= 0 || periodNo < 0) {
            return Collections.emptySet();
        }
        Set<Integer> finalPeriodNos = new HashSet<>();
        //如果periodNo <= periodNumber 兜底逻辑有几个周期返回几个周期
        for (int i = 0; i < periodNumber; i++) {
            int currentPeriodNo = periodNo--;
            if (currentPeriodNo < 0) {
                return finalPeriodNos;
            }
            finalPeriodNos.add(currentPeriodNo);
        }
        return finalPeriodNos;
    }

    @Override
    public Boolean addPriceRequire(RemoteAddPriceRequireDTO dto) {
        QuotationRentalRequirement quotationRentalRequest = pricingToolMapStruct.toPriceRentalRequest(dto);
        boolean save = quotationRentalRequestService.save(quotationRentalRequest);

        BizException.condition(!save, ErrorCode.EDIT_INVOICE_PARAM_ERROR);
        return Boolean.TRUE;
    }

    @Override
    public RemoteRentalDetailVO getRentalDetail(RemoteQueryRentalDetailDTO dto) {
        //查询当前品类配置的信息
        List<QuotationRentalConfig> configList = quotationRentalConfigService.queryBySecondCategoryId(dto.getSecondCategoryId());

        //查单位关联配置
        List<QuotationRentalQuotaConfig>  quotaConfigList=iQuotationRentalQuotaConfigService.listByRentalConfigIds(configList.stream().map(QuotationRentalConfig::getId).collect(Collectors.toSet()));
        //标准价配置单位
        List<QuotationStandardPrice> standardPriceList=iQuotationStandardPriceService.getStandardByIds(quotaConfigList.stream().map(QuotationRentalQuotaConfig::getStandardPriceId).collect(Collectors.toSet()),getAreaCodeByCityCode(dto.getCityCode()));

        //查询详情页配置
        List<QuotationDealAvg> quotationDealAvgList = queryQuotationDealAvgs(dto.getCityCode(), configList);

        // 查询用户是否订阅当前品类信息
        List<ContentSubscribeRel> contentSubscribeInfo = getContentSubscribeRels(dto.getBizUserId(),dto.getSecondCategoryId());

        //查询不同端的名称展示
        List<LeadsDeviceCategory> leadsDeviceCategories = getLeadsDeviceCategories(dto);
        //查询不同品类名称
        return PricingToolConvertor.toRemoteRentalDetailVO(dto.getCityCode(),configList, leadsDeviceCategories, quotationDealAvgList, contentSubscribeInfo, priceToolConfig,standardPriceList);
    }

    /**
     * 根据城市编码获取对应区域
     * @param cityCode
     * @return
     */
    @Override
    public String getAreaCodeByCityCode(String cityCode) {
        if (StringUtil.isBlank(cityCode)) {
            cityCode = DEFAULT_CITY_CODE;
        }

        List<AreaTreeVO> areaTreeVOList = redissonClient.getList(CacheService.AREA_TREE_VO_REDIS_KEY);
        if (CollectionUtil.isEmpty(areaTreeVOList)) {
            areaTreeVOList = thirdPartyRemoteService.cityTree();
        }

        if (CollectionUtil.isEmpty(areaTreeVOList)) {
            return null;
        }
        //<区，省>
        Map<StandardPriceRegionEnum, Set<String>> regionMap = priceToolConfig.getRegionMap();
        if (CollectionUtil.isEmpty(regionMap)) {
            log.warn("省会城市配置或区域映射配置为空，无法计算区域价格");
            return null;
        }
        //<省，区>反向映射
        Map<String, StandardPriceRegionEnum> reverseRegionMap = regionMap.entrySet().stream().flatMap(entry -> {
            return entry.getValue().stream().map(provinceCode -> new AbstractMap.SimpleEntry<>(provinceCode, entry.getKey()));
        }).collect(Collectors.toMap(Map.Entry::getKey, Map.Entry::getValue, (a, b) -> a));


        String finalCityCode = cityCode;
        Optional<AreaTreeVO> provinceOptional = areaTreeVOList.stream()
                .filter(province -> province.getChildren() != null)
                .filter(province -> province.getChildren().stream().anyMatch(city -> finalCityCode.equals(city.getCode())))
                .findFirst();
        StandardPriceRegionEnum regionEnum = provinceOptional.map(province -> reverseRegionMap.get(province.getCode())).orElse(null);
        return Objects.isNull(regionEnum) ? null : regionEnum.getValue();
    }

    /**
     * 查询租价配置信息
     */
    private List<QuotationDealAvg> queryQuotationDealAvgs(String cityCode, List<QuotationRentalConfig> configList) {
        //最近x周期的spuId
        Set<Long> spuIds = StreamUtil.ofSet(configList, QuotationRentalConfig::getSpuId);
        //城市
        String finalCityCode = StringUtil.isBlank(cityCode) ? DEFAULT_CITY_CODE:cityCode;

        //查询最近x周期的周期号
        Set<Integer> periodNos = queryRecentPeriodsNos(finalCityCode);

        //根据配置信息 查询最近x周期的数据
        return quotationDealAvgService.queryPeriodAvgPriceList(finalCityCode,spuIds, periodNos);
    }

    /**
     * 查询最近x周期对应的周期批次号列表
     */
    private Set<Integer> queryRecentPeriodsNos(String cityCode) {
        if (StringUtil.isBlank(cityCode)){
            return Collections.emptySet();
        }
        int periodNo = quotationDealAvgService.getLatestPeriodNoByCityCode(cityCode);
        return calculatePeriodNosByPeriodQuantity(priceToolConfig.getLocalRentTrendPeriods(),periodNo);
    }

    /**
     * 查询用户品类订阅信息
     */
    private List<ContentSubscribeRel> getContentSubscribeRels(Long bizUserId,Long secondCategoryId) {
        QueryContentSubscribeRelBO subscribeBO = QueryContentSubscribeRelBO.builder().
                        userId(bizUserId).bizType(PRICE_TOOL_SUBSCRIBE_TYPE).bizId(secondCategoryId)
                        .build();
        return contentSubscribeRelBizService.queryUserSubSpuPriceStatus(subscribeBO);
    }

    /**
     * 查询品类展示名称
     */
    private List<LeadsDeviceCategory> getLeadsDeviceCategories(RemoteQueryRentalDetailDTO dto) {
        LeadsDeviceConditionBO leadsDeviceConditionBO = LeadsDeviceConditionBO.builder()
                .channel(dto.getChannelType())
                .showType(LeadsDeviceShowTypeEnum.ALL_PAGE)
                .categoryId(dto.getSecondCategoryId())
                .build();
        return deviceCategoryService.listByCondition(leadsDeviceConditionBO);
    }

    @Override
    public Boolean submitRentalReview(RemoteSubmitRentalReviewDTO dto) {
        QuotationRentalFeedback review = pricingToolMapStruct.toPriceRentalReview(dto);
        boolean save = quotationRentalReviewService.saveOrUpdate(review);

        BizException.condition(!save, ErrorCode.ADD_PRICE_RENTAL_REVIEW_ERROR);
        return Boolean.TRUE;
    }

    @Override
    public RemoteRentalPageDetailVO pageRental(RemoteQueryRentalPageDTO dto) {
        List<QuotationRentalConfig> allConfigs = quotationRentalConfigService.listQuotationConfigs();
        //抖音提审需要对品类做过滤
        filterDouYinArraignDevice(dto.getChannelType(),dto.getReferer(),allConfigs);

        List<QuotationRentalConfig> filterFirstCategoryConfigs = filterTargetRentalConfigs(dto.getFirstCategoryId(), allConfigs);
        Set<Long> filterSecondCategoryIds =  filterTargetSecondCategories(filterFirstCategoryConfigs,dto);;

        //查询有序品类配置列表 对应小程序渠道先按一级品类排序 再按照二级品类排序
        Page<LeadsDeviceCategory>  deviceCategoryPage=  deviceCategoryService.pageByRentalCondition(dto,filterSecondCategoryIds);
        if (Objects.isNull(deviceCategoryPage) || CollectionUtils.isEmpty(deviceCategoryPage.getRecords())){
            return new  RemoteRentalPageDetailVO();
        }

        //获取当前页品类配置数据
        List<QuotationRentalConfig> curPageConfigs = filterCurPageRentalConfigs(deviceCategoryPage.getRecords(), filterFirstCategoryConfigs);
        //根据配置信息 查询最近2周期的数据
        List<QuotationDealAvg> quotationDealAvgList = getCurTwoPeriodQuotationDealAvgs(dto, curPageConfigs);

        List<LeadsDeviceCategory> firstCategories = getAllLeadsDeviceFirstCategories(dto,allConfigs);

        //查单位关联配置
        List<QuotationRentalQuotaConfig>  quotaConfigList=iQuotationRentalQuotaConfigService.listByRentalConfigIds(curPageConfigs.stream().map(QuotationRentalConfig::getId).collect(Collectors.toSet()));
        //标准价配置单位
        List<QuotationStandardPrice> standardPriceList=iQuotationStandardPriceService.getStandardByIds(quotaConfigList.stream().map(QuotationRentalQuotaConfig::getStandardPriceId).collect(Collectors.toSet()),getAreaCodeByCityCode(dto.getCityCode()));

        //过滤掉一级品类
        return PricingToolConvertor.toRemoteRentalPageDetailVO(firstCategories, deviceCategoryPage, curPageConfigs, quotationDealAvgList,standardPriceList);
    }

    private void filterDouYinArraignDevice(String channel, String referer, List<QuotationRentalConfig> allConfigs) {
        if (CollectionUtils.isEmpty(allConfigs)
                || channel == null || !channel.equals(ChannelTypeEnum.DOU_YIN_APPLETS.getMsg())) {
            return;
        }

        String versionStr = extractVersion(referer);
        if (!StringUtils.hasText(versionStr)) {
            return;
        }
        try {
            //判断版本号
            if (PREVIEW.equalsIgnoreCase(versionStr) || compareAppVersions(versionStr, douYinArraignConfig.getArraignVersion()) >= 0) {
                //过滤
                allConfigs.removeIf(category -> !douYinArraignConfig.getHomePageCategoryIds().contains(category.getSecondCategoryId()));
            }
        } catch (Exception e) {
            log.error("解析抖音版本号出现异常:", e);
        }
    }


    private Set<Long> filterTargetSecondCategories(List<QuotationRentalConfig> filterFirstCategoryConfigs,
                                                   RemoteQueryRentalPageDTO dto) {
        Set<Long> secondCategoryIds = StreamUtil.ofSet(filterFirstCategoryConfigs, QuotationRentalConfig::getSecondCategoryId);
        LeadsDeviceConditionBO leadsDeviceConditionBO = LeadsDeviceConditionBO.builder()
                .channel(dto.getChannelType())
                .showType(LeadsDeviceShowTypeEnum.ALL_PAGE)
                .isSecondCategory(Boolean.TRUE)
                .build();
        List<LeadsDeviceCategory> leadsDeviceCategories = deviceCategoryService.listByCondition(leadsDeviceConditionBO);
        Set<Long> leadsDeviceCategoryIds = StreamUtil.ofSet(leadsDeviceCategories, LeadsDeviceCategory::getCategoryId);
        return StreamUtil.of(secondCategoryIds)
                .filter(leadsDeviceCategoryIds::contains)
                .collect(Collectors.toSet());
    }

    /**
     * 获取目标品类配置数据
     */
    private static List<QuotationRentalConfig> filterTargetRentalConfigs(Long firstCategoryId,
                                                                         List<QuotationRentalConfig> allConfigs) {
        List<QuotationRentalConfig> filterFirstCategoryConfigs = allConfigs;
        if (Objects.nonNull(firstCategoryId)){
            filterFirstCategoryConfigs = StreamUtil.toListByFilter(allConfigs, config->
                    Objects.equals(config.getFirstCategoryId(),firstCategoryId));
        }
        return filterFirstCategoryConfigs;
    }

    private static List<QuotationRentalConfig> filterCurPageRentalConfigs(List<LeadsDeviceCategory> leadsDeviceCategoryRecords,
                                                                          List<QuotationRentalConfig> allConfigs) {
        if (CollectionUtil.isEmpty(leadsDeviceCategoryRecords) || CollectionUtil.isEmpty(allConfigs)){
            log.info("filterCurPageRentalConfigs.leadsDeviceCategoryRecords:{},allConfigs:{}",leadsDeviceCategoryRecords,allConfigs);
            return Collections.emptyList();
        }
        Set<Long> curPageSecondCategoryIds = StreamUtil.ofSet(leadsDeviceCategoryRecords,LeadsDeviceCategory::getCategoryId);
        return StreamUtil.toListByFilter(allConfigs,e->curPageSecondCategoryIds.contains(e.getSecondCategoryId()));
    }

    /**
     * 查询最近两个周期的均价数据
     */
    private List<QuotationDealAvg> getCurTwoPeriodQuotationDealAvgs(RemoteQueryRentalPageDTO dto,
                                                                    List<QuotationRentalConfig> curPageConfigs) {
        //当前页对应的spu
        Set<Long> curPageSpuIds = StreamUtil.ofSet(curPageConfigs,QuotationRentalConfig::getSpuId);

        //当前页要查询的周期
        Set<Integer> periodNos=
                calculatePeriodNosByPeriodQuantity(LAST_TWO_PERIOD_RENTAL_PRICE,quotationDealAvgService.getLatestPeriodNo());
        //城市
        String cityCode = StringUtil.isBlank(dto.getCityCode()) ? DEFAULT_CITY_CODE: dto.getCityCode();
        log.info("getCurTwoPeriodQuotationDealAvgs.cityCode:{},curPageSpuIds:{},periodNos:{}",cityCode,curPageSpuIds,periodNos);
        return quotationDealAvgService.queryPeriodAvgPriceList(cityCode,curPageSpuIds, periodNos);
    }

    /**
     * 查询所有的一级品类
     */
    private List<LeadsDeviceCategory> getAllLeadsDeviceFirstCategories(RemoteQueryRentalPageDTO dto,
                                                                       List<QuotationRentalConfig> allConfigs) {
        //共有的二级品类对应的一级品类
        Set<Long> firstCategoryIds = StreamUtil.ofSet(allConfigs, QuotationRentalConfig::getFirstCategoryId);

        Set<Long> secondCategoryIds = StreamUtil.ofSet(allConfigs, QuotationRentalConfig::getSecondCategoryId);
        LeadsDeviceConditionBO leadsDeviceConditionBO = LeadsDeviceConditionBO.builder()
                .channel(dto.getChannelType())
                .showType(LeadsDeviceShowTypeEnum.ALL_PAGE)
                .build();
        List<LeadsDeviceCategory> secondCategories = deviceCategoryService.listByCondition(leadsDeviceConditionBO);
        List<LeadsDeviceCategory> targetCategories =
                StreamUtil.toListByFilter(secondCategories, e->secondCategoryIds.contains(e.getCategoryId()));
        Set<Long> existFirstCategoryIds = StreamUtil.ofSet(targetCategories, LeadsDeviceCategory::getCategoryParentId);

        Set<Long> intersection = new HashSet<>(firstCategoryIds);
        intersection.retainAll(existFirstCategoryIds);
        return StreamUtil.toListByFilter(secondCategories, e -> intersection.contains(e.getCategoryId()));
    }

}
