package com.yaowu.hera.domain.sell.service.batis.service.impl;

import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.yaowu.hera.domain.sell.service.batis.mapper.SellDeviceMapper;
import com.yaowu.hera.domain.sell.service.batis.service.ISellDeviceService;
import com.yaowu.hera.model.bo.sell.SellDeviceBO;
import com.yaowu.hera.model.entity.sell.SellDevice;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

/**
 * <p>
 * 推荐设备表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-11-21
 */
@Service
public class SellDeviceServiceImpl
        extends ServiceImpl<SellDeviceMapper, SellDevice>
        implements ISellDeviceService {

    /**
     * 查询设备
     *
     * @param ids
     * @return
     */
    @Override
    public Map<Long, SellDeviceBO> getDeviceMap(Collection<Long> ids) {
        if (CollUtil.isEmpty(ids)) {
            return new HashMap<>(0);
        }
        List<SellDevice> sellDevices = listByIds(ids);
        if (CollUtil.isEmpty(ids)) {
            return new HashMap<>(0);
        }
        List<Long> parentIds = sellDevices.stream().map(SellDevice::getParentId)
                .filter(parentId -> !Objects.equals(parentId, 0L)).collect(Collectors.toList());
        Map<Long, SellDevice> parentMap = new HashMap<>();
        if (CollUtil.isNotEmpty(parentIds)) {
            List<SellDevice> parents = listByIds(parentIds);
            parentMap = parents.stream().collect(Collectors.toMap(SellDevice::getId, parent -> parent));
        }
        Map<Long, SellDevice> finalParentMap = parentMap;
        return sellDevices.stream().map(sellDevice -> {
            SellDeviceBO bo = new SellDeviceBO();
            bo.setId(sellDevice.getId());
            bo.setName(sellDevice.getName());
            bo.setParentId(sellDevice.getParentId());
            SellDevice parent = finalParentMap.get(sellDevice.getParentId());
            if (Objects.nonNull(parent)) {
                bo.setParentName(parent.getName());
            }
            return bo;
        }).collect(Collectors.toMap(SellDeviceBO::getId, bo -> bo));
    }
}
