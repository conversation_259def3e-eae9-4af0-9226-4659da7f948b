package com.yaowu.hera.domain.sticker.biz.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.freedom.feign.utils.FeignInvokeUtils;
import com.freedom.web.model.resp.BasePage;
import com.yaowu.hera.config.nacos.CarStickerConfig;
import com.yaowu.hera.domain.common.feign.DwzServiceFeign;
import com.yaowu.hera.domain.favor.biz.IFavorStockInfoBizService;
import com.yaowu.hera.domain.sticker.biz.IStickerPartnerBizService;
import com.yaowu.hera.domain.sticker.service.batis.service.IStickerPartnerInfoService;
import com.yaowu.hera.enums.common.dwz.TermOfValidityEnum;
import com.yaowu.hera.model.dto.common.dwz.GenerateShortUrlDTO;
import com.yaowu.hera.model.entity.sticker.StickerPartnerInfo;
import com.yaowu.hera.model.vo.common.dwz.ShortUrlVO;
import com.yaowu.hera.utils.BizException;
import com.yaowu.hera.utils.ErrorCode;
import com.yaowu.hera.utils.mapstruct.sticker.StickerMapStruct;
import com.yaowu.heraapi.model.dto.sticker.RemoteStickerAddPartnerDTO;
import com.yaowu.heraapi.model.dto.sticker.RemoteStickerEditPartnerDTO;
import com.yaowu.heraapi.model.dto.sticker.RemoteStickerPartnerPageDTO;
import com.yaowu.heraapi.model.vo.sticker.RemoteStickerAddPartnerVO;
import com.yaowu.heraapi.model.vo.sticker.RemoteStickerPartnerDetailVO;
import com.yaowu.heraapi.model.vo.sticker.RemoteStickerPartnerPageVO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import jakarta.annotation.Resource;

/**
 * <AUTHOR>
 * @since 2023/8/16 17:27
 **/
@Slf4j
@Service
public class StickerPartnerBizServiceImpl implements IStickerPartnerBizService {

    @Resource
    private IStickerPartnerInfoService stickerPartnerInfoService;

    @Resource
    private CarStickerConfig carStickerConfig;

    @Resource
    private DwzServiceFeign dwzServiceFeign;

    @Resource
    private IFavorStockInfoBizService stockInfoBizService;

    @Override
    public RemoteStickerAddPartnerVO add(RemoteStickerAddPartnerDTO dto) {
        StickerUtil.validatePhones(dto.getPhones());

        BizException.condition(stickerPartnerInfoService.checkExistByPartnerCode(dto.getPartnerCode()),
                ErrorCode.STICKER_PARTNER_CODE_EXIST);
        BizException.condition(stickerPartnerInfoService.checkExistByPartnerName(dto.getPartnerName()),
                ErrorCode.STICKER_PARTNER_NAME_EXIST);

        StickerPartnerInfo partnerInfo = generateStickerPartnerInfo(dto);
        stickerPartnerInfoService.save(partnerInfo);
        return StickerMapStruct.INSTANCE.toRemoteStickerAddPartnerVO(partnerInfo.getId());
    }

    @Override
    public Boolean edit(RemoteStickerEditPartnerDTO dto) {
        StickerUtil.validatePhones(dto.getPhones());
        StickerPartnerInfo stickerPartnerInfo = stickerPartnerInfoService.getById(dto.getId());
        BizException.condition(null == stickerPartnerInfo, ErrorCode.STICKER_PARTNER_NOT_EXIST);

        if (!stickerPartnerInfo.getPartnerName().equalsIgnoreCase(dto.getPartnerName())) {
            BizException.condition(stickerPartnerInfoService.checkExistByPartnerName(dto.getPartnerName()),
                    ErrorCode.STICKER_PARTNER_NAME_EXIST);
        }

        stickerPartnerInfo.setPartnerName(dto.getPartnerName());
        stickerPartnerInfo.setPhones(dto.getPhones());
        return stickerPartnerInfoService.updateById(stickerPartnerInfo);
    }

    @Override
    public RemoteStickerPartnerDetailVO detail(Long id) {
        StickerPartnerInfo stickerPartnerInfo = stickerPartnerInfoService.getById(id);
        BizException.condition(null == stickerPartnerInfo, ErrorCode.STICKER_PARTNER_CODE_EXIST);
        return StickerMapStruct.INSTANCE.toRemoteStickerPartnerDetailVO(stickerPartnerInfo);
    }

    @Override
    public BasePage<RemoteStickerPartnerPageVO> page(RemoteStickerPartnerPageDTO dto) {
        LambdaQueryWrapper<StickerPartnerInfo> queryWrapper = Wrappers.lambdaQuery(StickerPartnerInfo.class)
                .eq((StringUtils.isNotBlank(dto.getPartnerName())), StickerPartnerInfo::getPartnerName, dto.getPartnerName())
                .orderByAsc(StickerPartnerInfo::getCreateTime);
        Page<StickerPartnerInfo> page = stickerPartnerInfoService.page(dto.pageRequest(), queryWrapper);
        return BasePage.simpleConvert(page, this::convertToRemoteStickerPartnerPageVO);
    }

    private RemoteStickerPartnerPageVO convertToRemoteStickerPartnerPageVO(StickerPartnerInfo partnerInfo) {
        return StickerMapStruct.INSTANCE.toRemoteStickerPartnerPageVO(partnerInfo);
    }

    private StickerPartnerInfo generateStickerPartnerInfo(RemoteStickerAddPartnerDTO dto) {
        StickerPartnerInfo partnerInfo = StickerMapStruct.INSTANCE.toStickerPartnerInfo(dto);
        partnerInfo.setAliasKey(StickerUtil.generateMonitorKey());
        partnerInfo.setMonitorUrl(generateMonitorUrl(partnerInfo.getPartnerName(), partnerInfo.getPartnerCode(), partnerInfo.getAliasKey()));
        return partnerInfo;
    }

    private String generateMonitorUrl(String partnerName, String partnerCode, String monitorKey) {
        BizException.condition(StringUtils.isBlank(carStickerConfig.getPartnerMonitorUrlPrefix()),
                ErrorCode.STICKER_CONFIG_ERROR);

        String params = String.format("pid=%s", monitorKey);
        String query = carStickerConfig.getPartnerMonitorUrlPrefix().endsWith("?") ? "" : "?";
        String monitorLongUrl = carStickerConfig.getPartnerMonitorUrlPrefix() + query + params;
        String monitorShortUrl = generateShortUrl(monitorLongUrl);
        if (StringUtils.isBlank(monitorShortUrl)) {
            log.error("generate short url error, part name: {}, part code:{}:", partnerName, partnerCode);
            return monitorLongUrl;
        }

        return monitorShortUrl;
    }

    private String generateShortUrl(String url) {
        GenerateShortUrlDTO dto = new GenerateShortUrlDTO();
        dto.setLongUrl(url);
        dto.setTermOfValidity(TermOfValidityEnum.ONE_YEAR);
        ShortUrlVO vo = FeignInvokeUtils.convert(dwzServiceFeign.generateShortUrl(dto), ShortUrlVO.class);
        return vo.getSuccess() ? vo.getShortUrl() : null;
    }
}
