package com.yaowu.hera.domain.leads.biz;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.freedom.toolscommon.utils.StreamTools;
import com.freedom.web.exception.BusinessException;
import com.yaowu.hera.config.nacos.mtl.MerchantFindCustomerConfig;
import com.yaowu.hera.domain.feign.MelinaFeignBizService;
import com.yaowu.hera.domain.feign.NoticeFeignBizService;
import com.yaowu.hera.domain.feign.PassportFeignBizService;
import com.yaowu.hera.domain.leads.service.batis.service.ILeadsInfoExtService;
import com.yaowu.hera.domain.leads.service.batis.service.ILeadsInfoService;
import com.yaowu.hera.domain.leads.service.batis.service.ILeadsMerchantRelationService;
import com.yaowu.hera.domain.leads.service.batis.service.ILeadsOrderInfoService;
import com.yaowu.hera.domain.mtl.callorder.service.batis.service.IContactRecordService;
import com.yaowu.hera.domain.mtl.leadsorder.biz.ILeadsOrderInfoBizService;
import com.yaowu.hera.domain.mtl.personalsetting.IPersonalSettingBizService;
import com.yaowu.hera.enums.mtl.BindDirectionEnum;
import com.yaowu.hera.enums.mtl.LeadsProgressEnum;
import com.yaowu.hera.model.bo.common.SpuInfo;
import com.yaowu.hera.model.bo.leads.PrivateNumberAxbHolder;
import com.yaowu.hera.model.entity.leads.LeadsInfo;
import com.yaowu.hera.model.entity.leads.LeadsInfoExt;
import com.yaowu.hera.model.entity.leads.LeadsMerchantRelation;
import com.yaowu.hera.model.entity.mtl.callorder.ContactRecord;
import com.yaowu.hera.remote.kratos.IKratosFeignService;
import com.yaowu.hera.remote.melina.MelinaMerchantInfoFeignService;
import com.yaowu.hera.utils.StreamUtil;
import com.yaowu.heraapi.enums.mtl.B2CLeadsStatusEnum;
import com.yaowu.kratosapi.model.dto.device.RemoteEnableTreeSpuDTO;
import com.yaowu.kratosapi.model.vo.device.RemoteDeviceCategoryTreeVO;
import com.yaowu.melinaapi.model.vo.store.RemoteStoreInfoDetailVO;
import com.yaowu.melinaapi.model.vo.store.RemoteStoreUserRelationVO;
import com.yaowu.notice.model.vo.RemotePrivateNumberAxbVO;
import com.yaowu.passportapi.model.vo.user.UserInfoVO;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.StringUtils;

import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

import static com.yaowu.hera.utils.ErrorCode.*;

/**
 * <AUTHOR>
 * @date 2024/9/3-14:21
 */
@Slf4j
public abstract class AbstractLeadsInfoService {

    // 正则表达式用于匹配手机号码，例如中国的手机号码
    private static final String PHONE_REGEX = "^1[3-9]\\d{9}$";

    // 正则表达式用于匹配固话号码，例如国内的固话号码，如020-12345678
    private static final String LANDLINE_REGEX = "^0\\d{2,3}-\\d{7,8}$";

    @Autowired
    protected ILeadsMerchantRelationService iLeadsMerchantRelationService;

    @Autowired
    protected MerchantFindCustomerConfig merchantFindCustomerConfig;

    @Autowired
    protected ILeadsInfoService leadsInfoService;

    @Autowired
    protected ILeadsMerchantRelationService leadsMerchantRelationService;

    @Autowired
    protected NoticeFeignBizService noticeFeignBizService;

    @Resource
    protected PassportFeignBizService passportFeignBizService;

    @Resource
    protected MelinaFeignBizService melinaFeignBizService;

    @Autowired
    protected MelinaMerchantInfoFeignService melinaMerchantInfoFeignService;

    @Autowired
    protected IKratosFeignService kratosFeignService;

    @Autowired
    protected IContactRecordService iLeadsStoreInquiryRecordService;

    @Autowired
    protected ILeadsInfoExtService leadsInfoExtService;
    @Autowired
    protected ILeadsOrderInfoService leadsOrderInfoService;
    @Autowired
    protected ILeadsOrderInfoBizService leadsOrderInfoBizService;

    @Resource
    protected IPersonalSettingBizService personalSettingBizService;

    /**
     * 获取非空的线索数据
     *
     * @param id 线索id
     * @return 线索数据
     */
    protected LeadsInfo getNotNullLeadsInfoById(Long id) {
        LeadsInfo leadsInfo = this.leadsInfoService.getById(id);
        BusinessException.condition(leadsInfo == null, LEADS_NOT_EXISTS.getCode(), LEADS_NOT_EXISTS.getMsg());
        return leadsInfo;
    }

    /**
     * 计算线索匹配进度
     *
     * @param leadsInfoId 线索id
     * @return 线索匹配进度
     */
    protected LeadsProgressEnum calculateLeadsProgress(Long leadsInfoId) {
        LeadsInfo leadsInfo = leadsInfoService.getById(leadsInfoId);
        return calculateLeadsProgress(leadsInfo);
    }

    /**
     * 计算线索匹配进度
     *
     * @param leadsInfo 线索id
     * @return 线索匹配进度
     */
    protected LeadsProgressEnum calculateLeadsProgress(LeadsInfo leadsInfo) {
        if (leadsInfo == null) {
            return null;
        }
        LeadsInfoExt leadsInfoExt = leadsInfoExtService.getByLeadsInfoId(leadsInfo.getId());
        if (Objects.isNull(leadsInfoExt)) {
            return null;
        }

        LocalDateTime now = LocalDateTime.now();
        Set<B2CLeadsStatusEnum> matched = Set.of(B2CLeadsStatusEnum.ISSUED_TO_ONLINE_SALE, B2CLeadsStatusEnum.PURCHASED, B2CLeadsStatusEnum.RATING_SUCCEED);
        if (matched.contains(leadsInfoExt.getB2cLeadsStatus())) {
            return LeadsProgressEnum.MATCHED_SUCCESSFULLY;
        }
        if (Objects.equals(B2CLeadsStatusEnum.CANCELED, leadsInfoExt.getB2cLeadsStatus())) {
            return LeadsProgressEnum.EXPIRED;
        }

        //60分钟内商户匹配中
        if (leadsInfo.getSubmitTime().plusMinutes(merchantFindCustomerConfig.getLeadsProgressTime()).isAfter(now)) {
            return LeadsProgressEnum.LESS_SIXTY_MATCHING;
        }
        //60分钟后无商户匹配中
        return LeadsProgressEnum.OVER_SIXTY_MATCHING;

    }

    /**
     * 线索商户匹配关联关系
     *
     * @param leadsInfoIds 线索id
     * @return
     */
    protected List<LeadsMerchantRelation> leadsInfoRelations(Set<Long> leadsInfoIds, String sceneCode) {
        return leadsMerchantRelationService.listByLeadInfoIds(leadsInfoIds, sceneCode);
    }

    /**
     * 线索商户匹配关联关系
     *
     * @param leadsInfoId 线索id
     * @return
     */
    protected List<LeadsMerchantRelation> leadsInfoRelations(Long leadsInfoId, String sceneCode) {
        if (leadsInfoId == null) {
            return CollUtil.newArrayList();
        }
        return leadsInfoRelations(CollUtil.newHashSet(leadsInfoId), sceneCode);
    }

    /**
     * 获取客户的手机号
     *
     * @param customerUserId 客户userId
     * @return
     */
    protected String getUserPhone(Long customerUserId) {
        UserInfoVO userInfo = passportFeignBizService.getUserInfoByUserId(customerUserId);
        BusinessException.condition(userInfo == null, USER_WARN_NOT_EXIST.getCode(), USER_WARN_NOT_EXIST.getMsg());
        return userInfo.getPhone();
    }


    /**
     * 获取品类数据
     *
     * @return
     */
    public Map<Long/*firstCategoryId*/, List<SpuInfo>> spuCategoryMap() {
        RemoteEnableTreeSpuDTO spuDTO = new RemoteEnableTreeSpuDTO();
        // 传0就是全部
        spuDTO.setBizGroupCode(0);
        spuDTO.setBizTypeCode(0);
        //1.设备 2.属具
        spuDTO.setCatType(1);
        List<RemoteDeviceCategoryTreeVO> treeVOS = kratosFeignService.enableTreeSpu(spuDTO);
        return SpuInfo.buildCategory(treeVOS);
    }

    /**
     * 线索绑定的隐私号数据
     *
     * @param leadsInfoIds  线索id
     * @param testSceneCode 是否需要忽略测试的场景
     * @return
     */
    protected Map<Long, List<PrivateNumberAxbHolder>> leadsInfoPrivateNumberAxbInfo(Set<Long> leadsInfoIds, String testSceneCode) {
        List<ContactRecord> inquiryRecords = iLeadsStoreInquiryRecordService.getListByLeadsIds(leadsInfoIds, testSceneCode);
        // 过滤异常的数据
        List<ContactRecord> inquiryRecordFilters = StreamUtil.toListByFilter(inquiryRecords, v -> StrUtil.isNotBlank(v.getBindNum()));
        List<PrivateNumberAxbHolder> privateNumbers = new ArrayList<>();
        Map<Long, List<ContactRecord>> leadsIdGroupMap = StreamTools.group(inquiryRecordFilters, ContactRecord::getLeadsId);
        // 获取全量的数据
        Map<String, RemotePrivateNumberAxbVO> axbMap = noticeFeignBizService
                .axbInfoMap(StreamUtil.ofSet(inquiryRecordFilters, ContactRecord::getSubscriptionId));
        leadsIdGroupMap.forEach((leadsInfoId, records) -> {
            // records 同一个门店id,且customerId是一致，取最新的
            Map<String, List<ContactRecord>> storeUserMap = StreamTools.group(records, v -> v.getStoreId() + "_" + v.getCustomerUserId());
            storeUserMap.forEach((k, rs) -> {
                appendPrivateNumbers(leadsInfoId, rs, axbMap, privateNumbers);

            });
        });
        return StreamTools.group(privateNumbers, PrivateNumberAxbHolder::getLeadsInfoId);
    }

    private void appendPrivateNumbers(Long leadsInfoId, List<ContactRecord> rs,
                                      Map<String, RemotePrivateNumberAxbVO> axbMap,
                                      List<PrivateNumberAxbHolder> privateNumbers) {
        // 取最新的
        ContactRecord leadsStoreInquiryRecord = rs.stream().max(Comparator.comparing(ContactRecord::getCreateTime)).get();
        RemotePrivateNumberAxbVO axbVO = axbMap.get(leadsStoreInquiryRecord.getSubscriptionId());
        if (!axbMap.containsKey(leadsStoreInquiryRecord.getSubscriptionId())) {
            axbVO = getRemotePrivateNumberAxbVO(leadsStoreInquiryRecord);
        }
        privateNumbers.add(PrivateNumberAxbHolder.of(leadsStoreInquiryRecord.getSubscriptionId(), axbVO, leadsInfoId, leadsStoreInquiryRecord.getStoreId()));
    }

    @NotNull
    private RemotePrivateNumberAxbVO getRemotePrivateNumberAxbVO(ContactRecord leadsStoreInquiryRecord) {
        RemotePrivateNumberAxbVO axbVO;
        axbVO = new RemotePrivateNumberAxbVO();
        boolean isMerchantCallCustomer = BindDirectionEnum.getByCode(leadsStoreInquiryRecord.getBindDirection()) == BindDirectionEnum.MERCHANT_CALLER;
        String callerNum = isMerchantCallCustomer ? leadsStoreInquiryRecord.getMerchantNum() : leadsStoreInquiryRecord.getCustomerNum();
        String calleeNum = isMerchantCallCustomer ? leadsStoreInquiryRecord.getCustomerNum() : leadsStoreInquiryRecord.getMerchantNum();
        axbVO.setRelationNum(leadsStoreInquiryRecord.getBindNum());
        axbVO.setCalleeNum(callerNum);
        axbVO.setCallerNum(calleeNum);
        axbVO.setSubscriptionId(leadsStoreInquiryRecord.getSubscriptionId());
        return axbVO;
    }


    protected Map<Long, String> storePhoneWithDefault(Collection<RemoteStoreInfoDetailVO> stores) {
        Map<Long, String> phones = new HashMap<>();
        StreamUtil.of(stores).filter(v -> StringUtils.hasText(v.getContactsPhone())).forEach(v -> {
            phones.put(v.getId(), v.getContactsPhone());
        });
        // 如果为空就获取店长的手机号
        Set<Long> notPhoneStoreIds =
                StreamUtil.of(stores).filter(v -> !StringUtils.hasText(v.getContactsPhone())).map(RemoteStoreInfoDetailVO::getId).collect(Collectors.toSet());
        StreamUtil.of(notPhoneStoreIds).forEach(v -> {
            RemoteStoreUserRelationVO storeManagerByStoreId = melinaFeignBizService.getStoreManagerByStoreId(v);
            if (storeManagerByStoreId == null || storeManagerByStoreId.getPhone() == null) {
                return;
            }
            phones.put(v, storeManagerByStoreId.getPhone());
        });
        return phones;
    }

    protected LeadsMerchantRelation getLastedMerchantRelation(Long leadInfoId, Long storeId) {
        if (leadInfoId == null || storeId == null) {
            return null;
        }
        Map<Long/*门店id*/, List<LeadsMerchantRelation>> relationMap = this.leadsMerchantRelationService.listByCondition(CollUtil.newHashSet(leadInfoId), CollUtil.newHashSet(storeId));
        return StreamUtil.of(relationMap.get(storeId)).findFirst().orElse(null);
    }
}
