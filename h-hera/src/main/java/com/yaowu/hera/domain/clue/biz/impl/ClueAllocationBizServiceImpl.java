package com.yaowu.hera.domain.clue.biz.impl;

import cn.hutool.core.io.IoUtil;
import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.freedom.redis.annotation.DistributedLock;
import com.freedom.web.exception.BusinessException;
import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import com.yaowu.hera.config.nacos.ClueAllocationConfig;
import com.yaowu.hera.domain.clue.biz.IClueAllocationBizService;
import com.yaowu.hera.domain.clue.biz.IClueTicketBizService;
import com.yaowu.hera.domain.clue.biz.exector.IExecutor;
import com.yaowu.hera.domain.clue.service.batis.service.IClueAllocationRuleService;
import com.yaowu.hera.domain.dispatch.biz.IDispatcherCacheBizService;
import com.yaowu.hera.model.bo.ClueAllocationUserGroupBO;
import com.yaowu.hera.model.entity.ticket.ClueAllocationRule;
import com.yaowu.hera.utils.BizException;
import com.yaowu.hera.utils.convertor.clue.ClueTicketMapstruct;
import com.yaowu.hera.utils.drools.DRLGenerator;
import com.yaowu.heraapi.model.dto.clue.HeraEditRuleDTO;
import com.yaowu.heraapi.model.dto.clue.HeraRuleDTO;
import com.yaowu.heraapi.model.dto.clue.HeraRuleFieldDTO;
import com.yaowu.heraapi.model.vo.clue.HeraUserGroupVO;
import com.yaowu.heraapi.model.vo.clue.HeraClueAllocationRuleVO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import jakarta.annotation.Nullable;
import java.io.IOException;
import java.io.InputStream;
import java.nio.charset.StandardCharsets;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.IntStream;

import static com.yaowu.hera.utils.ErrorCode.ALLOCATION_RULE_REPEAT;
import static com.yaowu.hera.utils.ErrorCode.INNER_ERROR;

/**
 * <AUTHOR>
 * @email <EMAIL>
 * @description 功能：
 * @creatTime 2024.05.27 17:03:00
 */
@Slf4j
@Service
public class ClueAllocationBizServiceImpl implements IClueAllocationBizService {
    public static final String RULE_NAME_PREFIX = "规则";
    public static final String FALLBACK_RULE_NAME = "兜底规则";
    public static final String PLATFORM_SALES_BD_ID = "platformSalesBdId";

    @Autowired
    private ClueAllocationConfig clueAllocationConfig;

    @Autowired
    private IClueAllocationRuleService clueAllocationRuleService;

    @Autowired
    private IDispatcherCacheBizService dispatcherCacheBizService;

    @Autowired
    private ClueTicketMapstruct clueTicketMapstruct;

    @Autowired
    private IExecutor droolsExecutor;

    @Lazy
    @Autowired
    private IClueTicketBizService clueTicketBizService;

    @Override
    public List<HeraRuleFieldDTO> getRuleField() {
        return clueAllocationConfig.getRuleField();
    }

    @Override
    public List<Object> getRuleElement(String field) {
        return clueAllocationConfig.getRuleElement().getOrDefault(field, Lists.newArrayList());
    }

    @Override
    public List<HeraUserGroupVO> getUserGroupDTO() {
        return clueAllocationConfig.getUserGroups().stream()
                .map(e -> new HeraUserGroupVO()
                        .setGroupCode(e.getGroupCode()).setGroupName(e.getGroupName())
                        .setUserInfos(e.getUserInfos().stream()
                                .map(ClueAllocationUserGroupBO.UserInfo::getUserName)
                                .collect(Collectors.toList()))
                ).collect(Collectors.toList());
    }

    @Override
    public boolean saveClueAllocationRule(HeraEditRuleDTO editRuleDTO) {
        HashSet<List<HeraRuleDTO.Condition>> conditionCollection = Sets.newHashSet();
        // 遍历规则集并检查是否有重复的规则
        editRuleDTO.getRules().forEach(rule ->
                BizException.condition(!conditionCollection.add(rule.getConditions()), ALLOCATION_RULE_REPEAT));
        conditionCollection.clear();

        // 获取规则代码并生成规则内容
        String ruleCode = clueAllocationConfig.getRuleCode();
        IntStream.range(0, editRuleDTO.getRules().size())
                .forEach(index -> {
                    HeraRuleDTO rule = editRuleDTO.getRules().get(index);
                    rule.setName(RULE_NAME_PREFIX + index);
                    if (StrUtil.isBlank(rule.getId())) {
                        rule.setId(IdUtil.nanoId(6));
                    }
                });
        String ruleContent = DRLGenerator.generateDRL(editRuleDTO.getRules());
        // 保存或更新规则
        saveOrUpdateRule(ruleCode, ruleContent, editRuleDTO);
        // 更新分配缓存
        clueTicketBizService.refreshDispatchCache();
        return true;
    }


    @Override
    public boolean uploadAllocationRule(MultipartFile file, String ruleCode) {
        String ruleContent = getContent(file);
        log.info("上传分配规则文件，ruleCode:{},文件内容：{}", ruleCode, ruleContent);
        return saveOrUpdateRule(ruleCode, ruleContent, new HeraEditRuleDTO());
    }

    @Override
    public HeraClueAllocationRuleVO getClueAllocationRule() {
        String ruleCode = clueAllocationConfig.getRuleCode();
        ClueAllocationRule allocationRule = clueAllocationRuleService.getByCode(ruleCode);
        if (Objects.isNull(allocationRule)) {
            return null;
        }
        HeraEditRuleDTO ruleStructure = allocationRule.getRuleStructure();
        if (Objects.isNull(ruleStructure)) {
            return null;
        }
        return  clueTicketMapstruct.toHeraEditRuleVO(allocationRule);
    }


    @Override
    @DistributedLock(key = "h-hera:com.yaowu.hera.domain.clue.biz.impl.ClueAllocationBizServiceImpl.allocation")
    public boolean allocation(String ruleId, Integer groupCode, Map<String, Object> paramMap) {
        log.info("线索分配人员技能组 ruleId = {} groupCode = {}, paramMap = {}", ruleId, groupCode, JSON.toJSONString(paramMap));
        // 按 ruleId + groupCode 分组并将嵌套列表扁平化
        Map<String, List<ClueAllocationUserGroupBO.UserInfo>> ruleIdGroupCodeToUsers = dispatcherCacheBizService.getRuleIdGroupCodeToUsersCache();
        if (Objects.isNull(ruleIdGroupCodeToUsers)) {
            log.warn("获取电销排班缓存数据为空");
            return false;
        }
        String key = ruleId + "-" + groupCode;
        List<ClueAllocationUserGroupBO.UserInfo> userInfos = ruleIdGroupCodeToUsers.getOrDefault(key, new ArrayList<>());
        ClueAllocationUserGroupBO.UserInfo userInfo = pickUser(userInfos);
        if (Objects.isNull(userInfo)) {
            log.warn("电销人员技能组 groupCode = {}，人员信息为空", groupCode);
            return false;
        }
        log.info("线索分配的人员信息 userInfo  = {}", userInfo);
        paramMap.put(PLATFORM_SALES_BD_ID, userInfo.getUserId());
        dispatcherCacheBizService.removeAndSetGroupUser(ruleIdGroupCodeToUsers);
        return true;
    }

    @Override
    public @Nullable ClueAllocationUserGroupBO.UserInfo pickUser(List<ClueAllocationUserGroupBO.UserInfo> userInfos) {
        ClueAllocationUserGroupBO.UserInfo cursorUser = userInfos.stream()
                .filter(u -> Objects.nonNull(u.getCursor()))
                .filter(ClueAllocationUserGroupBO.UserInfo::getCursor)
                .findFirst()
                .orElseThrow(() -> new BusinessException("电销分配失败 找不到游标节点"));

        Map<Long, ClueAllocationUserGroupBO.UserInfo> userIdToUser = userInfos.stream()
                .collect(Collectors.toMap(ClueAllocationUserGroupBO.UserInfo::getUserId,
                        Function.identity(), (v1, v2) -> v1));
        return ClueAllocationUserGroupBO.findNextUser(userIdToUser, cursorUser);
    }


    private String getContent(MultipartFile multipartFile) {
        String content = null;
        try (InputStream inputStream = multipartFile.getInputStream()) {
            content = IoUtil.read(inputStream, StandardCharsets.UTF_8);
        } catch (IOException e) {
            log.error("Description Failed to read the file. The information is abnormal：", e);
            throw BizException.newOne(INNER_ERROR);
        }
        return content;
    }

    private boolean saveOrUpdateRule(String ruleCode, String ruleContent, HeraEditRuleDTO editRuleDTO) {
        ClueAllocationRule allocationRule = clueAllocationRuleService.getByCode(ruleCode);
        if (Objects.isNull(allocationRule)) {
            allocationRule = new ClueAllocationRule();
            allocationRule.setRuleCode(ruleCode);
            allocationRule.setRuleContent(ruleContent);
            allocationRule.setRuleStructure(editRuleDTO);
            return clueAllocationRuleService.save(allocationRule);
        }
        allocationRule.setRuleContent(ruleContent);
        allocationRule.setRuleStructure(editRuleDTO);
        BizException.condition(!clueAllocationRuleService.updateById(allocationRule), INNER_ERROR);
        droolsExecutor.clearIfPresent(ruleCode);
        return true;
    }

}
