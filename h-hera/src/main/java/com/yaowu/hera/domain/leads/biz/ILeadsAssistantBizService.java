package com.yaowu.hera.domain.leads.biz;

import com.yaowu.heraapi.model.dto.common.RemoteIdDTO;
import com.yaowu.heraapi.model.dto.mtl.*;
import com.yaowu.heraapi.model.vo.mtl.*;
import com.freedom.web.model.resp.BaseResult;
import com.yaowu.heraapi.model.dto.mtl.*;
import com.yaowu.heraapi.model.vo.mtl.HeraLeadsCancelVO;
import com.yaowu.heraapi.model.vo.mtl.HeraLeadsInfoWithCallRecordVO;
import com.yaowu.heraapi.model.vo.mtl.HeraRatingRuleConfigVO;
import com.yaowu.heraapi.model.vo.mtl.RemoteLeadsSubmitVO;
import com.yaowu.heraapi.model.vo.mtl.leadsinfo.RemoteCloseRequirementInfoVO;
import com.yaowu.heraapi.model.vo.mtl.order.RemotePreCheckOrderRefundVO;
import jakarta.validation.constraints.NotBlank;
import org.hibernate.validator.constraints.Length;
import org.springframework.validation.annotation.Validated;

import java.util.Set;

/**
 * <AUTHOR>
 * @email <EMAIL>
 * @creatTime 2024.10.18 15:40:00
 * @description: assistant
 */

@Validated
public interface ILeadsAssistantBizService {

    /**
     * 线索取消、现阶段该功能只有抖音小程序使用
     *
     * @param dto
     * @return
     */
    HeraLeadsCancelVO cancelLeads(HeraLeadsInfoCancelDTO dto);

    /**
     * 企业微信机器人取消
     * @param phone
     * @param cancelRemark
     * @return
     */
    Set<Long> cancelLeadsWeChatRobot(@NotBlank(message = "电话不允许为空") String phone, @Length(max = 128, message = "取消备注不允许超过128个字符") String cancelRemark);

    /**
     * 配置清洗规则因子
     *
     * @param dto
     */
    void configRatingRuleElement(HeraRatingRuleConfigDTO dto);

    /**
     * 获取清洗规则因子
     *
     * @return
     */
    HeraRatingRuleConfigVO getRatingRuleElement();

    /**
     * 提交报价单线索和自动分配
     *
     * @return 线索ID
     */
    RemoteLeadsSubmitVO submit(RemoteLeadsSubmitDTO submitDTO);

    /**
     * 我的线索详情
     * @param dto
     * @return
     */
    HeraLeadsInfoWithCallRecordVO leadsDetailWithCallRecord(HeraLeadsDetailWithCallRecordDTO dto);


    /**
     * 用户直接取消报价单线索
     */
    Boolean cancelMtlLeadsDirectly(HeraLeadsInfoDirectCancelDTO dto);

    void processHistoryCancelClueRecord();

    /**
     * 用户取消线索补充取消原因
     */
    Boolean supplementCancelReason(HeraLeadsInfoSupplementCancelDTO dto);

    /**
     * 线索催单
     * @param dto
     * @return
     */
    boolean leadsUrgent(RemoteIdDTO dto);

    /**
     * 查询最近未过期的线索-频控
     * @param dto
     * @return
     */
    RemoteLeadsSimpleVO queryLatestUnexpiredLeads(RemoteQueryLatestUnexpiredLeadsDTO dto);

    RemoteCloseRequirementInfoVO getCloseRequirementInfo(RemoteCloseRequirementInfoQueryDTO dto);

    Boolean firstFollowUpLeadsUrgent(RemoteFirstFollowUpClueDTO dto);

    /**
     * 抖音小程序提交表单
     * @param dto
     * @return
     */
    Long douYinSubmitForm(HeraLeadsSubmitFormDTO dto);
}
