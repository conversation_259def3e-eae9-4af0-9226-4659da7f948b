package com.yaowu.hera.domain.quotation.service.batis.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.yaowu.hera.domain.quotation.service.batis.mapper.PriceDailyQuotesMapper;
import com.yaowu.hera.domain.quotation.service.batis.service.IPriceDailyQuotesService;
import com.yaowu.hera.model.bo.pricetool.PriceDailyQuotesBO;
import com.yaowu.hera.model.entity.price.PriceDailyQuotes;
import com.yaowu.hera.utils.StringUtil;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * <p>
 * 文章数据表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-11-04
 */
@Service
public class PriceDailyQuotesServiceImpl extends ServiceImpl<PriceDailyQuotesMapper, PriceDailyQuotes> implements IPriceDailyQuotesService {


    @Override
    public List<PriceDailyQuotes> listByQuoteTime(PriceDailyQuotesBO priceDailyQuotesBO) {
        LambdaQueryWrapper<PriceDailyQuotes> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(Objects.nonNull(priceDailyQuotesBO.getCategoryId()), PriceDailyQuotes::getCategoryId, priceDailyQuotesBO.getCategoryId());
        wrapper.eq(StringUtil.isNotBlank(priceDailyQuotesBO.getQuoteTime()),PriceDailyQuotes::getQuoteTime,priceDailyQuotesBO.getQuoteTime());
        if (Objects.nonNull(priceDailyQuotesBO.getStartQuoteDate()) && Objects.nonNull(priceDailyQuotesBO.getEndQuoteDate())){
            Set<String> dates = priceDailyQuotesBO.getStartQuoteDate()
                    .datesUntil(priceDailyQuotesBO.getEndQuoteDate().plusDays(1))
                    .map(LocalDate::toString)
                    .collect(Collectors.toSet());
            wrapper.in(CollectionUtil.isNotEmpty(dates),PriceDailyQuotes::getQuoteTime,dates);
        }
        wrapper.eq(Objects.nonNull(priceDailyQuotesBO.getAreaLevel()), PriceDailyQuotes::getAreaLevel, priceDailyQuotesBO.getAreaLevel());
        wrapper.eq(StringUtil.isNotBlank(priceDailyQuotesBO.getCityCode()), PriceDailyQuotes::getAreaCode, priceDailyQuotesBO.getCityCode());
        wrapper.eq(StringUtil.isNotBlank(priceDailyQuotesBO.getQuoteTime()), PriceDailyQuotes::getQuoteTime, priceDailyQuotesBO.getQuoteTime());
        wrapper.eq(Objects.nonNull(priceDailyQuotesBO.getQuoteType()),PriceDailyQuotes::getQuoteType, priceDailyQuotesBO.getQuoteType());
        if (Objects.nonNull(priceDailyQuotesBO.getQuoteVersion())) {
            wrapper.eq(PriceDailyQuotes::getQuoteVersion, priceDailyQuotesBO.getQuoteVersion());
        }else {
            //只查询本地租价1.0的数据
            wrapper.eq(PriceDailyQuotes::getQuoteVersion,1);
        }
        wrapper.orderByDesc(PriceDailyQuotes::getQuoteTime);
        return list(wrapper);
    }

    @Override
    public List<PriceDailyQuotes> listByQuoteTime(LocalDate fromDate, LocalDate toDate) {
        LambdaQueryWrapper<PriceDailyQuotes> wrapper = new LambdaQueryWrapper<>();
        List<String> dateList = fromDate.datesUntil(toDate.plusDays(1)).map(LocalDate::toString).toList();
        wrapper.in(CollectionUtil.isNotEmpty(dateList),PriceDailyQuotes::getQuoteTime,dateList);
        //只查询本地租价1.0的数据
        wrapper.eq(PriceDailyQuotes::getQuoteVersion,1);
        wrapper.orderByDesc(PriceDailyQuotes::getQuoteTime);
        return list(wrapper);
    }

    @Override
    public List<PriceDailyQuotes> listByCondition(List<String> quoteTimeList, List<Long> categoryIds, List<String> areaCodes) {
        if (CollectionUtils.isEmpty(quoteTimeList) || CollectionUtils.isEmpty(categoryIds) || CollectionUtils.isEmpty(areaCodes)) {
            return Collections.emptyList();
        }

        LambdaQueryWrapper<PriceDailyQuotes> wrapper = new LambdaQueryWrapper<>();

        // 优化查询条件顺序：先过滤选择性高的条件
        // 1. 先过滤 delete_flag=0 (选择性最高，只有3.18%的数据)
        wrapper.eq(PriceDailyQuotes::getDeleteFlag, 0);

        // 2. 再过滤 quote_version=1 (进一步缩小范围)
        wrapper.eq(PriceDailyQuotes::getQuoteVersion, 1);

        // 3. 然后是时间范围 (通常选择性较高)
        wrapper.in(CollectionUtil.isNotEmpty(quoteTimeList), PriceDailyQuotes::getQuoteTime, quoteTimeList);

        // 4. 品类ID (选择性中等)
        wrapper.in(CollectionUtil.isNotEmpty(categoryIds), PriceDailyQuotes::getCategoryId, categoryIds);

        // 5. 最后是区域编码 (选择性相对较低，但已经被前面条件大幅过滤)
        wrapper.in(CollectionUtil.isNotEmpty(areaCodes), PriceDailyQuotes::getAreaCode, areaCodes);

        return list(wrapper);
    }

    @Override
    public boolean deleteHistoryData(LocalDate startTime) {
        if (Objects.isNull(startTime)){
            return false;
        }
        LocalDateTime localDate = startTime.minusDays(7).atStartOfDay();
        LambdaQueryWrapper<PriceDailyQuotes> wrapper = new LambdaQueryWrapper<>();
        wrapper.lt(PriceDailyQuotes::getGenerateTime, localDate);
        //只删除本地租价1.0的数据
        wrapper.eq(PriceDailyQuotes::getQuoteVersion,1);
        return remove(wrapper);
    }
}
