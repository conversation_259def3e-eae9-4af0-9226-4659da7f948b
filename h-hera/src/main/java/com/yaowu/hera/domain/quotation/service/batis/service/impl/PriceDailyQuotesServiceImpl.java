package com.yaowu.hera.domain.quotation.service.batis.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.yaowu.hera.domain.quotation.service.batis.mapper.PriceDailyQuotesMapper;
import com.yaowu.hera.domain.quotation.service.batis.service.IPriceDailyQuotesService;
import com.yaowu.hera.model.bo.pricetool.PriceDailyQuotesBO;
import com.yaowu.hera.model.entity.price.PriceDailyQuotes;
import com.yaowu.hera.utils.StringUtil;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * <p>
 * 文章数据表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-11-04
 */
@Service
public class PriceDailyQuotesServiceImpl extends ServiceImpl<PriceDailyQuotesMapper, PriceDailyQuotes> implements IPriceDailyQuotesService {


    @Override
    public List<PriceDailyQuotes> listByQuoteTime(PriceDailyQuotesBO priceDailyQuotesBO) {
        LambdaQueryWrapper<PriceDailyQuotes> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(Objects.nonNull(priceDailyQuotesBO.getCategoryId()), PriceDailyQuotes::getCategoryId, priceDailyQuotesBO.getCategoryId());
        wrapper.eq(StringUtil.isNotBlank(priceDailyQuotesBO.getQuoteTime()),PriceDailyQuotes::getQuoteTime,priceDailyQuotesBO.getQuoteTime());
        if (Objects.nonNull(priceDailyQuotesBO.getStartQuoteDate()) && Objects.nonNull(priceDailyQuotesBO.getEndQuoteDate())){
            Set<String> dates = priceDailyQuotesBO.getStartQuoteDate()
                    .datesUntil(priceDailyQuotesBO.getEndQuoteDate().plusDays(1))
                    .map(LocalDate::toString)
                    .collect(Collectors.toSet());
            wrapper.in(CollectionUtil.isNotEmpty(dates),PriceDailyQuotes::getQuoteTime,dates);
        }
        wrapper.eq(Objects.nonNull(priceDailyQuotesBO.getAreaLevel()), PriceDailyQuotes::getAreaLevel, priceDailyQuotesBO.getAreaLevel());
        wrapper.eq(StringUtil.isNotBlank(priceDailyQuotesBO.getCityCode()), PriceDailyQuotes::getAreaCode, priceDailyQuotesBO.getCityCode());
        wrapper.eq(StringUtil.isNotBlank(priceDailyQuotesBO.getQuoteTime()), PriceDailyQuotes::getQuoteTime, priceDailyQuotesBO.getQuoteTime());
        wrapper.eq(Objects.nonNull(priceDailyQuotesBO.getQuoteType()),PriceDailyQuotes::getQuoteType, priceDailyQuotesBO.getQuoteType());
        if (Objects.nonNull(priceDailyQuotesBO.getQuoteVersion())) {
            wrapper.eq(PriceDailyQuotes::getQuoteVersion, priceDailyQuotesBO.getQuoteVersion());
        }else {
            //只查询本地租价1.0的数据
            wrapper.eq(PriceDailyQuotes::getQuoteVersion,1);
        }
        wrapper.orderByDesc(PriceDailyQuotes::getQuoteTime);
        return list(wrapper);
    }

    @Override
    public List<PriceDailyQuotes> listByQuoteTime(LocalDate fromDate, LocalDate toDate) {
        LambdaQueryWrapper<PriceDailyQuotes> wrapper = new LambdaQueryWrapper<>();
        List<String> dateList = fromDate.datesUntil(toDate.plusDays(1)).map(LocalDate::toString).toList();
        wrapper.in(CollectionUtil.isNotEmpty(dateList),PriceDailyQuotes::getQuoteTime,dateList);
        //只查询本地租价1.0的数据
        wrapper.eq(PriceDailyQuotes::getQuoteVersion,1);
        wrapper.orderByDesc(PriceDailyQuotes::getQuoteTime);
        return list(wrapper);
    }

    @Override
    public List<PriceDailyQuotes> listByCondition(List<String> quoteTimeList, List<Long> categoryIds, List<String> areaCodes) {
        if (CollectionUtils.isEmpty(quoteTimeList) || CollectionUtils.isEmpty(categoryIds) || CollectionUtils.isEmpty(areaCodes)) {
            return Collections.emptyList();
        }

        LambdaQueryWrapper<PriceDailyQuotes> wrapper = new LambdaQueryWrapper<>();

        // 基于现有索引 idx_quotetime_categoryid_areacode_quoteversion_deleteflag 优化查询条件顺序
        // 严格按照索引字段顺序构建查询条件，确保索引能被有效利用

        // 1. quote_time (索引第一个字段) - 时间条件通常选择性较好
        wrapper.in(CollectionUtil.isNotEmpty(quoteTimeList), PriceDailyQuotes::getQuoteTime, quoteTimeList);

        // 2. category_id (索引第二个字段) - 品类过滤
        wrapper.in(CollectionUtil.isNotEmpty(categoryIds), PriceDailyQuotes::getCategoryId, categoryIds);

        // 3. area_code (索引第三个字段) - 区域过滤，这是慢查询的主要原因
        wrapper.in(CollectionUtil.isNotEmpty(areaCodes), PriceDailyQuotes::getAreaCode, areaCodes);

        // 4. quote_version (索引第四个字段) - 版本过滤
        wrapper.eq(PriceDailyQuotes::getQuoteVersion, 1);

        // 5. delete_flag (索引第五个字段) - 逻辑删除过滤，选择性最高但在索引末尾
        wrapper.eq(PriceDailyQuotes::getDeleteFlag, 0);

        return list(wrapper);
    }

    /**
     * 优化版本：使用更高效的查询策略
     * 基于数据统计：总数据1,113,583条，有效数据35,424条(3.18%)
     * 当区域编码数量过多时，使用分批查询避免慢SQL
     */
    public List<PriceDailyQuotes> listByConditionOptimized(List<String> quoteTimeList, List<Long> categoryIds, List<String> areaCodes) {
        if (CollectionUtils.isEmpty(quoteTimeList) || CollectionUtils.isEmpty(categoryIds) || CollectionUtils.isEmpty(areaCodes)) {
            return Collections.emptyList();
        }

        // 根据实际数据分布调整分批策略
        // 考虑到有效数据只有3.18%，可以适当增加批次大小
        int batchSize = determineBatchSize(areaCodes.size());

        if (areaCodes.size() <= batchSize) {
            // 区域编码数量在合理范围内，直接查询
            return listByCondition(quoteTimeList, categoryIds, areaCodes);
        }

        // 分批查询，避免IN条件过大
        List<PriceDailyQuotes> result = new ArrayList<>();
        int totalBatches = (int) Math.ceil((double) areaCodes.size() / batchSize);

        log.info("区域编码数量过多({}个)，将分{}批查询，每批{}个", areaCodes.size(), totalBatches, batchSize);

        for (int i = 0; i < areaCodes.size(); i += batchSize) {
            int endIndex = Math.min(i + batchSize, areaCodes.size());
            List<String> batchAreaCodes = areaCodes.subList(i, endIndex);

            log.debug("执行第{}批查询，区域编码数量：{}", (i / batchSize + 1), batchAreaCodes.size());

            List<PriceDailyQuotes> batchResult = listByCondition(quoteTimeList, categoryIds, batchAreaCodes);
            if (CollectionUtil.isNotEmpty(batchResult)) {
                result.addAll(batchResult);
                log.debug("第{}批查询完成，返回{}条数据", (i / batchSize + 1), batchResult.size());
            }
        }

        log.info("分批查询完成，总共返回{}条数据", result.size());
        return result;
    }

    /**
     * 根据区域编码数量动态确定批次大小
     * 基于经验值和数据库性能调优
     */
    private int determineBatchSize(int totalAreaCodes) {
        if (totalAreaCodes <= 50) {
            return 50;  // 小量数据直接查询
        } else if (totalAreaCodes <= 200) {
            return 30;  // 中等数据量
        } else {
            return 20;  // 大量数据，使用较小批次
        }
    }

    @Override
    public boolean deleteHistoryData(LocalDate startTime) {
        if (Objects.isNull(startTime)){
            return false;
        }
        LocalDateTime localDate = startTime.minusDays(7).atStartOfDay();
        LambdaQueryWrapper<PriceDailyQuotes> wrapper = new LambdaQueryWrapper<>();
        wrapper.lt(PriceDailyQuotes::getGenerateTime, localDate);
        //只删除本地租价1.0的数据
        wrapper.eq(PriceDailyQuotes::getQuoteVersion,1);
        return remove(wrapper);
    }
}
