package com.yaowu.hera.domain.mtl.leadsorder.refund.impl;

import com.google.common.collect.Maps;
import com.yaowu.hera.domain.leads.service.batis.service.ILeadsInfoCustomerCancelService;
import com.yaowu.hera.domain.mtl.leadsorder.refund.BaseOrderRefund;
import com.yaowu.hera.model.bo.mtl.SubmitOrderRefundContext;
import com.yaowu.hera.model.entity.leads.LeadsInfoCustomerCancel;
import com.yaowu.hera.model.entity.leads.LeadsOrderInfo;
import com.yaowu.hera.model.entity.leads.LeadsOrderInfoRefund;
import com.yaowu.hera.utils.BizException;
import com.yaowu.hera.utils.ErrorCode;
import com.yaowu.hera.utils.constants.PushConstants;
import com.yaowu.heraapi.enums.mtl.RemoteClueCancelChannelEnum;
import com.yaowu.heraapi.enums.mtl.order.LeadsRefundTypeEnum;
import com.yaowu.heraapi.model.dto.mtl.order.HeraOrderRefundDTO;
import com.yaowu.notice.constant.enums.ChannelEnum;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * <AUTHOR> Weiyang
 * @email <EMAIL>
 * @creatTime 2024.11.18 10:43:00
 * @description:
 */
@Slf4j
@Component
public class AutoOrderRefund extends BaseOrderRefund {


    @Resource
    private ILeadsInfoCustomerCancelService leadsInfoCustomerCancelService;

    @Override
    public boolean supports(LeadsRefundTypeEnum leadsRefundTypeEnum) {
        return LeadsRefundTypeEnum.CANCEL_LEADS_REFUND == leadsRefundTypeEnum;
    }

    @Override
    public boolean doSubmitRefund(SubmitOrderRefundContext context) {
        LeadsOrderInfo leadsOrderInfo = context.getLeadsOrderInfo();
        HeraOrderRefundDTO submitDto = context.getSubmitDto();
        LeadsOrderInfoRefund init = leadsOrderInfoRefundMapstruct.init(submitDto, leadsOrderInfo, LeadsRefundTypeEnum.CANCEL_LEADS_REFUND);
        init.setRequesterName(autoRequesterName);
        BizException.isFail(leadsOrderInfoRefundService.save(init), ErrorCode.INNER_ERROR);
        context.setLeadsOrderInfoRefund(init);
        return true;
    }


    @Override
    public boolean judgeAutoRefund(LeadsOrderInfo leadsOrderInfo) {
        if (Boolean.FALSE.equals(leadsRatingSupport.middleCallEnable())) {
            log.info("外呼中间号降级期间，线索取消不支持自动退款");
            return false;
        }
        // 默认自动审核
        return orderRefundSupport.judgeAutoRefundForLeadsCancel(leadsOrderInfo);
    }

    @Override
    public void autoRefundAppPush(LeadsOrderInfo leadsOrderInfo, LeadsInfoCustomerCancel leadsInfoCustomerCancel) {
        // 如果是线索取消是关闭类型，发送线索关闭的APP通知
        if (isLeadsClose(leadsInfoCustomerCancel)) {
            // 订单退款成功发送消息
            leadsOrderInfoBizService.asyncAppPushOrderRefundNotify(leadsOrderInfo,
                    ChannelEnum.STORE_FRONT_DESK_APP,
                    PushConstants.H_HERA_MTL_CLOSE_ORDER_AUTO_REFUND_SUCCESS,
                    Maps.newHashMap());
            return;
        }
        // 订单退款成功发送消息
        leadsOrderInfoBizService.asyncAppPushOrderRefundNotify(leadsOrderInfo,
                ChannelEnum.STORE_FRONT_DESK_APP,
                PushConstants.H_HERA_MTL_ORDER_AUTO_REFUND_SUCCESS,
                Maps.newHashMap());
    }

    @Override
    protected String whenPermitGetMsgTitle(LeadsInfoCustomerCancel leadsInfoCustomerCancel) {
        if (isLeadsClose(leadsInfoCustomerCancel)) {
            return  "因客户设置“线索已找到设备”，关联订单自动完成退款";
        }
        return "有1条因线索取消，关联订单自动完成退款";

    }

    private boolean isLeadsClose(LeadsInfoCustomerCancel leadsInfoCustomerCancel) {
        if (leadsInfoCustomerCancel == null) {
            return false;
        }
        RemoteClueCancelChannelEnum cancelChannelEnum = RemoteClueCancelChannelEnum.getByCode(leadsInfoCustomerCancel.getChannel());
        return cancelChannelEnum == RemoteClueCancelChannelEnum.APPLET_USER_ACTIVE_LEADS_CLOSE;
    }
}
