package com.yaowu.hera.domain.tools.biz;

import com.freedom.web.model.resp.BasePage;
import com.yaowu.heraapi.model.dto.tools.RemoteChannelPosterImgPageDTO;
import com.yaowu.heraapi.model.dto.tools.RemoteGeneratePosterDTO;
import com.yaowu.heraapi.model.dto.tools.RemoteGetByChannelInfoDTO;
import com.yaowu.heraapi.model.vo.tools.RemoteChannelPosterImgPageVO;
import com.yaowu.heraapi.model.vo.tools.RemoteGeneratePosterVO;
import com.yaowu.heraapi.model.vo.tools.RemoteGetByChannelInfoVO;

/**
 * @Author: liuzhenpeng
 * @Date: 2025/2/14 11:39
 */
public interface IMerchantSettleInToolsBizService {
    /**
     * 海报列表分页
     *
     * @param dto
     * @return
     */
    BasePage<RemoteChannelPosterImgPageVO> getPosterPage(RemoteChannelPosterImgPageDTO dto);

    /**
     * 根据渠道码获取渠道公司信息
     * @param dto
     * @return
     */
    RemoteGetByChannelInfoVO getByChannelInfo(RemoteGetByChannelInfoDTO dto);

//    /**
//     * 提交生成海报记录日志
//     *
//     * @param dto
//     * @return
//     */
//    Long addLog(RemoteChannelGenerateLogDTO dto);

    /**
     * 生成海报+二维码
     *
     * @param dto
     * @return
     */
    RemoteGeneratePosterVO generatePoster(RemoteGeneratePosterDTO dto);
}
