package com.yaowu.hera.domain.popularmerchant.service.batis.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.yaowu.hera.model.dto.popularMerchant.PopularMerchantQueryDTO;
import com.yaowu.hera.model.entity.popluarmerchant.StoreHot;
import com.yaowu.hera.model.dto.popularMerchant.PopularMerchatPageDTO;

import java.util.List;


/**
 * <p>
 * 热门商户记录表 服务类
 * </p>
 */
public interface IPopularMerchantService extends IService<StoreHot> {

    Boolean updatePopularMerchant(Long id,Long storeId, String provinceJson);

    StoreHot getPopularMerchantInfoByStoreId(Long storeId);


    Page<StoreHot> pagePopularMerchant(PopularMerchatPageDTO dto);
    List<StoreHot> listPopularMerchant(PopularMerchantQueryDTO dto);
}
