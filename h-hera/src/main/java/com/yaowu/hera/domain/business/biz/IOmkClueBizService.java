package com.yaowu.hera.domain.business.biz;

import com.freedom.web.model.resp.BasePage;
import com.yaowu.hera.model.entity.business.BusinessInfo;
import com.yaowu.hera.model.entity.business.OmkInfo;
import com.yaowu.hera.model.vo.clue.GeneralizedCustomerSourceVO;
import com.yaowu.heraapi.model.dto.omk.HandOverClueInfoDTO;
import com.yaowu.heraapi.model.dto.omk.HeraClueCancelDefeatDTO;
import com.yaowu.heraapi.model.dto.omk.RemoteOmkClueAddDTO;
import com.yaowu.heraapi.model.dto.omk.RemoteOmkClueDefeatDTO;
import com.yaowu.heraapi.model.dto.omk.RemoteOmkClueEditDTO;
import com.yaowu.heraapi.model.dto.omk.RemoteOmkCluePageDTO;
import com.yaowu.heraapi.model.dto.omk.RemoteOmkClueStatusCntDTO;
import com.yaowu.heraapi.model.dto.omk.RemoteOmkCreateOrderDTO;
import com.yaowu.heraapi.model.dto.omk.RemoteOmkMerchantRemarkAddDTO;
import com.yaowu.heraapi.model.dto.omk.RemoteOmkMerchantRemarkPageDTO;
import com.yaowu.heraapi.model.dto.omk.RemoteOmkRepeatOrderDTO;
import com.yaowu.heraapi.model.pojo.common.RemoteOmkChannel;
import com.yaowu.heraapi.model.pojo.common.RemoteOmkState;
import com.yaowu.heraapi.model.vo.business.HeraClueStatisticsVO;
import com.yaowu.heraapi.model.vo.business.RemoteBusinessCreateOrderCheckVO;
import com.yaowu.heraapi.model.vo.omk.RemoteOmkClueDetailVO;
import com.yaowu.heraapi.model.vo.omk.RemoteOmkClueOpVO;
import com.yaowu.heraapi.model.vo.omk.RemoteOmkCluePageVO;
import com.yaowu.heraapi.model.vo.omk.RemoteOmkClueStatusCntVO;
import com.yaowu.heraapi.model.vo.omk.RemoteOmkCreateOrderResultVO;
import com.yaowu.heraapi.model.vo.omk.RemoteOmkMerchantRemarkVO;
import com.yaowu.heraapi.model.vo.omk.RemoteOmkMerchantRemarksVO;

import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * <AUTHOR>
 * @since 2023/8/4 17:22
 **/
public interface IOmkClueBizService {

    /**
     * 电销线索分页列表
     *
     * @param dto RemoteOmkCluePageDTO
     * @return 线索分页列表
     */
    BasePage<RemoteOmkCluePageVO> page(RemoteOmkCluePageDTO dto);

    /**
     * 添加电销线索
     *
     * @param dto RemoteOmkClueAddDTO
     * @return 添加电销线索结果
     */
    RemoteOmkClueOpVO add(RemoteOmkClueAddDTO dto);

    /**
     * 添加电销线索并创建订单
     *
     * @param dto RemoteOmkClueAddDTO
     * @return 添加电销线索结果
     */
    RemoteOmkCreateOrderResultVO addAndCreateOrder(RemoteOmkClueAddDTO dto);

    /**
     * 前置校验-添加电销线索并创建订单
     *
     * @param dto RemoteOmkClueAddDTO
     * @return 前置校验结果
     */
    RemoteBusinessCreateOrderCheckVO addAndCreateOrderPreCheck(RemoteOmkClueAddDTO dto);

    /**
     * 编辑电销线索
     *
     * @param dto RemoteOmkClueAddDTO
     * @return 添加电销线索结果
     */
    RemoteOmkClueOpVO edit(RemoteOmkClueEditDTO dto);

    /**
     * 电销线索详情
     *
     * @param id id
     * @return 编辑电销线索结果
     */
    RemoteOmkClueDetailVO detail(Long id);

    /**
     * 战败
     *
     * @param dto RemoteOmkClueDefeatDTO
     * @return 结果
     */
    Boolean defeat(RemoteOmkClueDefeatDTO dto);

    /**
     * 获取线索状态列表
     *
     * @return 状态列表
     */
    List<RemoteOmkState> stateList();

    /**
     * 电销线索渠道列表
     *
     * @return 电销线索渠道列表
     */
    List<RemoteOmkChannel> channelList();

    /**
     * 电销线索创建订单
     *
     * @param dto RemoteOmkCreateOrderDTO
     * @return 电销线索创建订单结果
     */
    RemoteOmkCreateOrderResultVO createOrder(RemoteOmkCreateOrderDTO dto);

    /**
     * 根据电话号码获取线索渠道
     *
     * @param customerPhone
     * @return 渠道
     */
    String getOnlineSalesCustomerSource(String customerPhone);

    /**
     * 根据客户id 获取线索渠道
     *
     * @param customerId
     * @return 渠道
     */
    String getOnlineSalesCustomerSourceByCustomerId(Long customerId);

    /**
     * 电销线索取消战败
     *
     * @param dto
     * @return 是否成功
     */
    boolean cancelDefeatClue(HeraClueCancelDefeatDTO dto);

    /**
     * 再来一单
     */
    RemoteOmkCreateOrderResultVO repeatOrder(RemoteOmkRepeatOrderDTO dto);


    /**
     * 线索转交
     *
     * @param dto
     * @return
     */
    Boolean handOverClueInfo(HandOverClueInfoDTO dto);

    /**
     * 电销线索-根据商户ID获取商户评价
     *
     * @param merchantId
     * @return
     */
    List<RemoteOmkMerchantRemarkVO> listRemarksByMerchantId(Long merchantId);


    /**
     * 批量获取商户评价
     *
     * @param merchantIds
     * @return
     */
    RemoteOmkMerchantRemarksVO listRemarksByMerchantIds(List<Long> merchantIds);

    /**
     * 电销线索-根据商户ID获取更多商户评价
     *
     * @param dto
     * @return
     */
    BasePage<RemoteOmkMerchantRemarkVO> pageRemarks(RemoteOmkMerchantRemarkPageDTO dto);

    /**
     * 电销线索-添加商户信息备注/评价
     *
     * @param dto
     * @return
     */
    boolean addRemark(RemoteOmkMerchantRemarkAddDTO dto);

    /**
     * 电销线索状态统计
     *
     * @param dto
     * @return
     */
    List<RemoteOmkClueStatusCntVO> countClueStatus(RemoteOmkCluePageDTO dto);

    List<RemoteOmkClueStatusCntVO> countClueStatusAll(RemoteOmkClueStatusCntDTO dto);

    /**
     * 查询电销客户来源映射关系(key:电话号码,value:线索渠道)
     */
    Map<String, GeneralizedCustomerSourceVO> getOnlineSalesCustomerSourceMap(Set<String> phoneSet);

    /**
     * 首页线索&工单数据统计
     * @param onlineSalesBdId
     * @return
     */
    HeraClueStatisticsVO homePageClueStatistics(Long merchantId,Long onlineSalesBdId);

    /**
     * 根据线索id 查询 omk信息
     * @param businessId
     * @return id 无效或者不存在默认对象
     */
    OmkInfo selectOmkInfo(Long businessId);
    /**
     * 根据线索工单查询对应的电销线索的信息
     */
    List<BusinessInfo> listClueByTicketIds(Set<Long> clueTicketIds);

    /**
     * @implNote 更新电销订单渠道信息
     */
    void updateOmkOrderChannelInfo();
}
