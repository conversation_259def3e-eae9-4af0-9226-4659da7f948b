package com.yaowu.hera.domain.leads.biz.impl;

import cn.hutool.core.lang.Pair;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.freedom.feign.utils.FeignInvokeUtils;
import com.freedom.redis.utils.RedisLockUtil;
import com.freedom.toolscommon.utils.StreamTools;
import com.freedom.web.model.resp.BaseResult;
import com.google.common.collect.Sets;
import com.yaowu.hera.domain.leads.biz.ILeadsDeviceBizService;
import com.yaowu.hera.domain.leads.service.batis.service.ILeadsDeviceAssociationWordService;
import com.yaowu.hera.domain.leads.service.batis.service.ILeadsDeviceCategoryLogService;
import com.yaowu.hera.domain.leads.service.batis.service.ILeadsDeviceCategoryService;
import com.yaowu.hera.domain.leads.service.batis.service.ILeadsDeviceClassificationService;
import com.yaowu.hera.domain.mtl.leadsdevice.biz.ISpuCollectionRelationBizService;
import com.yaowu.hera.domain.mtl.leadsdevice.service.batis.service.ISpuCollectionRelationService;
import com.yaowu.hera.domain.tag.biz.ITagBizService;
import com.yaowu.hera.enums.mtl.CategoryOperTypeEnum;
import com.yaowu.hera.enums.mtl.LeadsDeviceShowTypeEnum;
import com.yaowu.hera.model.bo.leads.LeadsDeviceConditionBO;
import com.yaowu.hera.model.dto.mtl.leadsdevice.SpuCollectionRelationQueryDTO;
import com.yaowu.hera.model.entity.leads.LeadsDeviceAssociationWord;
import com.yaowu.hera.model.entity.leads.LeadsDeviceCategory;
import com.yaowu.hera.model.entity.leads.LeadsDeviceCategoryLog;
import com.yaowu.hera.model.entity.leads.LeadsDeviceClassification;
import com.yaowu.hera.model.entity.mtl.leadsdevice.SpuCollectionRelation;
import com.yaowu.hera.model.pojo.mtl.LeadsDeviceAssociationWordQueryModel;
import com.yaowu.hera.remote.kratos.IKratosFeignService;
import com.yaowu.hera.utils.BizException;
import com.yaowu.hera.utils.ErrorCode;
import com.yaowu.hera.utils.mapstruct.mtl.LeadsCategoryAssociationWordMapStruct;
import com.yaowu.hera.utils.mapstruct.mtl.LeadsCategoryMapStruct;
import com.yaowu.hera.utils.mapstruct.mtl.LeadsDeviceClassificationMapStruct;
import com.yaowu.heraapi.enums.device.ChannelTypeEnum;
import com.yaowu.heraapi.enums.mtl.UrlConfigTypeEnum;
import com.yaowu.heraapi.model.dto.mtl.*;
import com.yaowu.heraapi.model.pojo.business.UrlConfig;
import com.yaowu.heraapi.model.vo.common.tag.RemoteTagVO;
import com.yaowu.heraapi.model.vo.mtl.RemoteNoneMtlCategoryVO;
import com.yaowu.heraapi.model.vo.mtl.RemoteWebCategoryDetailVO;
import com.yaowu.heraapi.model.vo.mtl.RemoteWebCategoryPageVO;
import com.yaowu.kratosapi.model.dto.device.RemoteCategoryConditionDTO;
import com.yaowu.kratosapi.model.vo.device.RemoteDeviceCategoryDetailVO;
import com.yaowu.tagsystemapi.feign.RemoteTagCommonServiceV2Feign;
import com.yaowu.tagsystemapi.model.dto.tag.v2.AddMarkDTO;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.time.LocalDateTime;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

@Slf4j
@Service
public class LeadsDeviceBizServiceImpl implements ILeadsDeviceBizService {

    @Resource
    private ILeadsDeviceCategoryService iLeadsDeviceCategoryService;
    @Resource
    private ILeadsDeviceCategoryLogService iLeadsDeviceCategoryLogService;
    @Resource
    private LeadsCategoryMapStruct leadsCategoryMapStruct;
    @Resource
    private IKratosFeignService iKratosFeignService;
    @Resource
    private ILeadsDeviceAssociationWordService iLeadsDeviceAssociationWordService;
    @Resource
    private ITagBizService iTagBizService;
    @Resource
    private RemoteTagCommonServiceV2Feign remoteTagCommonServiceV2Feign;
    @Resource
    private ILeadsDeviceClassificationService leadsDeviceClassificationService;
    @Resource
    private ILeadsDeviceClassificationService iLeadsDeviceClassificationService;
    @Resource
    private ISpuCollectionRelationBizService spuCollectionRelationBizService;
    @Resource
    private ISpuCollectionRelationService spuCollectionRelationService;

    //是客端的品类打标
    private static final String TAG_TYPE_CATEGORY_CUSTOMER = "CATEGORY_CUSTOMER";

    @Override
    public List<LeadsDeviceCategory> getCategoryByLocationType(int locationType, String channel) {
        LeadsDeviceConditionBO deviceConditionBO = LeadsDeviceConditionBO.builder()
                .locationType(locationType)
                .channel(channel)
                .build();
        return iLeadsDeviceCategoryService.listByCondition(deviceConditionBO);
    }

    @Override
    public LeadsDeviceCategory getCategoryByLocationTypeAndId(int locationType, Long categoryId, String channel) {
        return iLeadsDeviceCategoryService.getCategoryByLocationTypeAndCatId(locationType,categoryId,channel);
    }

    @Override
    public Map<Long,LeadsDeviceCategory> getCategoryByLocationTypeAndIds(int locationType, Set<Long> categoryIds, String channel) {
        LeadsDeviceConditionBO deviceConditionBO = LeadsDeviceConditionBO.builder()
                .locationType(locationType)
                .channel(channel)
                .categoryIds(categoryIds)
                .build();
        List<LeadsDeviceCategory> leadsDeviceCategories = iLeadsDeviceCategoryService.listByCondition(deviceConditionBO);
        if(CollectionUtils.isEmpty(leadsDeviceCategories)){
            return new HashMap<>();
        }
        return StreamTools.toMap(leadsDeviceCategories,LeadsDeviceCategory::getCategoryId, Function.identity());
    }

    @Override
    public List<LeadsDeviceCategory> getAllCategory() {
        return iLeadsDeviceCategoryService.listByCondition(LeadsDeviceConditionBO.builder().showType(LeadsDeviceShowTypeEnum.ALL_PAGE).build());
    }

    @Override
    public List<RemoteWebCategoryPageVO> getWebCategoryList(RemoteWebCategoryDTO dto) {
        //获取所有的品类
        List<LeadsDeviceCategory> categoryList = iLeadsDeviceCategoryService.list(buildMtlCategoryWrapper(dto));
        if(CollectionUtils.isEmpty(categoryList)){
            return new ArrayList<>();
        }
        //重复可以去掉，优先用全部页数据
        removeWebCategoryListData(categoryList);
        //获取品类的原来名称
        Map<Long, RemoteDeviceCategoryDetailVO> categoryMapByIds = Optional.ofNullable(iKratosFeignService
                .getCategoryByIds(StreamTools.toSet(categoryList, LeadsDeviceCategory::getCategoryId))).orElse(new HashMap<>());
        //封VO
        List<RemoteWebCategoryPageVO> voList = categoryList.stream().map(category -> {
            RemoteWebCategoryPageVO remoteWebCategoryPageVO = leadsCategoryMapStruct.leadsDeviceCategoryToRemoteWebCategoryPageVO(category);
            if (remoteWebCategoryPageVO == null) {
                return remoteWebCategoryPageVO;
            }
            remoteWebCategoryPageVO.setPageSort(category.getSort());
            if(remoteWebCategoryPageVO.getPageSort()==null || remoteWebCategoryPageVO.getPageSort().equals(0)){
                remoteWebCategoryPageVO.setPageSort(null);
            }
            Optional.ofNullable(categoryMapByIds.get(remoteWebCategoryPageVO.getCategoryId())).ifPresent(remoteDeviceInstanceBasicVO -> {
                remoteWebCategoryPageVO.setOriginalCategoryName(remoteDeviceInstanceBasicVO.getName());
            });
            return remoteWebCategoryPageVO;
        }).collect(Collectors.toList());

        return voList.stream()
                .sorted(Comparator.comparing(RemoteWebCategoryPageVO::getPageSort, Comparator.nullsLast(Comparator.naturalOrder()))
                        .thenComparing(RemoteWebCategoryPageVO::getUpdateTime, Comparator.reverseOrder()))
                .collect(Collectors.toList());
    }

    /**
     * 优先使用全部页，又保留首页的排序
     * @param categoryList
     */
    private void removeWebCategoryListData(List<LeadsDeviceCategory> categoryList) {
        if(CollectionUtils.isEmpty(categoryList)){
            return;
        }

        Map<String, List<LeadsDeviceCategory>> groupByChannelAndCatId = StreamTools.group(categoryList, cat -> getKey(cat));
        groupByChannelAndCatId.forEach((channelAndCatId,list)->{
            if(CollectionUtils.isEmpty(list)){
                return;
            }

            LeadsDeviceCategory homeCategory = list.stream().filter(cat -> cat.getShowType().equals(LeadsDeviceShowTypeEnum.HOME_PAGE.getCode())).findFirst().orElse(new LeadsDeviceCategory());
            Integer pageSort = homeCategory.getSort();
            //只有一个，并且不是首页的
            if(list.size()==1 && pageSort==null){
                LeadsDeviceCategory leadsDeviceCategory = list.get(0);
                //直接空
                leadsDeviceCategory.setSort(pageSort);
                return;
            }

            if(list.size()>1){
                //移除首页的
                list.removeIf(cat->cat.getShowType().equals(LeadsDeviceShowTypeEnum.HOME_PAGE.getCode()));
                for (LeadsDeviceCategory leadsDeviceCategory : list) {
                    leadsDeviceCategory.setSort(pageSort);
                }
            }
        });
        System.out.println(groupByChannelAndCatId);
        categoryList.clear();
        categoryList.addAll(groupByChannelAndCatId.values().stream().flatMap(Collection::stream).collect(Collectors.toList()));
    }

    String getKey(LeadsDeviceCategory obj){
        return obj.getChannelType()+"_"+obj.getCategoryId();
    }

    private UrlConfig getUrlConfigByCode(List<UrlConfig> urls,UrlConfigTypeEnum typeEnum){
        return urls.stream().filter(urlConfig -> urlConfig.getType() == typeEnum.getCode()).findFirst().orElse(new UrlConfig());
    }

    private Wrapper<LeadsDeviceCategory> buildMtlCategoryWrapper(RemoteWebCategoryDTO dto) {
        LambdaQueryWrapper<LeadsDeviceCategory> wrapper = Wrappers.lambdaQuery(LeadsDeviceCategory.class);
        //品类id
        wrapper.eq(dto.getCategoryId()!=null,LeadsDeviceCategory::getCategoryId,dto.getCategoryId());
        //平台
        if(StringUtils.hasText(dto.getChannelType())){
            wrapper.eq(LeadsDeviceCategory::getChannelType,dto.getChannelType());
        }else{
            Set<String> channelTypes = Sets.newHashSet(ChannelTypeEnum.NEW_CUSTOMER_APPLETS.getMsg(), ChannelTypeEnum.ALIPAY_APPLETS.getMsg(), ChannelTypeEnum.DOU_YIN_APPLETS.getMsg(), ChannelTypeEnum.KWAI_APPLETS.getMsg());
            wrapper.in(LeadsDeviceCategory::getChannelType,channelTypes);
        }
        //只需要这些类型
        wrapper.in(LeadsDeviceCategory::getShowType, Sets.newHashSet(LeadsDeviceShowTypeEnum.HOME_PAGE.getCode()
                ,LeadsDeviceShowTypeEnum.ALL_PAGE.getCode()));
//        wrapper.orderByDesc(LeadsDeviceCategory::getUpdateTime);
        return wrapper;
    }

    private void fillChannelType(LambdaQueryWrapper<LeadsDeviceCategory> w1, Set<String> channelTypes) {
        if(CollectionUtils.isEmpty(channelTypes)){
           return;
        }
        for (String channelType : channelTypes) {
            w1.like(LeadsDeviceCategory::getChannelType, channelType).or();
        }
    }

    @Override
    public RemoteWebCategoryDetailVO getWebCategoryDetail(RemoteWebCategoryDetailDTO dto) {
        List<LeadsDeviceCategory> allCategory = iLeadsDeviceCategoryService.getAllCategoryByCatIdAndChannel(dto.getCategoryId(), dto.getChannelType());
        LeadsDeviceCategory homePageCategory = getXPageCategoryFromAllCategory(allCategory,LeadsDeviceShowTypeEnum.HOME_PAGE);

        LeadsDeviceCategory allPageCategory = allCategory.stream()
                .filter(leadsDeviceCategory -> leadsDeviceCategory.getShowType().equals(LeadsDeviceShowTypeEnum.ALL_PAGE.getCode()))
                .findFirst()
                .orElse(null);
        if(homePageCategory==null && allPageCategory==null){
            return null;
        }
        List<UrlConfig> urls = new ArrayList<>();
        RemoteWebCategoryDetailVO remoteWebCategoryDetailVO = leadsCategoryMapStruct.leadsDeviceCategoryToRemoteWebCategoryDetailVO(allPageCategory!=null?allPageCategory:homePageCategory);
        if(homePageCategory!=null){
            remoteWebCategoryDetailVO.setPageShowFlag(true);
            remoteWebCategoryDetailVO.setPageSort(homePageCategory.getSort());
            remoteWebCategoryDetailVO.setHotDeviceFlag(homePageCategory.isHotDeviceFlag());
            urls.addAll(getHomePageCategoryUrl(homePageCategory.getUrls()));
        }
        if(allPageCategory!=null){
            remoteWebCategoryDetailVO.setAllPageSort(allPageCategory.getSort());
            remoteWebCategoryDetailVO.setAllPageShowFlag(allPageCategory!=null);
            urls.addAll(getAllPageCategoryUrl(allPageCategory.getUrls()));
        }
        remoteWebCategoryDetailVO.setUrls(urls);
        List<LeadsDeviceClassification> listByCategoryIdAndChannelType = leadsDeviceClassificationService.getListByCategoryIdAndChannelType(dto.getCategoryId(), dto.getChannelType());
        List<IdAndNameModel> idAndNameModels = LeadsDeviceClassificationMapStruct.INSTANCE.toIdAndNameModels(listByCategoryIdAndChannelType);
        Map<Long, Pair<Long, String>> extractedRelations = extractDeviceTagSpuCollectionRelations(idAndNameModels);
        for (IdAndNameModel model : idAndNameModels) {
            Long modelId = model.getId();
            Pair<Long, String> relationPair = extractedRelations.get(modelId);
            if (relationPair != null) {
                model.setRelationId(relationPair.getKey());
                model.setRelationName(relationPair.getValue());
            }
        }
        remoteWebCategoryDetailVO.setDeviceTags(idAndNameModels);

        List<LeadsDeviceAssociationWord> search = iLeadsDeviceAssociationWordService.listByBizIdAndChannelType(dto.getCategoryId(), dto.getChannelType());
        remoteWebCategoryDetailVO.setDeviceAssociationWords(LeadsCategoryAssociationWordMapStruct.INSTANCE.toIdAndNameModels(search));
        return remoteWebCategoryDetailVO;
    }

    private Map<Long, Pair<Long, String>> extractDeviceTagSpuCollectionRelations(List<IdAndNameModel> idAndNameModels) {
        Set<Long> deviceTagIds = idAndNameModels.stream()
                .map(IdAndNameModel::getId)
                .filter(Objects::nonNull)
                .collect(Collectors.toSet());
        SpuCollectionRelationQueryDTO queryDTO = new SpuCollectionRelationQueryDTO();
        queryDTO.setDeviceTagIds(deviceTagIds);
        List<SpuCollectionRelation> spuCollectionRelations = spuCollectionRelationService.listByCondition(queryDTO);
        Map<Long, Pair<Long, String>> deviceTagSpuCollectionMap = new HashMap<>();
        for (SpuCollectionRelation spuCollectionRelation : spuCollectionRelations) {
            deviceTagSpuCollectionMap.computeIfAbsent(spuCollectionRelation.getDeviceTagId(), k -> new Pair<>(spuCollectionRelation.getSpuCollectionId(), spuCollectionRelation.getSpuCollectionName()));
        }
        return deviceTagSpuCollectionMap;
    }

    private LeadsDeviceCategory getXPageCategoryFromAllCategory(List<LeadsDeviceCategory> allCategory, LeadsDeviceShowTypeEnum leadsDeviceShowTypeEnum) {
        if(CollectionUtils.isEmpty(allCategory)){
            return null;
        }
        return allCategory.stream()
                .filter(leadsDeviceCategory -> leadsDeviceCategory.getShowType().equals(leadsDeviceShowTypeEnum.getCode()))
                .findFirst()
                .orElse(null);
    }

    private List<UrlConfig> getAllPageCategoryUrl(List<UrlConfig> urls) {
        if(CollectionUtils.isEmpty(urls)){
            return new ArrayList<>();
        }
        urls.removeIf(urlConfig -> UrlConfigTypeEnum.HOME_PAGE.getCode().equals(urlConfig.getType()));
        return urls;
    }

    private List<UrlConfig> getHomePageCategoryUrl(List<UrlConfig> urls) {
        if(CollectionUtils.isEmpty(urls)){
            return new ArrayList<>();
        }
        urls.removeIf(urlConfig -> !UrlConfigTypeEnum.HOME_PAGE.getCode().equals(urlConfig.getType()));
        List<UrlConfig> returnUrls = new ArrayList<>();
        if(!CollectionUtils.isEmpty(urls)) {
            returnUrls.add(urls.get(0));
        }
        return returnUrls;
    }

    @Transactional
    @Override
    public Boolean updateWebCategory(RemoteUpdateWebCategoryDTO dto) {
        return RedisLockUtil.lock(getWebCategoryLock(dto.getChannelType(),dto.getCategoryId()), 30, () -> {
            //获取数据，判断是否存在
            LeadsDeviceCategory deviceCategory = iLeadsDeviceCategoryService.getAllOrHomePageCategoryByCatIdAndChannel(dto.getCategoryId(), dto.getChannelType());
            BizException.condition(deviceCategory==null, ErrorCode.CATEGORY_ERROR_NOT_EXIST);
            //先首页
            updateHomePageCategory(dto);
            //处理全部页
            updateAllPageCategory(dto,deviceCategory);
            return true;
        });
    }

    public boolean updateAllPageCategory(RemoteUpdateWebCategoryDTO dto, LeadsDeviceCategory deviceCategory) {
        if (dto.isAllPageShowFlag()) {
            updateDeviceTag(dto);
            updateWithCheckRepeat(dto);
            //查一下是否存在
            LeadsDeviceCategory allPageCategory = getAllPageCategoryByCondition(dto);
            if (allPageCategory == null) {
                LeadsDeviceCategory allPageLeadsDeviceCategory = leadsCategoryMapStruct.toLeadsDeviceCategory(dto);
                List<UrlConfig> urls = Optional.ofNullable(allPageLeadsDeviceCategory.getUrls()).orElse(new ArrayList<>());
                urls.removeIf(urlConfig -> UrlConfigTypeEnum.HOME_PAGE.getCode().equals(urlConfig.getType()));
                allPageLeadsDeviceCategory.setUrls(urls);
                LeadsDeviceCategoryLog leadsDeviceCategoryLog = getAddLeadsDeviceCategoryLog(dto, allPageLeadsDeviceCategory);
                iLeadsDeviceCategoryLogService.save(leadsDeviceCategoryLog);
                iLeadsDeviceCategoryService.save(allPageLeadsDeviceCategory);
                return allPageLeadsDeviceCategory.getId() != null ? true : false;
            }
            LeadsDeviceCategoryLog leadsDeviceCategoryLog = new LeadsDeviceCategoryLog();
            leadsDeviceCategoryLog.setBizId(deviceCategory.getCategoryId());
            leadsDeviceCategoryLog.setBeforeModification(JSONUtil.toJsonStr(deviceCategory));
            leadsCategoryMapStruct.updateLeadsDeviceCategory(dto, deviceCategory);
            leadsDeviceCategoryLog.setAfterModification(JSONUtil.toJsonStr(deviceCategory));
            leadsDeviceCategoryLog.setOperType(CategoryOperTypeEnum.EDIT.getCode());
            leadsDeviceCategoryLog.setOperTime(LocalDateTime.now());
            leadsDeviceCategoryLog.setUserId(dto.getUserId());
            leadsDeviceCategoryLog.setUserName(dto.getUserName());
            iLeadsDeviceCategoryLogService.save(leadsDeviceCategoryLog);
            return iLeadsDeviceCategoryService.updateById(deviceCategory);
        } else {
            return removeCategoryDataWhenUpdateAllPageCategory(dto);
        }
    }

    private LeadsDeviceCategory getAllPageCategoryByCondition(RemoteUpdateWebCategoryDTO dto) {
        LeadsDeviceConditionBO build = LeadsDeviceConditionBO.builder()
                .categoryId(dto.getCategoryId())
                .channel(dto.getChannelType())
                .showType(LeadsDeviceShowTypeEnum.ALL_PAGE)
                .build();
        LeadsDeviceCategory allPageCategory = iLeadsDeviceCategoryService.getOneByCondition(build);
        return allPageCategory;
    }

    public boolean removeCategoryDataWhenUpdateAllPageCategory(RemoteUpdateWebCategoryDTO dto) {
        //删数据
//            List<LeadsDeviceCategoryLog> deviceCategoryLogs = buildDelCategoryLog(leadsDeviceCategories,dto);
//            iLeadsDeviceCategoryLogService.saveBatch(deviceCategoryLogs);
        //删除关联词
        iLeadsDeviceAssociationWordService.deleteByBizIdAndChannelType(dto.getCategoryId(),dto.getChannelType());
        //删除标签
        iLeadsDeviceClassificationService.deleteByBizIdAndChannelType(dto.getCategoryId(),dto.getChannelType());
        LeadsDeviceConditionBO build = LeadsDeviceConditionBO.builder()
                .channel(dto.getChannelType())
                .categoryId(dto.getCategoryId())
                .showType(LeadsDeviceShowTypeEnum.ALL_PAGE).build();
        List<LeadsDeviceCategory> leadsDeviceCategories = iLeadsDeviceCategoryService.listByCondition(build);
        if(CollectionUtils.isEmpty(leadsDeviceCategories)){
            return true;
        }
        return iLeadsDeviceCategoryService.removeByIds(StreamTools.toSet(leadsDeviceCategories,LeadsDeviceCategory::getId));
    }

    private List<LeadsDeviceCategoryLog> buildDelCategoryLog(List<LeadsDeviceCategory> leadsDeviceCategories, RemoteDeleteWebCategoryDTO dto) {
        List<LeadsDeviceCategoryLog> deviceCategoryLogs = leadsDeviceCategories.stream().map(deviceCategory -> {
            LeadsDeviceCategoryLog leadsDeviceCategoryLog = new LeadsDeviceCategoryLog();
            leadsDeviceCategoryLog.setBizId(deviceCategory.getCategoryId());
            leadsDeviceCategoryLog.setBeforeModification(JSONUtil.toJsonStr(deviceCategory));
            leadsDeviceCategoryLog.setAfterModification(JSONUtil.toJsonStr(deviceCategory));
            leadsDeviceCategoryLog.setOperType(CategoryOperTypeEnum.DEL.getCode());
            leadsDeviceCategoryLog.setOperTime(LocalDateTime.now());
            leadsDeviceCategoryLog.setUserId(dto.getUserId());
            leadsDeviceCategoryLog.setUserName(dto.getUserName());
            return leadsDeviceCategoryLog;
        }).collect(Collectors.toList());
        return deviceCategoryLogs;
    }

    private void updateDeviceTag(RemoteUpdateWebCategoryDTO dto) {
        List<LeadsDeviceClassification> deviceClassifications = leadsDeviceClassificationService.getListByCategoryIdAndChannelType(dto.getCategoryId(),dto.getChannelType());

        List<IdAndNameModel> deviceTags = dto.getDeviceTags();
        if(CollectionUtils.isEmpty(deviceTags) && CollectionUtils.isEmpty(deviceClassifications)){
            //啥都不用干
        }else if(CollectionUtils.isEmpty(deviceTags) && !CollectionUtils.isEmpty(deviceClassifications)){
            //全删
            leadsDeviceClassificationService.removeBatchByIds(StreamTools.toSet(deviceClassifications,LeadsDeviceClassification::getId));
        }else if(!CollectionUtils.isEmpty(deviceTags) && CollectionUtils.isEmpty(deviceClassifications)){
            //直接加
            saveDeviceTag(dto);
        }else if(!CollectionUtils.isEmpty(deviceTags) && !CollectionUtils.isEmpty(deviceClassifications)){
            // 创建一个 Set 来存储 uiList 中的所有 name
            Set<String> uiNames = dto.getDeviceTags().stream().map(IdAndNameModel::getName).collect(Collectors.toSet());
            // 删除的数据（数据库存在，界面不存在的）
            List<LeadsDeviceClassification> toDelList = deviceClassifications.stream()
                    .filter(item -> !uiNames.contains(item.getClassifiName())).collect(Collectors.toList());
            if(!CollectionUtils.isEmpty(toDelList)){
                leadsDeviceClassificationService.removeByIds(StreamTools.toSet(toDelList,LeadsDeviceClassification::getId));
            }
            Set<String> existClassificationName = StreamTools.toSet(deviceClassifications,LeadsDeviceClassification::getClassifiName);
            // 过滤已经存在数据库的数据，剩下的都是新增的
            dto.getDeviceTags().removeIf(tag->existClassificationName.contains(tag.getName()));
            if(!CollectionUtils.isEmpty(dto.getDeviceTags())){
                //保存进去
                saveDeviceTag(dto);
            }
        }
    }

    private void updateWithCheckRepeat(RemoteUpdateWebCategoryDTO dto) {
        List<LeadsDeviceAssociationWord> list = iLeadsDeviceAssociationWordService.listByBizIdAndChannelType(dto.getCategoryId(),dto.getChannelType());
        Set<String> existAssociationWords = StreamTools.toSet(list, LeadsDeviceAssociationWord::getAssociationWord);
        if(!CollectionUtils.isEmpty(existAssociationWords) && CollectionUtils.isEmpty(dto.getDeviceAssociationWords())){
            //数据库有 &界面没有 = 直接删
            iLeadsDeviceAssociationWordService.removeBatchByIds(StreamTools.toSet(list, LeadsDeviceAssociationWord::getId));
        }else if(CollectionUtils.isEmpty(existAssociationWords) && !CollectionUtils.isEmpty(dto.getDeviceAssociationWords())){
            //数据库没有 & 界面有 = 直接插入
            List<LeadsDeviceAssociationWord> saveList = buildLeadsDeviceAssociationWordList(dto.getDeviceAssociationWords(),dto.getCategoryId(),dto.getCategoryName(),dto.getChannelType());
            iLeadsDeviceAssociationWordService.saveBatch(saveList);
        }else if(!CollectionUtils.isEmpty(existAssociationWords) && !CollectionUtils.isEmpty(dto.getDeviceAssociationWords())){
            // 创建一个 Set 来存储 uiList 中的所有 name
            Set<String> uiNames = dto.getDeviceAssociationWords().stream().map(IdAndNameModel::getName).collect(Collectors.toSet());
            // 删除的数据（数据库存在，界面不存在的）
            List<LeadsDeviceAssociationWord> toDelList = list.stream()
                    .filter(item -> !uiNames.contains(item.getAssociationWord())).collect(Collectors.toList());
            if(!CollectionUtils.isEmpty(toDelList)){
                iLeadsDeviceAssociationWordService.removeByIds(StreamTools.toSet(toDelList,LeadsDeviceAssociationWord::getId));
            }
            // 过滤已经存在数据库的数据，剩下的都是新增的
            dto.getDeviceAssociationWords().removeIf(word->existAssociationWords.contains(word.getName()));
            if(!CollectionUtils.isEmpty(dto.getDeviceAssociationWords())){
                //保存进去
                List<LeadsDeviceAssociationWord> saveList = buildLeadsDeviceAssociationWordList(dto.getDeviceAssociationWords(),
                        dto.getCategoryId(),dto.getCategoryName(),dto.getChannelType());
                iLeadsDeviceAssociationWordService.saveBatch(saveList);
            }
        }
    }


    @Override
    public List<RemoteNoneMtlCategoryVO> getWebNoneMtlCategoryList(RemoteNoneMtlCategoryDTO dto) {
        //获取所有的品类，一二级都要
        RemoteCategoryConditionDTO remoteCategoryConditionDTO = new RemoteCategoryConditionDTO();
        remoteCategoryConditionDTO.setIgnoreBizFlag(true);
        List<RemoteDeviceCategoryDetailVO> baseCategoryList = iKratosFeignService.getCategoryListByCondition(remoteCategoryConditionDTO);
        if(CollectionUtils.isEmpty(baseCategoryList)){
            return new ArrayList<>();
        }

        return filterNoneMtlCategoryList(baseCategoryList,dto);
    }

    @Transactional
    @Override
    public Long addWebCategory(RemoteAddWebCategoryDTO dto) {
        // 加锁（平台+品类id）
        return RedisLockUtil.lock(getWebCategoryLock(dto.getChannelType(), dto.getCategoryId()), 30, () -> {
            //先处理首页
            //addHomePageCategory(dto);
            if (dto.getCategoryParentId() == null || dto.getCategoryParentId().equals(0L)) {
                return 0L;
            }
            //获取数据，优先获取所有页面的
            LeadsDeviceCategory deviceCategory = iLeadsDeviceCategoryService.getAllOrHomePageCategoryByCatIdAndChannel(dto.getCategoryId(), dto.getChannelType());
            BizException.condition(deviceCategory != null, ErrorCode.CATEGORY_ERROR_EXIST);
            //保存设备分类
            saveDeviceTag(dto);
            //保存关联词
            saveWithCheckRepeat(dto);
            LeadsDeviceCategory allPageLeadsDeviceCategory = leadsCategoryMapStruct.toLeadsDeviceCategory(dto);
            List<UrlConfig> urls = Optional.ofNullable(allPageLeadsDeviceCategory.getUrls()).orElse(new ArrayList<>());
            urls.removeIf(urlConfig -> UrlConfigTypeEnum.HOME_PAGE.getCode().equals(urlConfig.getType()));
            allPageLeadsDeviceCategory.setUrls(urls);
            LeadsDeviceCategoryLog leadsDeviceCategoryLog = getAddLeadsDeviceCategoryLog(dto, allPageLeadsDeviceCategory);
            iLeadsDeviceCategoryLogService.save(leadsDeviceCategoryLog);
            iLeadsDeviceCategoryService.save(allPageLeadsDeviceCategory);
            //还要判断一下是否存在父品类
            saveIfParentCatNotExist(dto, LeadsDeviceShowTypeEnum.ALL_PAGE);
            return allPageLeadsDeviceCategory.getId();
        });
    }

    public void saveDeviceTag(RemoteAddWebCategoryDTO dto) {
        List<IdAndNameModel> deviceTags = dto.getDeviceTags();
        if (CollectionUtils.isEmpty(deviceTags)) {
            return;
        }
        //去掉界面名称重复的数据
        deviceTags = removeRepeatName(deviceTags);
        //客端的所有标签
        getExistTagAndFillTagId(deviceTags);
        //判断是否已经都存在，都存在就不用管理了
        Set<Long> existTagIds = deviceTags.stream().filter(deviceTag -> deviceTag.getId() != null).map(IdAndNameModel::getId).collect(Collectors.toSet());
        if (!CollectionUtils.isEmpty(existTagIds)) {
            List<LeadsDeviceClassification> deviceClassifications = leadsDeviceClassificationService.getListByCategoryIdAndChannelType(dto.getCategoryId(), dto.getChannelType(), existTagIds);
            Set<Long> thisCatTagIds = StreamTools.toSet(deviceClassifications, LeadsDeviceClassification::getClassifiId);
            deviceTags.removeIf(deviceTag -> deviceTag.getId() != null && thisCatTagIds.contains(deviceTag.getId()));
        }
        if (CollectionUtils.isEmpty(deviceTags)) {
            return;
        }
        // todo 注意:这里直接全部拿回来，数据暂时不多，后续多了再新加api(目前没有)
        List<RemoteTagVO> tags = iTagBizService.getTags(dto.getCategoryId() + "", TAG_TYPE_CATEGORY_CUSTOMER);
        batchAddMark(tags, deviceTags, dto);

        buildAndSaveDeviceClassification(deviceTags, dto);

        spuCollectionRelationBizService.batchUpdateDeviceTagSpuMapping(deviceTags);
    }

    private void batchAddMark(List<RemoteTagVO> tags, List<IdAndNameModel> deviceTags, RemoteAddWebCategoryDTO dto) {
        if(CollectionUtils.isEmpty(tags)){
            //todo 批量打标，需改造
            for (IdAndNameModel deviceTag : deviceTags) {
                AddMarkDTO addMarkDTO = buildAddMarkDTO(dto,deviceTag);
                BaseResult<Long> baseResult = remoteTagCommonServiceV2Feign.addMarkTag(addMarkDTO);
                Long tagId = FeignInvokeUtils.convert(baseResult, Long.class);
                deviceTag.setId(tagId);
            }
        }else{
            Map<String, RemoteTagVO> tmpTagVOMap = StreamTools.toMap(tags, RemoteTagVO::getTagName, Function.identity());
            deviceTags.stream().forEach(deviceTag->{
                if(tmpTagVOMap.containsKey(deviceTag.getName())){
                    RemoteTagVO remoteTagVO = tmpTagVOMap.get(deviceTag.getName());
                    deviceTag.setId(remoteTagVO.getTagId());
                }else {
                    AddMarkDTO addMarkDTO = buildAddMarkDTO(dto,deviceTag);
                    BaseResult<Long> baseResult = remoteTagCommonServiceV2Feign.addMarkTag(addMarkDTO);
                    Long tagId = FeignInvokeUtils.convert(baseResult, Long.class);
                    deviceTag.setId(tagId);
                }
            });
        }
    }

    public void buildAndSaveDeviceClassification(List<IdAndNameModel> deviceTags, RemoteAddWebCategoryDTO dto) {
        //然后保存到关联表中
        List<LeadsDeviceClassification> collect = deviceTags.stream().map(deviceTag -> {
            LeadsDeviceClassification leadsDeviceClassification = new LeadsDeviceClassification();
            leadsDeviceClassification.setCategoryId(dto.getCategoryId());
            leadsDeviceClassification.setChannelType(dto.getChannelType());
            leadsDeviceClassification.setClassifiId(deviceTag.getId());
            leadsDeviceClassification.setClassifiName(deviceTag.getName());
            leadsDeviceClassification.setSort(deviceTag.getSort());
            return leadsDeviceClassification;
        }).collect(Collectors.toList());
        leadsDeviceClassificationService.saveBatch(collect);
    }

    private AddMarkDTO buildAddMarkDTO(RemoteAddWebCategoryDTO dto, IdAndNameModel deviceTag) {
        AddMarkDTO addMarkDTO = new AddMarkDTO();
        addMarkDTO.setObject(TAG_TYPE_CATEGORY_CUSTOMER);
        addMarkDTO.setCategory(4);
        addMarkDTO.setTagName(deviceTag.getName());
        addMarkDTO.setInstanceId(dto.getCategoryId()+"");
        //有id就是已经有标签了
        addMarkDTO.setTagId(deviceTag.getId());
        addMarkDTO.setReason("内部使用");
        addMarkDTO.setOpUserId(dto.getUserId());
        return addMarkDTO;
    }

    private void getExistTagAndFillTagId(List<IdAndNameModel> deviceTags) {
        List<RemoteTagVO> allCustomerCategoryTags = iTagBizService.getTagsByObject(TAG_TYPE_CATEGORY_CUSTOMER);
        if(CollectionUtils.isEmpty(allCustomerCategoryTags)){
            return;
        }
        Map<String, RemoteTagVO> tagVOMap = StreamTools.toMap(allCustomerCategoryTags, RemoteTagVO::getTagName, Function.identity());
        for (IdAndNameModel deviceTag : deviceTags) {
            RemoteTagVO remoteTagVO = tagVOMap.get(deviceTag.getName());
            if(remoteTagVO==null){
                deviceTag.setId(null);
                continue;
            }
            deviceTag.setId(remoteTagVO.getTagId());
        }
    }

    private List<IdAndNameModel> removeRepeatName(List<IdAndNameModel> deviceTags) {
        //todo 名称相同就去重
        return deviceTags.stream()
                .collect(Collectors.collectingAndThen(
                        Collectors.toMap(
                                IdAndNameModel::getName,  // Key: name
                                item -> item,             // Value: IdAndNameModel
                                (existing, replacement) -> existing,  // 保留第一个出现的元素
                                LinkedHashMap::new         // 使用 LinkedHashMap 保持插入顺序
                        ),
                        map -> new ArrayList<>(map.values())  // 将 Map 转换为 List
                ));
    }

    public void saveWithCheckRepeat(RemoteAddWebCategoryDTO dto) {
        List<IdAndNameModel> deviceAssociationWords = dto.getDeviceAssociationWords();
        if (CollectionUtils.isEmpty(deviceAssociationWords)) {
            return;
        }
        Long categoryId = dto.getCategoryId();
        String channelType = dto.getChannelType();
        //根据这些字段查询一下,没加锁
        LeadsDeviceAssociationWordQueryModel build = LeadsDeviceAssociationWordQueryModel.builder()
                .categoryId(categoryId).channelType(channelType).associationWords(StreamTools.toSet(deviceAssociationWords, IdAndNameModel::getName)).build();
        List<LeadsDeviceAssociationWord> list = iLeadsDeviceAssociationWordService.listByCondition(build);
        Set<String> existAssociationWords = StreamTools.toSet(list, LeadsDeviceAssociationWord::getAssociationWord);

        deviceAssociationWords.removeIf(deviceAssociationWord -> existAssociationWords.contains(deviceAssociationWord.getName()));
        if (CollectionUtils.isEmpty(deviceAssociationWords)) {
            return;
        }
        List<LeadsDeviceAssociationWord> saveList = buildLeadsDeviceAssociationWordList(deviceAssociationWords, categoryId, dto.getCategoryName(), channelType);
        iLeadsDeviceAssociationWordService.saveBatch(saveList);
    }

    private List<LeadsDeviceAssociationWord> buildLeadsDeviceAssociationWordList(List<IdAndNameModel> deviceAssociationWords,
                                                                                 Long categoryId, String categoryName, String channelType) {
        List<LeadsDeviceAssociationWord> saveList = new ArrayList<>();
        for (IdAndNameModel deviceAssociationWord : deviceAssociationWords) {
            LeadsDeviceAssociationWord leadsDeviceAssociationWord = new LeadsDeviceAssociationWord();
            leadsDeviceAssociationWord.setBizId(categoryId);
            leadsDeviceAssociationWord.setBizName(categoryName);
            leadsDeviceAssociationWord.setBizType(2);
            leadsDeviceAssociationWord.setChannelType(channelType);
            leadsDeviceAssociationWord.setAssociationWord(deviceAssociationWord.getName());
            saveList.add(leadsDeviceAssociationWord);
        }
        return saveList;
    }


    private String getWebCategoryLock(String channelType, Long categoryId) {
        return "WEB_CATEGORY_LOCK:"+channelType+":"+categoryId;
    }

    @Transactional
    public void addHomePageCategory(RemoteAddWebCategoryDTO dto) {
        if (!dto.isPageShowFlag()) {
            return;
        }
        //todo 加锁：渠道+首页+品类id
        LeadsDeviceConditionBO build = LeadsDeviceConditionBO.builder()
                .categoryId(dto.getCategoryId())
                .channel(dto.getChannelType())
                .showType(LeadsDeviceShowTypeEnum.HOME_PAGE)
                .build();
        List<LeadsDeviceCategory> leadsDeviceCategories = iLeadsDeviceCategoryService.listByCondition(build);
        if(!CollectionUtils.isEmpty(leadsDeviceCategories)){
            //存在了
            return;
        }
        //添加首页的数据
        LeadsDeviceCategory homePageCategory = leadsCategoryMapStruct.toHomeLeadsDeviceCategory(dto);
        List<UrlConfig> urls = Optional.ofNullable(homePageCategory.getUrls()).orElse(new ArrayList<>());
        urls.removeIf(urlConfig -> !UrlConfigTypeEnum.HOME_PAGE.getCode().equals(urlConfig.getType()));
        homePageCategory.setUrls(urls);
        LeadsDeviceCategoryLog leadsDeviceCategoryLog = getAddLeadsDeviceCategoryLog(dto, homePageCategory);
        iLeadsDeviceCategoryLogService.save(leadsDeviceCategoryLog);
        iLeadsDeviceCategoryService.save(homePageCategory);
        //还要判断一下是否存在父品类
        //saveIfParentCatNotExist(dto,LeadsDeviceShowTypeEnum.HOME_PAGE);
    }

    public void saveIfParentCatNotExist(RemoteAddWebCategoryDTO dto,LeadsDeviceShowTypeEnum showTypeEnum) {
        if(dto.getCategoryParentId()==null || dto.getCategoryParentId().equals(0L)){
            return;
        }
        LeadsDeviceConditionBO build = LeadsDeviceConditionBO.builder()
                .categoryId(dto.getCategoryParentId())
                .channel(dto.getChannelType())
                .showType(showTypeEnum)
                .build();
        LeadsDeviceCategory leadsDeviceCategory = iLeadsDeviceCategoryService.getOneByCondition(build);
        if(leadsDeviceCategory!=null){
            return;
        }
        LeadsDeviceCategory parentCat = new LeadsDeviceCategory();
        parentCat.setCategoryId(dto.getCategoryParentId());
        parentCat.setCategoryName(dto.getCategoryParentName());
        parentCat.setShowType(showTypeEnum.getCode());
        parentCat.setChannelType(dto.getChannelType());
        iLeadsDeviceCategoryService.save(parentCat);
    }

    @Transactional
    public void updateHomePageCategory(RemoteUpdateWebCategoryDTO dto) {
        LeadsDeviceConditionBO build = LeadsDeviceConditionBO.builder()
                .categoryId(dto.getCategoryId())
                .channel(dto.getChannelType())
                .showType(LeadsDeviceShowTypeEnum.HOME_PAGE)
                .build();
        LeadsDeviceCategory leadsDeviceCategory = iLeadsDeviceCategoryService.getOneByCondition(build);
        //去掉不展示
        if (!dto.isPageShowFlag() && leadsDeviceCategory!=null) {
            //删数据
            iLeadsDeviceCategoryService.removeById(leadsDeviceCategory.getId());
            LeadsDeviceCategoryLog leadsDeviceCategoryLog = getDelLeadsDeviceCategoryLog(dto,leadsDeviceCategory);
            iLeadsDeviceCategoryLogService.save(leadsDeviceCategoryLog);
            return;
        }
        //todo 加锁：渠道+首页+品类id
        if(leadsDeviceCategory==null){
            //不存在就新增
            addHomePageCategory(dto);
        }else{
            //修改首页的数据
            LeadsDeviceCategory homePageCategory = leadsCategoryMapStruct.updateHomeLeadsDeviceCategory(dto,leadsDeviceCategory);
            LeadsDeviceCategoryLog leadsDeviceCategoryLog = getUpdateLeadsDeviceCategoryLog(dto,leadsDeviceCategory, homePageCategory);
            iLeadsDeviceCategoryLogService.save(leadsDeviceCategoryLog);

            iLeadsDeviceCategoryService.updateById(homePageCategory);
        }
    }

    private Set<String> getThisCategoryExistChannelType(RemoteAddWebCategoryDTO dto, LeadsDeviceShowTypeEnum showType) {
        LeadsDeviceConditionBO deviceConditionBO = LeadsDeviceConditionBO.builder()
                .showType(showType)
                .categoryId(dto.getCategoryId())
//                .channelTypes(dto.getChannelType())
                .build();
        List<LeadsDeviceCategory> leadsCategories = Optional.ofNullable(iLeadsDeviceCategoryService.listByCondition(deviceConditionBO))
                .orElse(new ArrayList<>());
        Set<String> dbChannelTypes = leadsCategories.stream()
                .flatMap(category -> Arrays.stream(category.getChannelType().split(",")))
                .collect(Collectors.toSet());
        log.info("dbChannelTypes:{}",JSONUtil.toJsonStr(dbChannelTypes));
        return dbChannelTypes;
    }

    private LeadsDeviceCategoryLog getAddLeadsDeviceCategoryLog(RemoteAddWebCategoryDTO dto,
                                                                LeadsDeviceCategory homePageCategory) {
        LeadsDeviceCategoryLog leadsDeviceCategoryLog = new LeadsDeviceCategoryLog();
        leadsDeviceCategoryLog.setBizId(dto.getCategoryId());
        leadsDeviceCategoryLog.setAfterModification(JSONUtil.toJsonStr(homePageCategory));
        leadsDeviceCategoryLog.setOperType(CategoryOperTypeEnum.ADD.getCode());
        leadsDeviceCategoryLog.setOperTime(LocalDateTime.now());
        leadsDeviceCategoryLog.setUserId(dto.getUserId());
        leadsDeviceCategoryLog.setUserName(dto.getUserName());
        return leadsDeviceCategoryLog;
    }

    private LeadsDeviceCategoryLog getUpdateLeadsDeviceCategoryLog(RemoteAddWebCategoryDTO dto,
                                                                LeadsDeviceCategory oldHomePageCategory,
                                                                   LeadsDeviceCategory newHomePageCategory) {
        LeadsDeviceCategoryLog leadsDeviceCategoryLog = new LeadsDeviceCategoryLog();
        leadsDeviceCategoryLog.setBizId(dto.getCategoryId());
        leadsDeviceCategoryLog.setBeforeModification(JSONUtil.toJsonStr(oldHomePageCategory));
        leadsDeviceCategoryLog.setAfterModification(JSONUtil.toJsonStr(newHomePageCategory));
        leadsDeviceCategoryLog.setOperType(CategoryOperTypeEnum.EDIT.getCode());
        leadsDeviceCategoryLog.setOperTime(LocalDateTime.now());
        leadsDeviceCategoryLog.setUserId(dto.getUserId());
        leadsDeviceCategoryLog.setUserName(dto.getUserName());
        return leadsDeviceCategoryLog;
    }

    private LeadsDeviceCategoryLog getDelLeadsDeviceCategoryLog(RemoteAddWebCategoryDTO dto,
                                                                   LeadsDeviceCategory oldHomePageCategory) {
        LeadsDeviceCategoryLog leadsDeviceCategoryLog = new LeadsDeviceCategoryLog();
        leadsDeviceCategoryLog.setBizId(dto.getCategoryId());
        leadsDeviceCategoryLog.setBeforeModification(JSONUtil.toJsonStr(oldHomePageCategory));
//        leadsDeviceCategoryLog.setAfterModification(JSONUtil.toJsonStr(newHomePageCategory));
        leadsDeviceCategoryLog.setOperType(CategoryOperTypeEnum.DEL.getCode());
        leadsDeviceCategoryLog.setOperTime(LocalDateTime.now());
        leadsDeviceCategoryLog.setUserId(dto.getUserId());
        leadsDeviceCategoryLog.setUserName(dto.getUserName());
        return leadsDeviceCategoryLog;
    }

    @Override
    public Long copyWebCategory(RemoteCopyWebCategoryDTO dto) {
        LeadsDeviceCategory deviceCategory = iLeadsDeviceCategoryService.getById(dto.getId());
        if(deviceCategory==null){
            log.error("品类不存在{}",dto.getId());
            return 0L;
        }
        //复制不能给渠道
        deviceCategory.setChannelType("");
        LeadsDeviceCategoryLog leadsDeviceCategoryLog = new LeadsDeviceCategoryLog();
        leadsDeviceCategoryLog.setBizId(deviceCategory.getCategoryId());
        leadsDeviceCategoryLog.setAfterModification(JSONUtil.toJsonStr(deviceCategory));
        leadsDeviceCategoryLog.setOperType(CategoryOperTypeEnum.COPY.getCode());
        leadsDeviceCategoryLog.setOperTime(LocalDateTime.now());
        leadsDeviceCategoryLog.setUserId(dto.getUserId());
        leadsDeviceCategoryLog.setUserName(dto.getUserName());
        iLeadsDeviceCategoryLogService.save(leadsDeviceCategoryLog);
        deviceCategory.setId(null);
        iLeadsDeviceCategoryService.save(deviceCategory);
        return deviceCategory.getId();
    }

    private List<RemoteNoneMtlCategoryVO> filterNoneMtlCategoryList(List<RemoteDeviceCategoryDetailVO> baseCategoryList, RemoteNoneMtlCategoryDTO dto) {
        String categoryName = dto.getCategoryName();
        Integer showType = dto.getShowType();
        Set<String> channelTypes = dto.getChannelTypes();

        //通过品类名称进行过滤
        if (StringUtils.hasText(categoryName)) {
            baseCategoryList = baseCategoryList.stream()
                    .filter(category -> StringUtils.hasText(category.getName()) && category.getName().contains(categoryName))
                    .collect(Collectors.toList());
        }

        //根据条件进行过滤MTL
/*        LeadsDeviceConditionBO deviceConditionBO = LeadsDeviceConditionBO.builder()
                .fuzzyCategoryName(categoryName)
                .showType(LeadsDeviceShowTypeEnum.getStatusByCode(showType))
                .channelTypes(channelTypes).build();
        List<LeadsDeviceCategory> mtlDeviceCategories = iLeadsDeviceCategoryService.listByCondition(deviceConditionBO);
        if (CollectionUtils.isEmpty(mtlDeviceCategories)) {
            return baseCategoryList.stream().map(category -> leadsCategoryMapStruct.toRemoteNoneMtlCategoryVO(category)).collect(Collectors.toList());
        }
        Map<Long, LeadsDeviceCategory> mtlCategoryMap = StreamTools.toMap(mtlDeviceCategories, LeadsDeviceCategory::getCategoryId, Function.identity());*/

        return baseCategoryList.stream()
                //.filter(category -> StringUtils.hasText(category.getCatAlias()))
                //.filter(category -> !mtlCategoryMap.containsKey(category.getId()))
                .map(category -> leadsCategoryMapStruct.toRemoteNoneMtlCategoryVO(category))
                .collect(Collectors.toList());
    }
}
