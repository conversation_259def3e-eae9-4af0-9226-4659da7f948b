package com.yaowu.hera.domain.common.biz.impl;

import com.alibaba.hologres.com.google.common.collect.Sets;
import com.freedom.toolscommon.utils.StreamTools;
import com.yaowu.hera.domain.common.biz.INoticeBizService;
import com.yaowu.hera.domain.common.biz.INoticeManualInvokeBizService;
import com.yaowu.hera.domain.feign.PmsFeignBizService;
import com.yaowu.heraapi.model.dto.common.RemoteMerchantAllSettleInAppPushDTO;
import com.yaowu.heraapi.model.dto.common.RemoteMerchantAppPushDTO;
import com.yaowu.notice.constant.enums.ChannelEnum;
import com.yaowu.pmsapi.model.vo.user.UserRelVO;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.util.*;

/**
 * @Author: liuzhenpeng
 * @Date: 2024/11/12 16:55
 */
@Slf4j
@Service
public class NoticeManualInvokeBizServiceImpl implements INoticeManualInvokeBizService {

    @Resource
    private INoticeBizService iNoticeBizService;

    @Resource
    private PmsFeignBizService pmsFeignBizService;

    @Override
    public void merchantAppPushNotice(RemoteMerchantAppPushDTO dto) {
        // 查找商户id下的门店店长
        Set<Long> userIds = getMerchantPhone(dto.getMerchantIds());
        String templateCode = StringUtils.hasText(dto.getTemplateCode()) ? dto.getTemplateCode() : "H_PUSH_CATEGORY_UPDATE_NOTICE";
        Map<String, String> templateParams = new HashMap<>();
        Long effectiveTime = 2592000000L;
        userIds.forEach(userId -> {
            iNoticeBizService.sendPush(ChannelEnum.STORE_FRONT_DESK_APP,
                    templateCode, templateParams, Sets.newHashSet(userId), effectiveTime);
        });
    }

    /**
     * 获取指定门店的商户联系方式
     *
     * @param merchantIds
     * @return
     */
    private Set<Long> getMerchantPhone(Set<Long> merchantIds) {
        List<UserRelVO> userRelVOS = pmsFeignBizService.getUserRelListByMerchantIdsAndRoleCode(merchantIds,"PUBLIC_STORE_MANAGER");
        if(CollectionUtils.isEmpty(userRelVOS)){
            return new HashSet<>();
        }
        return StreamTools.toSet(userRelVOS, UserRelVO::getUserId);
    }
}
