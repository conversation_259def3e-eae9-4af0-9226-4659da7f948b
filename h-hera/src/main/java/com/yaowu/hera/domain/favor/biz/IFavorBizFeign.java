package com.yaowu.hera.domain.favor.biz;

import com.freedom.web.model.resp.BaseResult;
import com.yaowu.heraapi.model.vo.favor.RemoteFavorStockGrantWechatSignVO;
import com.yaowu.passportapi.model.vo.user.UserInfoSensitiveVO;
import com.yaowu.settle.api.model.vo.wechat.RemoteWechatStockDetailVO;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @since 2023/7/29 18:34
 **/
public interface IFavorBizFeign {

    /**
     * 根据手机号码获取customer bizIds，如果不存在会新创建
     *
     * @param phoneNumbers 手机号码集合
     * @return Phone-customer bizIds对应的map
     */
    Map<String, Long> getOrCreateCustomerBizIdsByPhones(List<String> phoneNumbers);

    /**
     * 生成短链
     *
     * @param url 原始链接
     * @return 短链
     */
    String generateShortUrl(String url);

    /**
     * 查询微信优惠券批次信息
     *
     * @param stockId 微信代金券批次号
     * @return 微信优惠券批次信息
     */
    BaseResult<RemoteWechatStockDetailVO> queryWechatStockDetail(String stockId);

    /**
     * 获取微信领券的签名
     *
     * @param stockId   微信代金券批次号
     * @param requestNo 商户单据号，唯一
     * @return 微信领券签名
     */
    RemoteFavorStockGrantWechatSignVO grantRemoteWechatSign(String stockId, String requestNo);

    /**
     * 根据id查询用户的加密信息
     *
     * @param bizUserIds 用户id
     * @return 用户信息
     */
    Map<Long, UserInfoSensitiveVO> querySensitive(List<Long> bizUserIds);

    /**
     * 根据手机号码，查询对应的CustomerBizUserId
     *
     * @param phone 手机号码
     * @return CustomerBizUserId
     */
    Long getCustomerBizUserIdByPhone(String phone);
}
