package com.yaowu.hera.domain.placement.service.batis.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.stream.StreamUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.yaowu.hera.domain.placement.service.batis.mapper.PlacementPageContentMapper;
import com.yaowu.hera.domain.placement.service.batis.service.IPlacementPageContentService;
import com.yaowu.hera.model.entity.placement.PlacementPageContent;
import com.yaowu.heraapi.enums.placement.RemotePageContentTypeEnum;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;


/**
 * <AUTHOR>
 * @email <EMAIL>
 * @creatTime 2024.10.29 19:40:00
 * @description:
 */

@Service
public class PlacementPageContentServiceImpl extends ServiceImpl<PlacementPageContentMapper, PlacementPageContent> implements IPlacementPageContentService {


    @Override
    public Map<Long, List<PlacementPageContent>> getPageId2PageContentMaps(Set<Long> pageIds, RemotePageContentTypeEnum secondCategory) {
        if (CollectionUtil.isEmpty(pageIds) || Objects.isNull(secondCategory)){
            return Collections.emptyMap();
        }
        LambdaQueryWrapper<PlacementPageContent> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.in(PlacementPageContent::getPlacePageId, pageIds);
        queryWrapper.eq(PlacementPageContent::getBizType, secondCategory);
        List<PlacementPageContent> pageContents = list(queryWrapper);
        return StreamUtil.of(pageContents).collect(Collectors.groupingBy(PlacementPageContent::getPlacePageId));
    }

    @Override
    public boolean removeByBizId(Long id) {
        LambdaQueryWrapper<PlacementPageContent> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(PlacementPageContent::getPlacePageId, id);
        return remove(queryWrapper);
    }

    @Override
    public List<PlacementPageContent> listPageContentsByContentId(Long id, RemotePageContentTypeEnum remotePageContentTypeEnum) {
        LambdaQueryWrapper<PlacementPageContent> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(PlacementPageContent::getPlacePageId,id);
        queryWrapper.eq(PlacementPageContent::getBizType,remotePageContentTypeEnum);
        return list(queryWrapper);
    }
}
