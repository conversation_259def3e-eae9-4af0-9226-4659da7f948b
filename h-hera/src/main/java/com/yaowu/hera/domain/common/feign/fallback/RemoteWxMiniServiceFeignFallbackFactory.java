package com.yaowu.hera.domain.common.feign.fallback;

import com.freedom.web.exception.BusinessException;
import com.yaowu.hera.domain.common.feign.IRemoteWxMiniFeign;
import org.springframework.cloud.openfeign.FallbackFactory;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @since 2023/7/29 12:27
 **/
@Component
public class RemoteWxMiniServiceFeignFallbackFactory implements FallbackFactory<IRemoteWxMiniFeign> {

    @Override
    public IRemoteWxMiniFeign create(Throwable cause) {
        throw new BusinessException(cause.getMessage());
    }
}
