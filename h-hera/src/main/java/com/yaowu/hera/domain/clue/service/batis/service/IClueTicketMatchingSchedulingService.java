package com.yaowu.hera.domain.clue.service.batis.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.yaowu.hera.model.entity.ticket.ClueTicketMatchingScheduling;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * <AUTHOR>
 * @description
 * @date 2022:10:28 20:14:27
 */
public interface IClueTicketMatchingSchedulingService extends IService<ClueTicketMatchingScheduling> {

    /**
     * 根据排次code 查询今天所有匹配的 userId
     *
     * @param workDate
     * @return
     */
    List<ClueTicketMatchingScheduling> findAllMatchByShiftCode(LocalDateTime workDate);

    /**
     * 根据时间 删除当月班次
     *
     * @param workDate
     * @return
     */
    boolean removeByWorkDate(Date workDate);


    /**
     * 根据日期查询对应月份的所有记录
     *
     * @param monthOfFirstDay
     * @return
     */
    List<ClueTicketMatchingScheduling> findMonthByDate(Date monthOfFirstDay);

    /**
     * 切换人员在线状态
     *
     * @param userIds
     * @param online
     * @return
     */
    boolean switchOnline(Set<Long> userIds, boolean online);

    /**
     * 根据用户id 查询当前时间的排班信息
     *
     * @param userIds
     * @return
     */
    Map<Long, ClueTicketMatchingScheduling> findDataByUserIdAndCurrentTime(Set<Long> userIds);

    /**
     * 查询最近的排班数据
     *
     * @return
     */
    List<ClueTicketMatchingScheduling> findLatestScheduling();

    /**
     * 获取当前时间对应的班次时间
     *
     * @return
     */
    LocalDateTime chargeWorkDate();

    /**
     *
     * @param stareDate
     * @param endDate
     * @return {@link List}<{@link ClueTicketMatchingScheduling}>
     */
    List<ClueTicketMatchingScheduling> listByDate(LocalDate stareDate, LocalDate endDate);

    /**
     *查询指定日期的排班信息
     * @param specifiedDate
     * @return {@link List}<{@link ClueTicketMatchingScheduling}>
     */
    List<ClueTicketMatchingScheduling> pickUpDate(LocalDate specifiedDate);


}
