package com.yaowu.hera.domain.mtl.leadsevent.biz.event;

import com.yaowu.hera.domain.mtl.leadsevent.biz.AbstractLeadsEvent;
import com.yaowu.hera.enums.mtl.LeadEventDomainTypeEnum;
import com.yaowu.hera.enums.mtl.LeadsEventTypeEnum;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import lombok.experimental.SuperBuilder;

import java.time.Duration;

/**
 * 更新线索已购买事件
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2024/12/13 9:23
 */
@EqualsAndHashCode(callSuper = true)
@Data
@SuperBuilder
@NoArgsConstructor
@AllArgsConstructor
public class UpdateLeadsPurchasedEvent extends AbstractLeadsEvent {
    /**
     * 购买线索的门店id
     */
    @Schema(description = "购买线索的门店id")
    private Long storeId;

    /**
     * 订单id
     */
    @Schema(description = "订单id")
    private Long orderId;

    @Override
    public LeadEventDomainTypeEnum domainType() {
        return LeadEventDomainTypeEnum.LEAD_ORDER;
    }

    @Override
    public LeadsEventTypeEnum eventType() {
        return LeadsEventTypeEnum.UPDATE_LEAD_PURCHASED;
    }

    @Override
    public Duration eventExpire() {
        return Duration.ofMinutes(10);
    }
}
