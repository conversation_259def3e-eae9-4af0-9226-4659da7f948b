package com.yaowu.hera.api.v1.mtl.agent;

import com.freedom.security.resource.config.annotation.PermissionScope;
import com.freedom.security.resource.config.enums.ScopeLevelEnum;
import com.freedom.web.model.resp.BasePage;
import com.freedom.web.model.resp.BaseResult;
import com.yaowu.hera.domain.discount.biz.IDiscountActivityBizService;
import com.yaowu.hera.domain.llm.biz.IAgentConsultBizService;
import com.yaowu.heraapi.feign.mtl.IRemoteActivityCouponFeign;
import com.yaowu.heraapi.feign.mtl.IRemoteAgentConsultRecordFeign;
import com.yaowu.heraapi.model.dto.mtl.*;
import com.yaowu.heraapi.model.vo.mtl.HeraCouponActivityDetailVO;
import com.yaowu.heraapi.model.vo.mtl.HeraCouponActivityPageVO;
import com.yaowu.heraapi.model.vo.mtl.agent.RemoteAddResultVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import jakarta.validation.Valid;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

@RestController
@Api("AgentConsultRecordController")
public class AgentConsultRecordController implements IRemoteAgentConsultRecordFeign {

    @Autowired
    private IAgentConsultBizService agentConsultBizService;


    @PermissionScope(scope = ScopeLevelEnum.SERVICE_INVOKE)
    @Override
    public BaseResult<RemoteAddResultVO> add(@Valid @RequestBody HeraAgentConsultRecordAddDTO dto) {
        return BaseResult.success(agentConsultBizService.add(dto));
    }
}
