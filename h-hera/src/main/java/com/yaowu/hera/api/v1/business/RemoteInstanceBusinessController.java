package com.yaowu.hera.api.v1.business;

import com.freedom.security.resource.config.annotation.PermissionScope;
import com.freedom.security.resource.config.enums.ScopeLevelEnum;
import com.freedom.web.model.resp.BaseResult;
import com.yaowu.hera.domain.business.biz.IInstanceBusinessBizService;
import com.yaowu.heraapi.feign.business.IRemoteInstanceBusinessFeign;
import com.yaowu.heraapi.model.dto.business.HeraInstanceBusinessDTO;
import com.yaowu.heraapi.model.dto.business.HeraInstanceOrderDTO;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR>
 * @description
 * @date 2023:11:21 10:32:34
 */
@RestController
@RequiredArgsConstructor
public class RemoteInstanceBusinessController implements IRemoteInstanceBusinessFeign {
    private final IInstanceBusinessBizService instanceBusinessBizService;


    @Override
    @PermissionScope(scope = ScopeLevelEnum.SERVICE_INVOKE)
    public BaseResult<Long> createBusiness(HeraInstanceBusinessDTO dto) {
        return BaseResult.success(instanceBusinessBizService.createBusiness(dto));
    }

    @Override
    @PermissionScope(scope = ScopeLevelEnum.SERVICE_INVOKE)
    public BaseResult<Long> createOrder( HeraInstanceOrderDTO dto) {
        return BaseResult.success(instanceBusinessBizService.createOrder(dto));
    }
}
