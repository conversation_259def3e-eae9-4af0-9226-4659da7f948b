package com.yaowu.hera.api.v1.adtracking;

import cn.hutool.json.JSONUtil;
import com.freedom.web.model.resp.BaseResult;
import com.yaowu.hera.domain.adtracking.biz.IAdTrackingBizService;
import com.yaowu.hera.domain.agent.biz.IAgentBizService;
import com.yaowu.heraapi.model.dto.adtracking.RemoteKuaishouLeadCallbackDTO;
import com.yaowu.heraapi.model.vo.adtracking.RemoteKuaishouLeadCallbackVO;
import io.swagger.annotations.ApiOperation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

/**
 * @Author: liuzhenpeng
 * @Date: 2024/8/6 20:57
 */
@Slf4j
@RestController
@Tag(name = "广告线索相关接口")
public class RemoteAdLeadCallbackNotifyController {
    @Autowired
    private IAdTrackingBizService iAdTrackingBizService;

    /**
     * 影刀客户消息回调通知
     */
    @ApiOperation(value = "快手线索回调通知")
    @PostMapping("/v1/ad-lead/kuaishou/lead/callback")
    public RemoteKuaishouLeadCallbackVO kuaishouLeadCallback(@Validated @RequestBody RemoteKuaishouLeadCallbackDTO dto) {
        log.info("快手线索回调通知:{}", JSONUtil.toJsonStr(dto));
        iAdTrackingBizService.sumbitKuaishouLead(dto);
        return RemoteKuaishouLeadCallbackVO.success();
    }
}
