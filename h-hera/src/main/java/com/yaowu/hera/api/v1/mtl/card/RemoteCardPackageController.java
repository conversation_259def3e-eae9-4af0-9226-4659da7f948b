
package com.yaowu.hera.api.v1.mtl.card;

import com.freedom.security.resource.config.annotation.PermissionScope;
import com.freedom.security.resource.config.enums.ScopeLevelEnum;
import com.freedom.web.model.resp.BasePage;
import com.freedom.web.model.resp.BaseResult;
import com.yaowu.hera.domain.mtl.card.biz.ICardPackageOrderBizService;
import com.yaowu.heraapi.feign.mtl.IRemoteCardPackageFeign;
import com.yaowu.heraapi.model.dto.mtl.RemoteListCardPackageOrderDTO;
import com.yaowu.heraapi.model.dto.mtl.RemoteCardPackageOrderPaymentDTO;
import com.yaowu.heraapi.model.dto.mtl.RemoteRefundCardDTO;
import com.yaowu.heraapi.model.dto.mtl.card.RemoteCardPackageOrderExportDTO;
import com.yaowu.heraapi.model.dto.mtl.card.RemoteCardPackageOrderPageDTO;
import com.yaowu.heraapi.model.dto.mtl.card.RemoteCardPackageOrderRefundDTO;
import com.yaowu.heraapi.model.vo.mtl.RemoteCardPackageOrderVO;
import com.yaowu.heraapi.model.vo.mtl.card.RemoteCardPackageOrderExportVO;
import com.yaowu.heraapi.model.vo.mtl.card.RemoteCardPackageOrderPageVO;
import com.yaowu.heraapi.model.vo.mtl.card.RemoteHeraSubmitPaymentVO;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;

@Slf4j
@RestController
@RequiredArgsConstructor
@Tag(name = "卡产品包相关接口")
@PermissionScope(scope = ScopeLevelEnum.SERVICE_INVOKE)
public class RemoteCardPackageController implements IRemoteCardPackageFeign {

    @Resource
    private ICardPackageOrderBizService cardPackageOrderBizService;

    @PermissionScope(scope = ScopeLevelEnum.SERVICE_INVOKE)
    @Override
    public BaseResult<RemoteHeraSubmitPaymentVO> cardPackageOrderPayment(RemoteCardPackageOrderPaymentDTO dto) {
        return BaseResult.success(cardPackageOrderBizService.cardPackageOrderPayment(dto));
    }

    @PermissionScope(scope = ScopeLevelEnum.SERVICE_INVOKE)
    @Override
    public BaseResult<Boolean> refundCard(RemoteRefundCardDTO dto) {
        return BaseResult.success(null);
    }

    @Override
    @PermissionScope(scope = ScopeLevelEnum.SERVICE_INVOKE)
    public BaseResult<List<RemoteCardPackageOrderVO>> listCardPackageOrder(RemoteListCardPackageOrderDTO dto) {
        return BaseResult.success(cardPackageOrderBizService.listCardPackageOrder(dto));
    }

    @Override
    @PermissionScope(scope = ScopeLevelEnum.SERVICE_INVOKE)
    public BaseResult<BasePage<RemoteCardPackageOrderPageVO>> pageCardPackageOrder(RemoteCardPackageOrderPageDTO dto) {
        return BaseResult.success(cardPackageOrderBizService.pageCardPackageOrder(dto));
    }

    @Override
    @PermissionScope(scope = ScopeLevelEnum.SERVICE_INVOKE)
    public BaseResult<BasePage<RemoteCardPackageOrderExportVO>> exportCardPackageOrder(RemoteCardPackageOrderExportDTO dto) {
        return BaseResult.success(cardPackageOrderBizService.exportCardPackageOrder(dto));
    }

    @PermissionScope(scope = ScopeLevelEnum.SERVICE_INVOKE)
    @Override
    public BaseResult<Boolean> processCardPackageOrderRefund(RemoteCardPackageOrderRefundDTO dto) {
        return BaseResult.success(cardPackageOrderBizService.processCardPackageOrderRefund(dto));
    }
}
