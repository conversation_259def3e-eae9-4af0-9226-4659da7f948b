package com.yaowu.hera.api.v1.agent;

import com.freedom.security.resource.config.annotation.PermissionScope;
import com.freedom.security.resource.config.enums.ScopeLevelEnum;
import com.freedom.web.model.resp.BaseResult;
import com.yaowu.hera.domain.agent.biz.IAgentBizService;
import com.yaowu.hera.model.dto.agent.*;
import com.yaowu.hera.model.vo.agent.AgentChatMessageVO;
import com.yaowu.hera.model.vo.agent.AgentConversationResponseVO;
import com.yaowu.hera.model.vo.agent.BizLeadsInfoVO;
import com.yaowu.hera.model.vo.agent.ConversationInitResultVO;
import com.yaowu.hera.model.vo.leads.LeadsSubmitVO;
import com.yaowu.hera.utils.mapstruct.agent.AgentMapStruct;
import com.yaowu.hera.utils.webflux.ReactiveResult;
import com.yaowu.heraapi.feign.mtl.IRemoteAgentServiceFeign;
import com.yaowu.heraapi.model.dto.mtl.agent.*;
import com.yaowu.heraapi.model.vo.mtl.RemoteLeadsSubmitVO;
import com.yaowu.heraapi.model.vo.mtl.agent.RemoteAgentChatMessageVO;
import com.yaowu.heraapi.model.vo.mtl.agent.RemoteAgentConversationResponseVO;
import com.yaowu.heraapi.model.vo.mtl.agent.RemoteBizLeadsInfoVO;
import com.yaowu.heraapi.model.vo.mtl.agent.RemoteConversationInitResultVO;
import io.swagger.annotations.ApiOperation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.RestController;
import reactor.core.publisher.Mono;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2024/7/17 16:46
 */
@Slf4j
@RestController
@RequiredArgsConstructor
@Tag(name = "MTL智能体相关接口")
@PermissionScope(scope = ScopeLevelEnum.SERVICE_INVOKE)
public class RemoteAgentController implements IRemoteAgentServiceFeign {
    @Resource
    private IAgentBizService agentBizService;

    @Override
    public BaseResult<Boolean> updatePickupAddress(RemoteUpdatePickupAddressDTO dto) {
        UpdatePickupAddressDTO updatePickupAddressDTO = AgentMapStruct.INSTANCE.toUpdatePickupAddressDTO(dto);
        return BaseResult.success(agentBizService.updatePickupAddress(updatePickupAddressDTO));
    }

    @Override
    public BaseResult<RemoteConversationInitResultVO> initConversation(RemoteConversationInitDTO dto) {
        ConversationInitDTO conversationInitDTO = AgentMapStruct.INSTANCE.toConversationInitDTO(dto);
        ConversationInitResultVO conversationInitResultVO = agentBizService.initConversation(conversationInitDTO);
        return BaseResult.success(AgentMapStruct.INSTANCE.toRemoteConversationInitResultVO(conversationInitResultVO));
    }

    @Override
    public Mono<BaseResult<RemoteAgentConversationResponseVO>> conversation(RemoteConversationRequestDTO dto) {
        AgentConversationRequestDTO agentConversationRequestDTO = AgentMapStruct.INSTANCE.toConversationRequestDTO(dto);
        Mono<AgentConversationResponseVO> asyncResponse = agentBizService.chatWithCustomerServiceAgent(agentConversationRequestDTO);
        return ReactiveResult.success(AgentMapStruct.INSTANCE.toRemoteAgentConversationResponseVOMono(asyncResponse));
    }

    @Override
    public BaseResult<List<RemoteAgentChatMessageVO>> listHistoryChatMessages(RemoteAgentHistoryChatMessageQueryDTO queryDTO) {
        AgentHistoryChatMessageQueryDTO historyChatMessageQueryDTO = AgentMapStruct.INSTANCE.toAgentHistoryChatMessageQueryDTO(queryDTO);
        List<AgentChatMessageVO> agentChatMessages = agentBizService.listHistoryChatMessages(historyChatMessageQueryDTO);
        return BaseResult.success(AgentMapStruct.INSTANCE.toRemoteAgentChatMessageVOList(agentChatMessages));
    }

    @Override
    public BaseResult<RemoteBizLeadsInfoVO> getBizLeadsInfoByCondition(RemoteBizLeadsInfoQueryDTO dto) {
        BizLeadsInfoQueryDTO bizLeadsInfoQueryDTO = AgentMapStruct.INSTANCE.toBizLeadsInfoQueryDTO(dto);
        BizLeadsInfoVO bizLeadsInfo = agentBizService.getBizLeadsInfoByCondition(bizLeadsInfoQueryDTO);
        return BaseResult.success(AgentMapStruct.INSTANCE.toBizLeadsInfoVO(bizLeadsInfo));
    }

    @ApiOperation(value = "取消MTL线索")
    @Override
    public BaseResult<Boolean> cancelMtlLeadsInfo(RemoteAgentCancelMtlLeadsInfoDTO remoteDTO) {
        AgentCancelMtlLeadsInfoDTO dto = AgentMapStruct.INSTANCE.toAgentCancelMtlLeadsInfoDTO(remoteDTO);
        return BaseResult.success(agentBizService.cancelMtlLeadsInfo(dto));
    }

    @ApiOperation(value = "提交MTL线索")
    @Override
    public BaseResult<RemoteLeadsSubmitVO> submitMtlLeadsInfo(RemoteAgentSubmitMtlLeadInfoDTO remoteDTO) {
        AgentSubmitMtlLeadInfoDTO dto = AgentMapStruct.INSTANCE.toAgentSubmitMtlLeadInfoDTO(remoteDTO);
        return BaseResult.success(agentBizService.submitMtlLeadsInfo(dto));
    }
}
