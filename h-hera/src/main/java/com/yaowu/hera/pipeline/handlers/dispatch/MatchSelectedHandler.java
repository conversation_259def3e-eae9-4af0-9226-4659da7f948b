package com.yaowu.hera.pipeline.handlers.dispatch;


import com.yaowu.hera.pipeline.ContextHandler;
import com.yaowu.hera.pipeline.context.DispatchPipelineContext;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @description 匹配选定一个调度
 * @date 2022:11:01 18:09:22
 */
@Component
@Deprecated
public class MatchSelectedHandler implements ContextHandler<DispatchPipelineContext> {

    private static final Logger log = LoggerFactory.getLogger(MatchSelectedHandler.class);

    @Override
    public boolean handle(DispatchPipelineContext context) {
        log.info("匹配选定调度 context = {}", context);

        return true;
    }


}
