package com.yaowu.hera.manual;

import cn.hutool.core.collection.CollUtil;
import com.freedom.web.exception.BusinessException;
import com.xxl.job.core.handler.annotation.XxlJob;
import com.yaowu.hera.domain.leads.service.batis.service.ILeadsOrderInfoService;
import com.yaowu.heraapi.enums.mtl.order.OrderContactSceneEnum;
import com.yaowu.hera.model.dto.mtl.LeadsOrderInfoQueryDTO;
import com.yaowu.hera.model.entity.leads.LeadsOrderInfo;
import com.yaowu.hera.utils.fetcher.MyBatisPageFetcher;
import com.yaowu.heraapi.enums.mtl.order.LeadsOrderTransactionSceneEnum;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.skywalking.apm.toolkit.trace.Trace;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2024/12/10 11:16
 */
@Slf4j
@Component
public class LeadsOrderManualFixApi {
    @Resource
    private ILeadsOrderInfoService leadsOrderInfoService;

    @Trace
    @XxlJob("updateHistoryLeadsOrderInfos")
    public void updateHistoryLeadsOrderInfos() {
        LeadsOrderInfoQueryDTO queryDTO = LeadsOrderInfoQueryDTO.builder()
                .transactionScene(LeadsOrderTransactionSceneEnum.MERCHANT_PURCHASE_LEAD.getCode())
                .orderSourceIsZero(true)
                .initiatorIdIsZero(true)
                .build();
        MyBatisPageFetcher<LeadsOrderInfoQueryDTO, LeadsOrderInfo> fetcher = new MyBatisPageFetcher<>(
                leadsOrderInfoService::pageByCondition, queryDTO, 500L);
        while (fetcher.hasNext()) {
            List<LeadsOrderInfo> fetchRecords = fetcher.next();
            if (CollUtil.isEmpty(fetchRecords)) {
                return;
            }
            for (LeadsOrderInfo leadsOrderInfo : fetchRecords) {
                leadsOrderInfo.setOrderSource(OrderContactSceneEnum.MERCHANT_PAY_LEADS.getCode());
                leadsOrderInfo.setOrderSourceDesc(OrderContactSceneEnum.MERCHANT_PAY_LEADS.getDesc());
                leadsOrderInfo.setInitiatorId(leadsOrderInfo.getStoreId());
                leadsOrderInfo.setRecipientId(0L);
                leadsOrderInfo.setPayerId(leadsOrderInfo.getStoreId());
            }
            boolean updated = leadsOrderInfoService.updateBatchById(fetchRecords);
            if(!updated){
                log.error("更新历史线索订单信息失败");
                throw new BusinessException("更新历史线索订单信息失败");
            }
        }
    }

}
