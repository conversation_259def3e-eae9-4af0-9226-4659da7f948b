package com.yaowu.hera.schedule.aicc;

import com.xxl.job.core.context.XxlJobHelper;
import com.xxl.job.core.handler.annotation.XxlJob;
import com.yaowu.hera.domain.aicc.biz.IAiccFulfillmentBizService;
import com.yaowu.hera.domain.aicc.biz.IAiccRepairedBizService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import jakarta.annotation.Resource;
import java.time.Duration;
import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @since 2023/11/29 10:07
 **/
@Component
@Slf4j
public class AicCreateFollowUpOrderXxlJob {

    @Resource
    private IAiccFulfillmentBizService fulfillmentBizService;

    @Resource
    private IAiccRepairedBizService repairedBizService;

    @XxlJob("aicCreateFollowUpOrderXxlJob")
    public void aicCreateFollowUpOrderXxlJob() {
        LocalDateTime startTime = LocalDateTime.now();
        XxlJobHelper.log("【回访工单】定时检查创建回访工单开始:  startTime = {}", startTime);
        log.info("【回访工单】定时检查创建回访工单开始:  startTime = {}", startTime);

        LocalDate checkDate = LocalDate.now().minusDays(1);
        fulfillmentBizService.queryFulfillmentAndCreateOrder(checkDate);
        repairedBizService.queryRepairedAndCreateOrder(checkDate);

        LocalDateTime endTime = LocalDateTime.now();
        Duration duration = Duration.between(startTime, endTime);
        long millis = duration.toMillis();
        XxlJobHelper.log("【回访工单】定时检查创建回访工单结束:  endTime = {}。用时:{}ms", endTime, millis);
        log.info("【回访工单】定时检查创建回访工单结束:  endTime = {}。用时:{}ms", endTime, millis);
    }
}
