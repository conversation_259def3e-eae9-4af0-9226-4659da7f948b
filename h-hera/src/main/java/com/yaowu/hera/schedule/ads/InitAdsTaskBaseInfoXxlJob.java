package com.yaowu.hera.schedule.ads;

import com.xxl.job.core.context.XxlJobHelper;
import com.xxl.job.core.handler.annotation.XxlJob;
import com.yaowu.hera.domain.ads.biz.IAdsTaskBizService;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.time.Duration;
import java.time.LocalDateTime;


/**
 * <AUTHOR>
 */
@Component
@Slf4j
public class InitAdsTaskBaseInfoXxlJob {

    @Resource
    private IAdsTaskBizService adsTaskBizService;

    @XxlJob("initAdsTaskBaseInfoJobHandler")
    public void initAdsTaskBaseInfoJobHandler() {
        LocalDateTime startTime = LocalDateTime.now();
        XxlJobHelper.log("【资源位】初始化投放任务的状态开始:  startTime = {}", startTime);
        log.info("【资源位】初始化投放任务的状态开始:  startTime = {}", startTime);
        adsTaskBizService.initAdsTaskBaseInfo();
        LocalDateTime endTime = LocalDateTime.now();
        Duration duration = Duration.between(startTime, endTime);
        long millis = duration.toMillis();
        XxlJobHelper.log("【资源位】初始化投放任务的状态结束:  endTime = {}。用时:{}ms", endTime, millis);
        log.info("【资源位】初始化投放任务的状态结束:  endTime = {}。用时:{}ms", endTime, millis);
    }
}
