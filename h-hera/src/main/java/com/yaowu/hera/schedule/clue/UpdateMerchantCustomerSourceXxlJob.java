package com.yaowu.hera.schedule.clue;

import com.freedom.feign.utils.FeignInvokeUtils;
import com.freedom.web.model.resp.BasePage;
import com.google.common.collect.Lists;
import com.xxl.job.core.context.XxlJobHelper;
import com.xxl.job.core.handler.annotation.XxlJob;
import com.yaowu.customerserviceapi.enums.customer.CustomerSourceEnum;
import com.yaowu.customerserviceapi.enums.customer.OnlineSalesCustomerSourceEnum;
import com.yaowu.customerserviceapi.feign.customer.IRemoteOnlineSaleCustomerInfoFeign;
import com.yaowu.customerserviceapi.model.dto.customer.RemoteMerchantCustomerPageDTO;
import com.yaowu.customerserviceapi.model.vo.customer.RemoteMerchantCustomerVO;
import com.yaowu.hera.config.nacos.CommonConfig;
import com.yaowu.hera.domain.business.biz.IOmkClueBizService;
import com.yaowu.passportapi.feign.user.IRemoteUserServiceFeign;
import com.yaowu.passportapi.model.dto.user.UserIdsDTO;
import com.yaowu.passportapi.model.vo.user.UserInfoVO;
import jodd.util.StringUtil;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Slf4j
@Component
@AllArgsConstructor
public class UpdateMerchantCustomerSourceXxlJob {

    private final IRemoteOnlineSaleCustomerInfoFeign iRemoteOnlineSaleCustomerInfoFeign;

    private final IRemoteUserServiceFeign remoteUserServiceFeign;

    private final CommonConfig commonConfig;

    private final IOmkClueBizService omkClueBizService;

    /**
     * 更新客户渠道来源
     */
    @XxlJob("updateMerchantCustomerSourceJobHandler")
    public void updateMerchantCustomerSourceJobHandler() {
        XxlJobHelper.log("开始执行”更新商户客户来源渠道信息“");
        long start = System.currentTimeMillis();
        updateOnlineSalesMerchantCustomers();
        long end = System.currentTimeMillis();
        XxlJobHelper.log("执行”更新商户客户来源渠道信息“结束，用时：" + (end - start) + "毫秒");
    }

    private void updateOnlineSalesMerchantCustomers() {
        List<RemoteMerchantCustomerVO> onlineSalesAddMerchantCustomers = new ArrayList<>();
        fillOnlineSalesAddMerchantCustomers(onlineSalesAddMerchantCustomers);
        List<RemoteMerchantCustomerVO> onlineSalesCopyMerchantCustomers = new ArrayList<>();
        fillOnlineSalesCopyMerchantCustomers(onlineSalesCopyMerchantCustomers);
        for (RemoteMerchantCustomerVO onlineSalesAddMerchantCustomer : onlineSalesAddMerchantCustomers) {
            onlineSalesAddMerchantCustomer.setCustomerSource(null);
            String onlineSalesCustomerSource = omkClueBizService.getOnlineSalesCustomerSourceByCustomerId(onlineSalesAddMerchantCustomer.getId());
            OnlineSalesCustomerSourceEnum onlineSalesCustomerSourceEnum =null;
            if(StringUtil.isNotBlank(onlineSalesCustomerSource) && !StringUtil.equals("REPURCHASE",onlineSalesCustomerSource)){
                onlineSalesCustomerSourceEnum = Enum.valueOf(OnlineSalesCustomerSourceEnum.class, onlineSalesCustomerSource);;
            }
            onlineSalesAddMerchantCustomer.setOnlineSalesCustomerSource(Objects.isNull(onlineSalesCustomerSourceEnum) ? OnlineSalesCustomerSourceEnum.OTHERS : onlineSalesCustomerSourceEnum);
        }
        for (RemoteMerchantCustomerVO onlineSalesCopyMerchantCustomer : onlineSalesCopyMerchantCustomers) {
            onlineSalesCopyMerchantCustomer.setCustomerSource(CustomerSourceEnum.PLATFORM_INTRODUCTION);
            onlineSalesCopyMerchantCustomer.setOnlineSalesCustomerSource(null);
        }
        List<RemoteMerchantCustomerVO> remoteMerchantCustomerVOList = new ArrayList<>();
        remoteMerchantCustomerVOList.addAll(onlineSalesAddMerchantCustomers);
        remoteMerchantCustomerVOList.addAll(onlineSalesCopyMerchantCustomers);
        List<List<RemoteMerchantCustomerVO>> partition = Lists.partition(remoteMerchantCustomerVOList, 30);
        for (List<RemoteMerchantCustomerVO> batch : partition) {
            iRemoteOnlineSaleCustomerInfoFeign.updateBatchMerchantCustomer(batch);
        }
    }
    /**
     * 填充线上销售商户客户信息
     *
     * @param onlineSalesCopyMerchantCustomers
     */
    private void fillOnlineSalesCopyMerchantCustomers(List<RemoteMerchantCustomerVO> onlineSalesCopyMerchantCustomers) {
        RemoteMerchantCustomerPageDTO remoteCustomerPageDTO = new RemoteMerchantCustomerPageDTO();
        remoteCustomerPageDTO.setPage(1);
        remoteCustomerPageDTO.setSize(50);
        BasePage<RemoteMerchantCustomerVO> merchantCustomerBasePage = null;
        while (merchantCustomerBasePage == null || !CollectionUtils.isEmpty(merchantCustomerBasePage.getRecords())) {
            merchantCustomerBasePage = FeignInvokeUtils.convertPage(iRemoteOnlineSaleCustomerInfoFeign.pageOnlineSalesCopyMerchantCustomer(remoteCustomerPageDTO), RemoteMerchantCustomerVO.class);
            if (!CollectionUtils.isEmpty(merchantCustomerBasePage.getRecords())) {
                onlineSalesCopyMerchantCustomers.addAll(merchantCustomerBasePage.getRecords());
            }
            if (merchantCustomerBasePage.isLast()) {
                break;
            }
            remoteCustomerPageDTO.setPage(remoteCustomerPageDTO.getPage() + 1);
        }
    }

    private void fillOnlineSalesAddMerchantCustomers(List<RemoteMerchantCustomerVO> onlineSalesAddMerchantCustomers) {
        RemoteMerchantCustomerPageDTO remoteCustomerPageDTO = new RemoteMerchantCustomerPageDTO();
        remoteCustomerPageDTO.setPage(1);
        remoteCustomerPageDTO.setSize(50);
        remoteCustomerPageDTO.setMerchantId(commonConfig.getOnlineSalesMerchantId());
        BasePage<RemoteMerchantCustomerVO> merchantCustomerBasePage = null;
        while (merchantCustomerBasePage == null || !CollectionUtils.isEmpty(merchantCustomerBasePage.getRecords())) {
            merchantCustomerBasePage = FeignInvokeUtils.convertPage(iRemoteOnlineSaleCustomerInfoFeign.pageOnlineSalesMerchantCustomer(remoteCustomerPageDTO), RemoteMerchantCustomerVO.class);
            if (!CollectionUtils.isEmpty(merchantCustomerBasePage.getRecords())) {
                onlineSalesAddMerchantCustomers.addAll(merchantCustomerBasePage.getRecords());
            }
            if (merchantCustomerBasePage.isLast()) {
                break;
            }
            remoteCustomerPageDTO.setPage(remoteCustomerPageDTO.getPage() + 1);
        }
    }

}
