package com.yaowu.hera.schedule.agent;

import com.xxl.job.core.context.XxlJobHelper;
import com.xxl.job.core.handler.annotation.XxlJob;
import com.yaowu.hera.domain.agent.biz.IAgentEvaluateBizService;
import com.yaowu.hera.model.dto.agent.AgentEvaluateUserProfileQueryDTO;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.skywalking.apm.toolkit.trace.Trace;
import org.apache.skywalking.apm.toolkit.trace.TraceContext;
import org.springframework.stereotype.Component;

import java.time.Duration;
import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2024/9/18 11:13
 */
@Component
@Slf4j
public class AgentEvaluateXxlJob {

    @Resource
    private IAgentEvaluateBizService agentEvaluateBizService;

    @XxlJob("CustomerSupportAgentEvaluateJobHandler")
    @Trace
    public void customerSupportAgentEvaluateJobHandler() {
        XxlJobHelper.log("客服智能体测评定时任务开始", TraceContext.traceId());
        log.info("客服智能体测评定时任务开始");
        AgentEvaluateUserProfileQueryDTO queryDTO = new AgentEvaluateUserProfileQueryDTO();
        queryDTO.setAgentAppId(1L);
        queryDTO.setLimit(1);
        agentEvaluateBizService.evaluateCustomerSupportAgent(queryDTO);

        XxlJobHelper.log("客服智能体测评定时任务结束", TraceContext.traceId());
        log.info("客服智能体测评定时任务结束");
    }

    /**
     * 生成用户画像数据
     */
    @XxlJob("GenerateUserProfileDataJobHandler")
    public void generateUserProfileDataJobHandler() {
        XxlJobHelper.log("生成用户画像数据开始", TraceContext.traceId());
        log.info("生成用户画像数据开始");
        agentEvaluateBizService.generateUserProfileData(1L);
        XxlJobHelper.log("生成用户画像数据结束", TraceContext.traceId());
        log.info("生成用户画像数据结束");
    }
}
