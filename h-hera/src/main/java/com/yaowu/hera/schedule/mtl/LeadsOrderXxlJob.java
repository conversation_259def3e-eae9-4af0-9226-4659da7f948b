package com.yaowu.hera.schedule.mtl;

import com.xxl.job.core.context.XxlJobHelper;
import com.xxl.job.core.handler.annotation.XxlJob;
import com.yaowu.hera.domain.mtl.leadsorder.biz.ILeadsOrderScheduleService;
import com.yaowu.hera.utils.TraceUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.skywalking.apm.toolkit.trace.Trace;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @email <EMAIL>
 * @creatTime 2025.02.17 13:46:00
 * @description:
 */

@Slf4j
@Component
public class LeadsOrderXxlJob {
	@Autowired
	private ILeadsOrderScheduleService leadsOrderScheduleService;


	@Trace
	@XxlJob("processPayingFailOrderJobHandler")
	public void processPayingFailOrderJobHandler() {
		XxlJobHelper.log("开始执行, 处理超时未支付订单 TID: {}", TraceUtil.traceId());
		log.info("开始执行, 处理超时未支付订单告警通知");
		leadsOrderScheduleService.processPayingFailOrder();
	}

	@Trace
	@XxlJob("processTimeoutPayingOrderJobHandler")
	public void processTimeoutPayingOrderJobHandler() {
		XxlJobHelper.log("开始执行, 订单超时为支付兜底处理 TID: {}", TraceUtil.traceId());
		log.info("开始执行, 处理超时未支付订单");
		leadsOrderScheduleService.processTimeoutPayingOrder();
	}
}
