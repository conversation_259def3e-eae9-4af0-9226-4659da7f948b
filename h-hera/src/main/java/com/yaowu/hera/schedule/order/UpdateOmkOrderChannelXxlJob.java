package com.yaowu.hera.schedule.order;

import com.xxl.job.core.context.XxlJobHelper;
import com.xxl.job.core.handler.annotation.XxlJob;
import com.yaowu.hera.domain.business.biz.IOmkClueBizService;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.skywalking.apm.toolkit.trace.Trace;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @implNote 更新订单线索渠道信息
 */
@Slf4j
@Component
@AllArgsConstructor
public class UpdateOmkOrderChannelXxlJob {

    private final IOmkClueBizService omkClueBizService;

    /**
     * 更新客户渠道来源
     */
    @XxlJob("updateOmkOrderChannelJobHandler")
    @Trace
    public void updateOmkOrderChannelJobHandler() {
        XxlJobHelper.log("开始执行”更新电销订单渠道信息“");
        long start = System.currentTimeMillis();
        log.info("开始执行”更新电销订单渠道信息“");
        omkClueBizService.updateOmkOrderChannelInfo();
        log.info("结束执行”更新电销订单渠道信息“");
        long end = System.currentTimeMillis();
        XxlJobHelper.log("执行”更新电销订单渠道信息“结束，用时：" + (end - start) + "毫秒");
    }

}
