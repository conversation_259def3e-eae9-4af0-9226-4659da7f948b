package com.yaowu.hera.config.typhandler;

import org.apache.ibatis.type.LocalDateTypeHandler;

import java.sql.CallableStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.time.LocalDate;

/**
 * <AUTHOR>
 * @date 2023/6/3
 */
public class CustomizeLocalDateTypeHandler extends LocalDateTypeHandler {

    private final static LocalDate DEFAULT_LOCAL_DATE = LocalDate.of(1971, 1, 1);

    @Override
    public LocalDate getResult(ResultSet rs, String columnName) throws SQLException {
        LocalDate result = super.getResult(rs, columnName);
        return handlerLocalDate(result);
    }

    @Override
    public LocalDate getResult(ResultSet rs, int columnIndex) throws SQLException {
        LocalDate result = super.getResult(rs, columnIndex);
        return handlerLocalDate(result);
    }

    @Override
    public LocalDate getResult(CallableStatement cs, int columnIndex) throws SQLException {
        LocalDate result = super.getResult(cs, columnIndex);
        return handlerLocalDate(result);
    }

    private LocalDate handlerLocalDate(LocalDate result) {
        if (result == null) {
            return null;
        }
        // 时间小于或等于1971-01-01 00:00:00.000的，默认为空
        if (result.isBefore(DEFAULT_LOCAL_DATE) || result.isEqual(DEFAULT_LOCAL_DATE)) {
            return null;
        }
        return result;
    }
}
