package com.yaowu.hera.config.nacos.mtl;

import com.alibaba.nacos.api.config.ConfigType;
import com.alibaba.nacos.api.config.annotation.NacosConfigurationProperties;
import com.google.common.collect.Lists;
import com.yaowu.hera.utils.JsonUtils;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;
import java.util.Optional;

/**
 * <AUTHOR>
 */
@Slf4j
@Data
@Component
@NacosConfigurationProperties(
        dataId = "mtl-config-dispatch",
        groupId = "${nacos.config.group}",
        type = ConfigType.YAML,
        autoRefreshed = true
)
public class LeadsDispatchRuleConfig {

    /**
     * 高价值报价单线索规则
     */
    private List<DispatchRuleConfigItem> highValueDispatch = Lists.newArrayList();


    public DeviceCategoryDispatchRule getDispatchRules(Long firstCategoryId, Long secondCategoryId, String cityName) {
        if (CollectionUtils.isEmpty(this.highValueDispatch)) {
            log.error("LeadsDispatchRuleConfigNotFound");
            return null;
        }

        DispatchRuleConfigItem configItem = this.highValueDispatch.stream()
                .filter(item -> ruleCheck(item, firstCategoryId, secondCategoryId, cityName))
                .findFirst()
                .orElse(null);
        DeviceCategoryDispatchRule rule = Optional.ofNullable(configItem)
                .map(item -> DeviceCategoryDispatchRule.of(item.durationThreshold, item.estimatedPriceThreshold))
                .orElse(null);
        if (rule != null) {
            log.info("LeadsDispatchRuleConfig RuleFound, firstCategoryId:{}, secondCategoryId:{}, cityName:{}, rule: {}",
                    firstCategoryId, secondCategoryId, cityName, JsonUtils.toJson(configItem));
        } else {
            log.info("LeadsDispatchRuleConfig RuleNotFound, firstCategoryId:{}, secondCategoryId:{}, cityName:{}",
                    firstCategoryId, secondCategoryId, cityName);
        }
        return rule;
    }

    private boolean ruleCheck(DispatchRuleConfigItem item, Long firstCategoryId, Long secondCategoryId, String cityName) {
        boolean categoryCheck = (!CollectionUtils.isEmpty(item.fistCategoryIds)
                && firstCategoryId != null && firstCategoryId > 0 && item.fistCategoryIds.contains(firstCategoryId))
                || (!CollectionUtils.isEmpty(item.secondCategoryIds)
                && secondCategoryId != null && secondCategoryId > 0 && item.secondCategoryIds.contains(secondCategoryId));
        boolean cityCheck = (Boolean.FALSE.equals(item.getLimitedCity()) ||
                        (Boolean.TRUE.equals(item.getLimitedCity()) &&
                                !CollectionUtils.isEmpty(item.getLimitedCityNames()) && item.getLimitedCityNames().contains(cityName)));
        return (categoryCheck && cityCheck);
    }


    /**
     * 配置项
     * <p>
     * fistCategoryIds 和 secondCategoryIds 互斥，不能同时为空
     */
    @Data
    public static class DispatchRuleConfigItem implements Serializable {

        private static final long serialVersionUID = -7131937322177436817L;

        /**
         * 限定的一级品类ID列表
         */
        private List<Long> fistCategoryIds;
        /**
         * 限定的二级品类ID列表
         */
        private List<Long> secondCategoryIds;
        /**
         * 租期阈值
         */
        private Integer durationThreshold;
        /**
         * 预估值阈值
         */
        private BigDecimal estimatedPriceThreshold;
        /**
         * 是否限定城市
         */
        private Boolean limitedCity;
        /**
         * 限定的城市名称列表
         */
        private List<String> limitedCityNames;
    }

}
