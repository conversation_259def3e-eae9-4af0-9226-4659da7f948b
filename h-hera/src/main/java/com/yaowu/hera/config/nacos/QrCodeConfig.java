package com.yaowu.hera.config.nacos;

import cn.hutool.core.collection.CollUtil;
import com.alibaba.nacos.api.config.ConfigType;
import com.alibaba.nacos.api.config.annotation.NacosConfigurationProperties;
import lombok.Data;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Data
@Component
@NacosConfigurationProperties(dataId = "${spring.application.name}",
        prefix = "qr-code",
        autoRefreshed = true, type = ConfigType.YAML, groupId = "${nacos.config.group}")
public class QrCodeConfig {

    /**
     * 绑码入口白名单（手机号）
     */
    private List<String> bindQrCodeEntryWhiteList;

    /**
     * 场景列表
     */
    private List<UsageScenario> usageScenarioList;

    /**
     * 位置列表
     */
    private List<Position> positionList;

    @Data
    public static class UsageScenario {

        private Integer code;

        private String name;
    }

    @Data
    public static class Position {

        private Integer code;

        private String name;

        private List<Integer> usageScenarioCodes;
    }

    public List<Position> getPositionListByUsageScenarioCode(Integer usageScenarioCode) {
        if (CollUtil.isEmpty(positionList)) {
            return new ArrayList<>();
        }
        if (usageScenarioCode == null) {
            return positionList;
        }
        return positionList.stream().filter(position ->
                position.getUsageScenarioCodes() != null && position.getUsageScenarioCodes().contains(usageScenarioCode)).collect(Collectors.toList());
    }

    public Map<Integer, String> getUsageScenarioMap() {
        return usageScenarioList != null ? usageScenarioList.stream()
                .collect(Collectors.toMap(UsageScenario::getCode, UsageScenario::getName, (v1, v2) -> v2)) : new HashMap<>();
    }

    public Map<Integer, String> getPositionMap() {
        return positionList != null ? positionList.stream()
                .collect(Collectors.toMap(Position::getCode, Position::getName, (v1, v2) -> v2)) : new HashMap<>();
    }
}
