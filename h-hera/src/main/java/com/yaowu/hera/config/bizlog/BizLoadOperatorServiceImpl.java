package com.yaowu.hera.config.bizlog;

import com.freedom.bizlog.core.opeartor.BizLoadOperatorService;
import com.freedom.security.common.SecurityContext;
import com.freedom.security.common.UserDetailsDto;
import org.springframework.stereotype.Component;

import java.util.Map;
import java.util.Optional;

/**
 * <AUTHOR>
 * @description
 * @date 2023:05:05 19:16:10
 */
@Component
public class BizLoadOperatorServiceImpl implements BizLoadOperatorService {

    @Override
    public Optional<String> getOperatorName() {
        return Optional.ofNullable(Optional.ofNullable(SecurityContext.getCurrentUser()).orElse(new UserDetailsDto()).getRealName());
    }

    @Override
    public Optional<String> getOperatorId() {
        return Optional.ofNullable(Optional.ofNullable(SecurityContext.getCurrentUser()).orElse(new UserDetailsDto()).getUserId() + "");
    }

    @Override
    public Map<String, Object> getExtra() {
        return BizLoadOperatorService.super.getExtra();
    }
}
