package com.yaowu.hera.enums.mtl;

import com.baomidou.mybatisplus.annotation.IEnum;
import lombok.Getter;

import java.util.Objects;

/**
 * @Author: liuzhenpeng
 * @Date: 2024/11/29 15:55
 */
@Getter
public enum BindDirectionEnum implements IEnum<Integer> {
    CUSTOMER_CALLER(1, "客主叫"),
    MERCHANT_CALLER(2, "商主叫");

    private final Integer code;
    private final String desc;


    BindDirectionEnum(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    @Override
    public Integer getValue() {
        return code;
    }

    public static BindDirectionEnum getByCode(Integer code){
        if (Objects.isNull(code)){
            return null;
        }
        for (BindDirectionEnum typeEnum:values()){
            if (Objects.equals(typeEnum.code,code)){
                return typeEnum;
            }
        }
        return null;
    }
}
