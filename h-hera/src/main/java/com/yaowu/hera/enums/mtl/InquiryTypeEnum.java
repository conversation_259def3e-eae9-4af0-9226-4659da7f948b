package com.yaowu.hera.enums.mtl;

import com.baomidou.mybatisplus.annotation.IEnum;
import com.yaowu.heraapi.enums.mtl.RemoteInquiryTypeEnum;
import lombok.Getter;

import java.util.Objects;

/**
 * 询价类型
 */
@Getter
public enum InquiryTypeEnum implements IEnum<Integer> {

    CUSTOMER_FIND_MERCHANT(1, "客找商"),

    MERCHANT_FIND_CUSTOMER(2, "商找客"),

;

    private final Integer code;
    private final String desc;


    InquiryTypeEnum(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    @Override
    public Integer getValue() {
        return code;
    }



    public static InquiryTypeEnum getByCode(Integer code){
        if (Objects.isNull(code)){
            return null;
        }
        for (InquiryTypeEnum typeEnum:values()){
            if (Objects.equals(typeEnum.code,code)){
                return typeEnum;
            }
        }
        return null;
    }
}
