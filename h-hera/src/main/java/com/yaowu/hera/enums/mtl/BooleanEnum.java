package com.yaowu.hera.enums.mtl;

import com.baomidou.mybatisplus.annotation.IEnum;
import lombok.Getter;

/**
 * <AUTHOR>
 * @since 2024/4/21
 */
@Getter
public enum BooleanEnum implements IEnum<Integer> {

    NO(0, false, "否"),
    YES(1, true, "是")
    ;

    private final Integer code;

    private final Boolean booleanVal;

    private final String desc;

    BooleanEnum(Integer code, Boolean booleanVal, String desc) {
        this.code = code;
        this.booleanVal = booleanVal;
        this.desc = desc;
    }

    @Override
    public Integer getValue() {
        return code;
    }

    public static BooleanEnum codeOf(Integer code) {
        if (code == null) {
            return null;
        }
        if (code.compareTo(0) == 0) {
            return NO;
        }
        if (code.compareTo(1) == 0) {
            return YES;
        }
        return null;
    }

    public static BooleanEnum booleanOf(Boolean booleanVal) {
        if (booleanVal == null) {
            return null;
        }
        if (booleanVal.compareTo(Boolean.TRUE) == 0) {
            return YES;
        } else {
            return NO;
        }
    }
}
