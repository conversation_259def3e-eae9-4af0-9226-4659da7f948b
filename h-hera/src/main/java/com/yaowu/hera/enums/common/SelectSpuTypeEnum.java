package com.yaowu.hera.enums.common;

import com.fasterxml.jackson.annotation.JsonValue;
import com.yaowu.hera.utils.BizException;
import com.yaowu.hera.utils.ErrorCode;
import lombok.Getter;
import lombok.RequiredArgsConstructor;

import java.util.Arrays;
import java.util.Objects;

/**
 * <AUTHOR>
 * @email <EMAIL>
 * @description 功能：
 * @creatTime 2023.04.26 15:04:00
 */

@Getter
@RequiredArgsConstructor
public enum SelectSpuTypeEnum {

    SELECT_ALL(1, "全选"),
    SELECT_PARTIAL(2, "部分选中");
    @JsonValue
    private final Integer code;

    private final String msg;

    public static SelectSpuTypeEnum parse(Integer code) {
        BizException.condition(Objects.isNull(code), ErrorCode.CLUE_DEVICE_SELECT_SPU_TYPE);
        return Arrays.stream(SelectSpuTypeEnum.values())
                .filter(item -> item.getCode().equals(code))
                .findFirst()
                .orElseThrow(() -> BizException.newOne(ErrorCode.CLUE_DEVICE_SELECT_SPU_TYPE));
    }

}
