package com.yaowu.hera.enums.audio;

import com.baomidou.mybatisplus.annotation.IEnum;
import com.yaowu.hera.model.entity.marketing.BaseMarketingConfigRule;
import com.yaowu.hera.model.vo.llm.BaseLlmResultVO;
import com.yaowu.hera.model.vo.llm.RemoteAudioTextExtractVO;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Objects;

/**
 * <AUTHOR>
 */
@Getter
@AllArgsConstructor
public enum InvokeLlmApiEnum implements IEnum<Integer> {
    MTL_QUOTATION_LEAD_CALL_RECORDING_DATA(1, "           目标\n" +
            "            从非结构化文本中提取租赁工程机械设备的关键信息，包括：\n" +
            "    \n" +
            "            设备一级品类名称\n" +
            "            设备二级品类名称\n" +
            "            设备SPU名称\n" +
            "            设备租赁时长\n" +
            "            设备租赁台量\n" +
            "            是否有询价\n" +
            "            文本说明\n" +
            "            每行记录的头部1、2标识了通话双方（通常1代表客户，2代表商户）；他们讨论租赁工程机械设备，如高空车、曲臂车、直臂车、升降车、挖机、随车吊等。\n" +
            "    \n" +
            "            问题\n" +
            "            请回答以下问题：\n" +
            "    \n" +
            "            双方讨论了什么类型的设备？\n" +
            "            设备可能用于哪个地点？\n" +
            "            设备的主要用途是什么？\n" +
            "            需要租多少台设备？\n" +
            "            租赁的时间长度是多少？\n" +
            "            租赁价格是多少？\n" +
            "            通话双方是否交换了手机号或微信等联系方式？\n" +
            "            通话结束后，双方是否计划继续联系？\n" +
            "            是否提到租赁平台（如机械星球等）？\n" +
            "            如果没有达成租赁协议，可能的原因是什么？\n" +
            "            变量定义\n" +
            "    \n" +
            "            设备一级品类名称：最高层级分类，如吊车、起重设备。\n" +
            "            设备二级品类名称：一级品类下的进一步分类，如吊篮、车载、挖机。\n" +
            "            设备SPU名称：具体设备产品单元，如吊篮。\n" +
            "            设备租赁时长：租赁时间，以小时为单位。请转换为小时数，例如“1天” = 24小时。\n" +
            "            租赁设备台量：租赁数量，如“1台”、“3台”、“5台以上”等。\n" +
            "            是否有询价：询价相关的词汇或短语，如“报价单”、“多少钱”、“价格”等。\n" +
            "            转换规则\n" +
            "    \n" +
            "            设备租赁时长：识别时间单位并转换为小时数。\n" +
            "            1天 = 24小时\n" +
            "            1周 = 168小时\n" +
            "            1月 = 720小时（假设30天）\n" +
            "            1年 = 8760小时（假设365天）\n" +
            "            模糊描述如“左右”按标准时间单位处理。\n" +
            "            租赁设备台量：\n" +
            "            1台: 1台\n" +
            "            2台: 2台\n" +
            "            3台: 3台\n" +
            "            3台以上: 4台及以上\n" +
            "            5台以上: 6台及以上\n" +
            "            3-5台: 3-5台之间\n" +
            "            处理方式\n" +
            "            请处理以下文本并提取信息：\n" +
            "            {text}\n" +
            "    \n" +
            "            指示\n" +
            "            当前的通话上下文在描述是同一条线索的信息，请严格按如下格式返回线索信息：\n" +
            "    \n" +
            "            <LEAD_RESULT>\n" +
            "            录音原文：<原始文本信息>\n" +
            "            录音总结：<对文本的总结>\n" +
            "            用车地址：<线索的用车地址>\n" +
            "            设备一级品类名称: <如果未提及，请填写\"NULL\">\n" +
            "            设备二级品类名称: <如果未提及，请填写\"NULL\">\n" +
            "            设备SPU名称: <如果未提及，请填写\"NULL\">\n" +
            "            台量: <需求的台量>\n" +
            "            起租时间: <客商沟通的起租时间>\n" +
            "            租赁周期: <客商沟通的租赁周期>\n" +
            "            租金备注:<客商沟通的租金，元 客商沟通的运费，元 客商沟通的押金，元  客商沟通的结算和付款规则 客商沟通的发票信息> \n" +
            "            设备用途: <客商沟通的设备用途>\n" +
            "            现场场景: <客商沟通的现场场景>\n" +
            "            用车地址: <客商沟通的用车地址>\n" +
            "            设备品类: <客商沟通的设备品类及SPU>\n" +
            "            设备品类特殊要求: <客商沟通的设备特殊要求>\n" +
            "            交换联系方式: <客商是否交换了电话号码>\n" +
            "            成交预测: <客商成交的可能性>\n" +
            "            </LEAD_RESULT>\n" +
            "    \n" +
            "            注意事项\n" +
            "    \n" +
            "            输入是一条线索的上下文通话信息,应当分析返回一条线索的对应的信息\n" +
            "            确保逻辑一致性，从上下文中提取相关信息。\n" +
            "            没有识别到任何线索信息时，不要输出结果。注意只有在通话中存在直接的电话号码交换,才认定为交换联系方式\n" +
            "            输出分析过程和结果。\n" +
            "    ", "MTL报价单线索通话录音数据提示词", RemoteAudioTextExtractVO.class);
    private final Integer code;
    private final String prompt;
    private final String desc;
    private final Class<?> bizClz;


    @Override
    public Integer getValue() {
        return null;
    }

    public static BaseLlmResultVO invokeStructDataVO(String text, InvokeLlmApiEnum invokeLlmApiEnum) {
        if (Objects.nonNull(invokeLlmApiEnum) && MTL_QUOTATION_LEAD_CALL_RECORDING_DATA == invokeLlmApiEnum) {
            return RemoteAudioTextExtractVO.parse(text);
        }
        return null;
    }


}
