package com.yaowu.hera.enums.merchant;

import com.baomidou.mybatisplus.annotation.IEnum;
import lombok.AllArgsConstructor;

/**
 * <AUTHOR>
 * @date 2024/9/24-17:01
 */
@AllArgsConstructor
public enum BusinessCheckTypeEnum implements IEnum<Integer> {

    /**
     * 商店主图
     */
    STORE_MAIN_IMAGE(1);

    private final Integer checkType;

    @Override
    public Integer getValue() {
        return this.checkType;
    }

    public static BusinessCheckTypeEnum of(Integer value) {
        for (BusinessCheckTypeEnum item : values()) {
            if (item.getValue().equals(value)) {
                return item;
            }
        }
        return null;
    }
}
