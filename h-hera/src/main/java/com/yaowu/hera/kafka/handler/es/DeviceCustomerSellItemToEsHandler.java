//package com.yaowu.hera.kafka.handler.es;
//
//import cn.hutool.core.collection.CollUtil;
//import com.yaowu.hera.domain.feign.KratosFeignBizService;
//import com.yaowu.hera.kafka.handler.AbstractDataWorksBizDataToEsHandler;
//import com.yaowu.hera.kafka.model.DataWorksDataModelInfo;
//import com.yaowu.hera.kafka.model.db.DeviceCustomerSellItemModel;
//import com.yaowu.hera.model.bo.es.DeviceEsWriteBO;
//import com.yaowu.kratosapi.model.vo.device.RemoteDeviceCategoryDetailVO;
//import org.springframework.beans.factory.annotation.Autowired;
//import org.springframework.stereotype.Component;
//
//import java.util.Set;
//
///**
// * <AUTHOR>
// * @date 2024/7/31-20:13
// */
//@Component
//public class DeviceCustomerSellItemToEsHandler extends AbstractDataWorksBizDataToEsHandler<DeviceCustomerSellItemModel> {
//
//    @Autowired
//    private KratosFeignBizService kratosFeignBizService;
//
//    @Override
//    public Set<Class<?>> modelTopics() {
//        return CollUtil.newHashSet(DeviceCustomerSellItemModel.class);
//    }
//
//    @Override
//    protected ActionExecuteResult execute(DataWorksDataModelInfo<DeviceCustomerSellItemModel> model) {
//        DeviceCustomerSellItemModel data = model.data();
//        if (isDelete(model) || DeviceCustomerSellItemModel.DISABLE_STATUS.equals(data.getStatus())) {
//            //注意事项，这里只处理二级类目id数据
//            Long parentId = data.getParentId();
//            if (parentId == 0) {
//                // 一级spu数据
//                Long spuId = data.getId();
//                // 品类id(最后一级)
//                Long categoryId = data.getCategoryId();
//                // 根据二级类目查找数据
//                RemoteDeviceCategoryDetailVO categoryDetailVO = kratosFeignBizService.getCatById(categoryId);
//                Long firstCategoryId = categoryDetailVO == null ? null : categoryDetailVO.getParentId();
//                if (spuId == null || firstCategoryId == null || categoryId == null) {
//                    return ActionExecuteResult.success();
//                }
//                String s = DeviceEsWriteBO.docId(firstCategoryId, categoryId, spuId);
//                elasticsearchService.esDelete(s, esIndexConfig.getIndexDevice());
//                // 这里删除原来得数据，还得在重新跑一次
//            } else {
//                // do nothing 二级spu数据
//            }
//        }
//         merchantSearchService.aggDeviceInfos();
//        return ActionExecuteResult.success();
//    }
//}
