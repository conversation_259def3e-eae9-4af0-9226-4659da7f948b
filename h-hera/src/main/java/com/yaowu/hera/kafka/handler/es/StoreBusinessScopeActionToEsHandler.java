//package com.yaowu.hera.kafka.handler.es;
//
//import cn.hutool.core.collection.CollUtil;
//import com.yaowu.hera.kafka.handler.AbstractDataWorksBizDataToEsHandler;
//import com.yaowu.hera.kafka.model.DataWorksDataModelInfo;
//import com.yaowu.hera.kafka.model.db.StoreBusinessScopeModel;
//import org.springframework.stereotype.Component;
//
//import java.util.Set;
//
///**
// * <AUTHOR>
// * @date 2024/7/31-11:04
// */
//@Component
//public class StoreBusinessScopeActionToEsHandler extends AbstractDataWorksBizDataToEsHandler<StoreBusinessScopeModel> {
//    @Override
//    public Set<Class<?>> modelTopics() {
//        return CollUtil.newHashSet(StoreBusinessScopeModel.class);
//    }
//
//    @Override
//    protected ActionExecuteResult execute(DataWorksDataModelInfo<StoreBusinessScopeModel> model) {
//        StoreBusinessScopeModel data = model.data();
//        merchantSearchService.aggStoreInfos(CollUtil.newHashSet(data.getStoreId()), indexFunction);
//        return ActionExecuteResult.success();
//    }
//}
