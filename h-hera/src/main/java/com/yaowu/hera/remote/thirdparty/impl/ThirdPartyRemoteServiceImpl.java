package com.yaowu.hera.remote.thirdparty.impl;

import cn.hutool.core.stream.StreamUtil;
import com.freedom.feign.utils.FeignInvokeUtils;
import com.freedom.web.model.resp.BaseResult;
import com.genlian.thirdparty.api.enums.wxmini.WxMiniEnum;
import com.genlian.thirdparty.api.feign.dict.IThirdPartyDictFeign;
import com.genlian.thirdparty.api.feign.lbs.IRemoteLbsDirectionFeign;
import com.genlian.thirdparty.api.feign.lbs.IRemoteLbsGeoFeign;
import com.genlian.thirdparty.api.feign.wxmini.IRemoteWxMiniServiceFeign;
import com.genlian.thirdparty.api.model.dto.dict.ThirdPartyQueryDictDTO;
import com.genlian.thirdparty.api.model.dto.lbs.RemoteSearchCityInfoDTO;
import com.genlian.thirdparty.api.model.dto.wxmini.GenerateUrlLinkDTO;
import com.genlian.thirdparty.api.model.vo.dict.ThirdPartyDictListVO;
import com.genlian.thirdparty.api.model.vo.lbs.RemoteAreaAddressModelVO;
import com.genlian.thirdparty.api.model.vo.lbs.RemoteCityAreaVO;
import com.yaowu.hera.domain.common.feign.CommonBizThirdPartyServiceFeign;
import com.yaowu.hera.model.vo.common.AreaTreeVO;
import com.yaowu.hera.remote.thirdparty.IThirdPartyRemoteService;
import jakarta.annotation.Resource;
import java.math.BigDecimal;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;
import jodd.util.StringUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

@Service
@Slf4j
public class ThirdPartyRemoteServiceImpl implements IThirdPartyRemoteService {

    @Autowired
    private IRemoteWxMiniServiceFeign wxMiniServiceFeign;

    @Resource
    private CommonBizThirdPartyServiceFeign commonBizThirdPartyServiceFeign;

    @Autowired
    private IRemoteLbsGeoFeign remoteLbsGeoFeign;

    @Autowired
    private IRemoteLbsDirectionFeign remoteLbsDirectionFeign;

    @Resource
    private IThirdPartyDictFeign thirdPartyDictFeign;

    /**
     * 生成小程序链接
     *
     * @param wxMiniEnum
     * @param path
     * @param query
     * @return
     */
    @Override
    public String generateWxMiniUrlLink(WxMiniEnum wxMiniEnum, String path, String query) {
        GenerateUrlLinkDTO generateUrlLinkDTO = new GenerateUrlLinkDTO();
        generateUrlLinkDTO.setWxMini(wxMiniEnum);
        generateUrlLinkDTO.setPath(path);
        generateUrlLinkDTO.setQuery(query);
        String generateUrlLink = null;
        try {
            generateUrlLink = FeignInvokeUtils.convert(wxMiniServiceFeign.generateUrlLink(generateUrlLinkDTO), String.class);
        } catch (Exception e) {
            log.error("generateWxMiniUrlLink 异常 [{}]", e.getMessage(), e);
        }
        return generateUrlLink;
    }

    @Override
    public List<AreaTreeVO> areaTree() {
        BaseResult<List<AreaTreeVO>> result = commonBizThirdPartyServiceFeign.areaTree();
        return FeignInvokeUtils.convertList(result, AreaTreeVO.class);
    }

    @Override
    public List<AreaTreeVO> cityTree() {
        BaseResult<List<AreaTreeVO>> result = commonBizThirdPartyServiceFeign.cityTree();
        return FeignInvokeUtils.convertList(result, AreaTreeVO.class);
    }

    @Override
    public RemoteCityAreaVO getRemoteCityAreaVOByCityName(String cityName) {
        if (StringUtil.isBlank(cityName)) {
            return null;
        }
        RemoteSearchCityInfoDTO remoteSearchCityInfoDTO = new RemoteSearchCityInfoDTO();
        Set<String> cityNames = new HashSet<>();
        cityNames.add(cityName);
        remoteSearchCityInfoDTO.setCityNames(cityNames);
        List<RemoteCityAreaVO> remoteCityAreaVOList =
                FeignInvokeUtils.convertList(remoteLbsGeoFeign.queryCityCodeListByNames(remoteSearchCityInfoDTO), RemoteCityAreaVO.class);
        return StreamUtil.of(remoteCityAreaVOList).findFirst().orElse(null);
    }

    @Override
    public RemoteCityAreaVO getRemoteCityAreaVOByIp(String ip) {
        if (StringUtil.isBlank(ip)) {
            return null;
        }
        try {
            List<RemoteCityAreaVO> remoteCityAreaVOList =
                    FeignInvokeUtils.convertList(remoteLbsGeoFeign.queryCityCodeListByIP(ip), RemoteCityAreaVO.class);
            if (CollectionUtils.isEmpty(remoteCityAreaVOList) || remoteCityAreaVOList.get(0) == null) {
                log.warn("调用三方服务根据ip获取地址空数据");
                return null;
            }
            return StreamUtil.of(remoteCityAreaVOList).findFirst().orElse(null);
        } catch (Exception e) {
            log.error("调用三方服务根据ip获取地址异常", e);
            return null;
        }
    }


    @Override
    public BigDecimal drivingOfAMap(String origin, String destination, Integer strategy) {
        if (StringUtil.isBlank(origin) || StringUtil.isBlank(destination) || strategy == null) {
            return null;
        }
        BigDecimal distance =
                FeignInvokeUtils.convert(remoteLbsDirectionFeign.drivingOfAMap(origin, destination, strategy), BigDecimal.class);
        return distance;
    }

    @Override
    public RemoteAreaAddressModelVO searchGeoAddressByAddressText(String address) {
        RemoteAreaAddressModelVO remoteAreaAddressModelVO = FeignInvokeUtils.convert(remoteLbsGeoFeign.searchGeoAddressByAddressText(address), RemoteAreaAddressModelVO.class);
        return remoteAreaAddressModelVO;
    }

    public List<ThirdPartyDictListVO> getDictList(String dictType, String code) {
        ThirdPartyQueryDictDTO dto = new ThirdPartyQueryDictDTO();
        dto.setDictType(dictType);
        dto.setCode(code);
        BaseResult<List<ThirdPartyDictListVO>> dictList = thirdPartyDictFeign.getDictList(dto);
        return FeignInvokeUtils.convertList(dictList, ThirdPartyDictListVO.class);
    }

    @Override
    public List<ThirdPartyDictListVO> getDictList(String dictType) {
        ThirdPartyQueryDictDTO dto = new ThirdPartyQueryDictDTO();
        dto.setDictType(dictType);
        BaseResult<List<ThirdPartyDictListVO>> dictList = thirdPartyDictFeign.getDictList(dto);
        return FeignInvokeUtils.convertList(dictList, ThirdPartyDictListVO.class);
    }

    @Override
    public Map<String, String> getSourceCodeToNameDictName(String dictType) {
        List<ThirdPartyDictListVO> dictList = getDictList(dictType);
        return com.yaowu.hera.utils.StreamUtil.toMap(dictList, ThirdPartyDictListVO::getCode, ThirdPartyDictListVO::getName);
    }
}
