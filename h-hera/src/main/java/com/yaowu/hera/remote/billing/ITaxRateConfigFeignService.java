package com.yaowu.hera.remote.billing;

import com.yaowu.billing.api.model.dto.invoice.tax.RemoteTaxRateConfigListQueryDTO;
import com.yaowu.billing.api.model.vo.invoice.RemoteTaxRateConfigVO;

import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * 履约服务单
 *
 * <AUTHOR>
 * @date 2023-06-27 20:05
 */
public interface ITaxRateConfigFeignService {

    /**
     * 获取税率配置列表
     */
    List<RemoteTaxRateConfigVO> configList(RemoteTaxRateConfigListQueryDTO dto);

    /**
     * 获取税率配置列表
     */
    Map<Long, List<RemoteTaxRateConfigVO>> getConfigListByMerchantIds(Set<Long> merchantIds);

}
