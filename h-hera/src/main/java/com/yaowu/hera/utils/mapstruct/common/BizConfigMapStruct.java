package com.yaowu.hera.utils.mapstruct.common;


import com.google.common.base.Strings;
import com.yaowu.hera.enums.common.BizConfigTypeEnum;
import com.yaowu.hera.model.entity.common.BizConfig;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.MappingConstants;
import java.util.Optional;
/**
 * <AUTHOR>
 */
@Mapper(
        componentModel = MappingConstants.ComponentModel.SPRING,
        imports = {
                Strings.class,
                Optional.class,
        }
)
public interface BizConfigMapStruct {


    @Mapping(target = "configType", source = "configType")
    @Mapping(target = "configDesc", expression = "java(configType == null ? \"\" : configType.getDesc())")
    @Mapping(target = "configContent", source = "content")
    @Mapping(target = "status", expression = "java(com.yaowu.hera.enums.common.BizConfigStatusEnum.ENABLED)")
    BizConfig toBizConfig(BizConfigTypeEnum configType, String content);

}
