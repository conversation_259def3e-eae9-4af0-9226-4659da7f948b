package com.yaowu.hera.utils.mapstruct.association;

import com.alibaba.nacos.common.utils.StringUtils;
import com.yaowu.hera.model.entity.leads.LeadsDeviceAssociationWord;
import com.yaowu.hera.model.entity.leads.LeadsDeviceCategory;
import com.yaowu.hera.utils.StreamUtil;
import com.yaowu.hera.utils.StringUtil;
import com.yaowu.heraapi.enums.device.ChannelTypeEnum;
import com.yaowu.heraapi.model.dto.association.RemoteAddLeadsDeviceAssociationDTO;
import com.yaowu.heraapi.model.dto.association.RemoteEditLeadsDeviceAssociationDTO;
import com.yaowu.heraapi.model.vo.association.RemoteLeadsDeviceCategoryVO;
import com.yaowu.heraapi.model.vo.association.RemoteLeadsDeviceAssociationDetailVO;
import com.yaowu.heraapi.model.vo.association.RemoteLeadsDeviceAssociationVO;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.factory.Mappers;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Objects;


/**
 * <AUTHOR>
 */
@Mapper
public interface LeadsDeviceAssociationMapStruct {

    LeadsDeviceAssociationMapStruct INSTANCE = Mappers.getMapper(LeadsDeviceAssociationMapStruct.class);


    default RemoteLeadsDeviceAssociationVO toRemoteLeadsDeviceAssociationVO(LeadsDeviceAssociationWord associationWord,
                                                                            Map<Long, List<LeadsDeviceCategory>> categoryIdToDeviceList) {
        if (Objects.isNull(associationWord)) {
            return null;
        }
        List<String> channelTypeDescList = new ArrayList<>();
        RemoteLeadsDeviceAssociationVO remoteLeadsDeviceAssociationVO = RemoteLeadsDeviceAssociationVO
                .builder()
                .id(associationWord.getId())
                .bizId(associationWord.getBizId())
                .bizType(associationWord.getBizType())
                .associationWord(associationWord.getAssociationWord())
                .lastModifier(associationWord.getModifier())
                .lastModifiedTime(associationWord.getUpdateTime()).build();
        List<LeadsDeviceCategory> leadsDeviceCategories = categoryIdToDeviceList.get(associationWord.getBizId());
        List<RemoteLeadsDeviceCategoryVO> leadsDeviceCategoryVOList = toLeadsDeviceCategoryVOList(leadsDeviceCategories);
        for (ChannelTypeEnum channelTypeEnum : ChannelTypeEnum.values()) {
            StreamUtil.of(leadsDeviceCategoryVOList).filter(e -> e.getChannelType().contains(channelTypeEnum.getMsg())).findFirst()
                    .ifPresent(
                            e -> {
                                channelTypeDescList.add(channelTypeEnum.getDesc() + "：" + e.getCategoryName());
                            }
                    );
        }
        remoteLeadsDeviceAssociationVO.setDeviceAlias(channelTypeDescList);
        return remoteLeadsDeviceAssociationVO;
    }

    LeadsDeviceAssociationWord toLeadsDeviceAssociationWord(RemoteAddLeadsDeviceAssociationDTO dto);

    default LeadsDeviceAssociationWord toUpdatedLeadsDeviceAssociationWord(LeadsDeviceAssociationWord oldLeadsDeviceAssociationWord,
                                                                   RemoteEditLeadsDeviceAssociationDTO dto){
        oldLeadsDeviceAssociationWord.setBizId(dto.getBizId());
        oldLeadsDeviceAssociationWord.setBizType(dto.getBizType());
        oldLeadsDeviceAssociationWord.setBizName(dto.getBizName());
        oldLeadsDeviceAssociationWord.setAssociationWord(dto.getAssociationWord());
        return oldLeadsDeviceAssociationWord;
    }

    RemoteLeadsDeviceAssociationDetailVO toRemoteLeadsDeviceAssociationDetailVO(LeadsDeviceAssociationWord leadsDeviceAssociationWord);


    @Mapping(target = "channelType", expression = "java(getChannelTypeList(leadsDeviceCategory))")
    RemoteLeadsDeviceCategoryVO toLeadsDeviceCategoryVO(LeadsDeviceCategory leadsDeviceCategory);

    default List<String> getChannelTypeList(LeadsDeviceCategory leadsDeviceCategory) {
        if (Objects.isNull(leadsDeviceCategory) || StringUtil.isBlank(leadsDeviceCategory.getChannelType())) {
            return Collections.emptyList();
        }
        String[] channelTypesArray = StringUtils.split(leadsDeviceCategory.getChannelType(), ",");
        return Arrays.asList(channelTypesArray);
    }

    List<RemoteLeadsDeviceCategoryVO> toLeadsDeviceCategoryVOList(List<LeadsDeviceCategory> leadsDeviceCategories);
}
