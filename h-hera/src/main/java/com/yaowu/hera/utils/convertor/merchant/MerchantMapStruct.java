package com.yaowu.hera.utils.convertor.merchant;

import com.yaowu.hera.model.dto.popularMerchant.PopularMerchantQueryDTO;
import com.yaowu.hera.model.entity.popluarmerchant.StoreHot;
import com.yaowu.hera.utils.LongUtils;
import com.yaowu.heraapi.model.dto.mtl.popularmerchant.RemotePopularMerchantQueryDTO;
import com.yaowu.heraapi.model.pojo.common.UploadFileModel;
import com.yaowu.heraapi.model.vo.mtl.ModelRangeByCategoryVO;
import com.yaowu.heraapi.model.vo.mtl.RemoteExposureStoreVO;
import com.yaowu.heraapi.model.vo.mtl.RemotePopularMerchantVO;
import com.yaowu.heraapi.model.vo.mtl.RemotePopularStoreVO;
import com.yaowu.melinaapi.model.pojo.RemoteUploadFileModel;
import com.yaowu.melinaapi.model.vo.store.RemoteStoreInfoDetailVO;
import com.yaowu.tagsystemapi.model.vo.tag.TagSimpleVO;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.MappingConstants;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * @className: PopularMerchantMapStruct
 * @author: wtt
 */
@Mapper(componentModel = MappingConstants.ComponentModel.SPRING,imports = {LongUtils.class})
public interface MerchantMapStruct {
    MerchantMapStruct INSTANCE = Mappers.getMapper(MerchantMapStruct.class);


    @Mapping(target = "storeId", source = "id")
    @Mapping(target = "mainPic", ignore = true)
    @Mapping(target = "distance", ignore = true)
    @Mapping(target = "address", ignore = true)
    RemoteExposureStoreVO toRemoteExposureStoreVO(RemoteStoreInfoDetailVO remoteStoreInfoDetailVO);


    List<ModelRangeByCategoryVO> toModelRangeByCategoryVOList(List<TagSimpleVO> tags);

    default ModelRangeByCategoryVO tagSimpleVOToModelRangeByCategoryVO(TagSimpleVO tagSimpleVO) {
        if ( tagSimpleVO == null ) {
            return null;
        }

        ModelRangeByCategoryVO modelRangeByCategoryVO = new ModelRangeByCategoryVO();
        modelRangeByCategoryVO.setId(tagSimpleVO.getTagId());
        modelRangeByCategoryVO.setName(tagSimpleVO.getTagName());
        modelRangeByCategoryVO.setTagCode(tagSimpleVO.getTagCode());
        return modelRangeByCategoryVO;
    }

    UploadFileModel toUploadFileModel(RemoteUploadFileModel remoteUploadFileModel);
}
