package com.yaowu.hera.utils.number;

import org.apache.commons.lang3.StringUtils;

import java.util.HashMap;
import java.util.Map;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * <AUTHOR>
 * @email <EMAIL>
 * @creatTime 2025.03.27 11:19:00
 * @description: 数字提取器
 */


public abstract class NumberExtractor {
	// 定义中文数字映射
	private static final Map<Character, Integer> CHINESE_NUMBER_MAP = new HashMap<>();

	static {
		CHINESE_NUMBER_MAP.put('零', 0);
		CHINESE_NUMBER_MAP.put('一', 1);
		CHINESE_NUMBER_MAP.put('二', 2);
		CHINESE_NUMBER_MAP.put('三', 3);
		CHINESE_NUMBER_MAP.put('四', 4);
		CHINESE_NUMBER_MAP.put('五', 5);
		CHINESE_NUMBER_MAP.put('六', 6);
		CHINESE_NUMBER_MAP.put('七', 7);
		CHINESE_NUMBER_MAP.put('八', 8);
		CHINESE_NUMBER_MAP.put('九', 9);
	}



	public static Long extractNumber(String input) {
		if(StringUtils.isBlank(input)){
			return 0L;
		}
		// 定义匹配数字和中文数字的正则表达式
		Pattern pattern = Pattern.compile("\\d+|[一二三四五六七八九零]+");
		Matcher matcher = pattern.matcher(input);

		while (matcher.find()) {
			String match = matcher.group();
			if (isNumeric(match)) {
				return Long.parseLong(match);
			} else {
				return (long) chineseNumberToArabic(match);
			}
		}
		return 0L;
	}

	// 判断字符串是否为纯数字
	private static boolean isNumeric(String str) {
		for (char c : str.toCharArray()) {
			if (!Character.isDigit(c)) {
				return false;
			}
		}
		return true;
	}

	// 将中文数字转换为阿拉伯数字
	private static int chineseNumberToArabic(String chineseNumber) {
		int result = 0;
		for (char c : chineseNumber.toCharArray()) {
			result = result * 10 + CHINESE_NUMBER_MAP.get(c);
		}
		return result;
	}
}
