package com.yaowu.hera.utils;

import org.springframework.util.StringUtils;

public class PhoneUtil {
    public static String removePrefix(String phone) {
        if(!StringUtils.hasText(phone)){
            return phone;
        }
        return phone.replace("+86", "");
    }

    public static String getLast4Digits(String phoneNumber) {
        if (phoneNumber == null || phoneNumber.length() < 4) {
            return "";
        }
        return phoneNumber.substring(phoneNumber.length() - 4);
    }
}
