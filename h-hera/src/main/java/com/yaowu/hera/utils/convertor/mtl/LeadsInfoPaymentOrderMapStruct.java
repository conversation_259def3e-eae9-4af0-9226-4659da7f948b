package com.yaowu.hera.utils.convertor.mtl;

import cn.hutool.core.bean.BeanUtil;
import com.yaowu.hera.enums.mtl.LeadsInfoPaymentOrderTypeEnum;
import com.yaowu.hera.model.dto.mtl.order.LeadsInfoPaymentOrderCreateDTO;
import com.yaowu.hera.model.dto.mtl.order.LeadsInfoPaymentOrderPaymentSubmitPayDTO;
import com.yaowu.hera.model.dto.mtl.order.LeadsInfoPaymentOrderQueryDTO;
import com.yaowu.hera.model.entity.leads.LeadsInfoPaymentOrder;
import com.yaowu.hera.model.entity.leads.order.BaseLeadsInfoPaymentOrderInfo;
import com.yaowu.hera.model.entity.leads.order.HighLevelLeadsInfoOrderInfo;
import com.yaowu.hera.model.vo.mtl.LeadsInfoPaymentVO;
import com.yaowu.hera.utils.common.ThirdPartyPayOrderUtils;
import com.yaowu.heraapi.enums.mtl.PaymentChannelEnum;
import com.yaowu.heraapi.enums.mtl.order.LeadsInfoPaymentOrderStatusEnum;
import com.yaowu.heraapi.model.dto.mtl.RemoteLeadsInfoPaymentOrderCreateDTO;
import com.yaowu.heraapi.model.dto.mtl.RemoteLeadsInfoPaymentOrderQueryDTO;
import com.yaowu.heraapi.model.dto.mtl.RemoteLeadsInfoPaymentOrderSubmitPayDTO;
import com.yaowu.heraapi.model.vo.mtl.order.RemoteLeadsInfoPaymentOrderDetailVO;
import com.yaowu.heraapi.model.vo.mtl.order.RemoteLeadsInfoPaymentOrderPageVO;
import com.yaowu.heraapi.model.vo.mtl.order.RemoteLeadsInfoPaymentVO;
import com.yaowu.settle.api.enums.mtl.payment.PaymentMethodEnum;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.Named;

/**
 * <AUTHOR>
 * @date 2025/3/3-19:16
 */
@Mapper(imports = {ThirdPartyPayOrderUtils.class})
public interface LeadsInfoPaymentOrderMapStruct {
    LeadsInfoPaymentOrderMapStruct INSTANCE = org.mapstruct.factory.Mappers.getMapper(LeadsInfoPaymentOrderMapStruct.class);

    @Mapping(target = "orderCode", expression = "java(ThirdPartyPayOrderUtils.generateOrderId(\"A\"))")
    @Mapping(target = "orderInfo", expression = "java(toOrderInfo(dto,orderType))")
    @Mapping(target = "orderStatus", constant = "WAITING")
    @Mapping(target = "totalAmount", source = "dto.paymentAmount")
    LeadsInfoPaymentOrder toLeadsInfoPaymentOrderEntity(LeadsInfoPaymentOrderCreateDTO dto, LeadsInfoPaymentOrderTypeEnum orderType);

    LeadsInfoPaymentOrderQueryDTO toQueryDTO(RemoteLeadsInfoPaymentOrderQueryDTO dto);

    @Mapping(target = "orderInfo", expression = "java(toOrderInfo(entity))")
    @Mapping(target = "paymentMethodDesc", source = "paymentMethod", qualifiedByName = "paymentMethodDesc")
    @Mapping(target = "orderStatusDesc", source = "orderStatus", qualifiedByName = "orderStatusDesc")
    RemoteLeadsInfoPaymentOrderPageVO toPageVO(LeadsInfoPaymentOrder entity);

    RemoteLeadsInfoPaymentOrderDetailVO toDetailVO(RemoteLeadsInfoPaymentOrderPageVO entity);

    default RemoteLeadsInfoPaymentOrderDetailVO toDetailVO(LeadsInfoPaymentOrder entity) {
        RemoteLeadsInfoPaymentOrderPageVO pageVO = toPageVO(entity);
        return toDetailVO(pageVO);
    }

    LeadsInfoPaymentOrderPaymentSubmitPayDTO toSubmitPayDTO(RemoteLeadsInfoPaymentOrderSubmitPayDTO dto);

    RemoteLeadsInfoPaymentVO toRemoteLeadsInfoPaymentVO(LeadsInfoPaymentVO vo);

    LeadsInfoPaymentOrderCreateDTO toLeadsInfoPaymentOrderCreateDTO(RemoteLeadsInfoPaymentOrderCreateDTO dto);

    default BaseLeadsInfoPaymentOrderInfo toOrderInfo(LeadsInfoPaymentOrderCreateDTO dto, LeadsInfoPaymentOrderTypeEnum orderType) {
        HighLevelLeadsInfoOrderInfo orderInfo = new HighLevelLeadsInfoOrderInfo();
        orderInfo.setOrderType(orderType);
        orderInfo.setRentAmount(dto.getRentAmount());
        orderInfo.setFreightAmount(dto.getFreightAmount());
        orderInfo.setOtherAmount(dto.getOtherAmount());
        return orderInfo;
    }

    default RemoteLeadsInfoPaymentOrderPageVO.LeadsInfoPaymentOrderInfo toOrderInfo(LeadsInfoPaymentOrder entity) {
        return BeanUtil.copyProperties(entity.getOrderInfo(), RemoteLeadsInfoPaymentOrderPageVO.LeadsInfoPaymentOrderInfo.class);
    }

    @Named("paymentMethodDesc")
    default String paymentMethodDesc(PaymentMethodEnum paymentMethod) {
        if (paymentMethod == null) {
            return null;
        }
        return switch (paymentMethod) {
            case ALIPAY -> "支付宝";
            case WECHAT -> "微信";
            default -> null;
        };
    }

    @Named("orderStatusDesc")
    default String orderStatusDesc(LeadsInfoPaymentOrderStatusEnum orderStatus) {
        return orderStatus == null ? null : orderStatus.getDesc();
    }
}
