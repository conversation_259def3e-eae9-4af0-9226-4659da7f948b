package com.yaowu.hera.utils.mapstruct.mtl;

import com.yaowu.hera.model.dto.mtl.LeadsPurchaseDTO;
import com.yaowu.hera.model.entity.leads.LeadsOrderInfo;
import com.yaowu.hera.model.entity.mtl.card.CardPackageOrder;
import com.yaowu.settle.api.model.mtl.dto.aggregation.RemoteSubmitPaymentDTO;
import com.yaowu.settle.api.model.mtl.dto.aggregation.ext.CardPackagePurchaseExtParam;
import com.yaowu.settle.api.model.mtl.dto.aggregation.ext.LeadsInfoAndCardPackagePurchaseExtParam;
import com.yaowu.settle.api.model.mtl.dto.aggregation.ext.LeadsInfoPurchaseExtParam;
import com.yaowu.settle.api.model.mtl.dto.coupon.RemoteCouponRedeemRequestDTO;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.MappingConstants;

/**
 * <AUTHOR>
 * @email <EMAIL>
 * @creatTime 2025.02.18 18:03:00
 * @description:
 */

@Mapper(componentModel  = MappingConstants.ComponentModel.SPRING)
public interface SettleMtlAccountBizServiceMapstruct {
	@Mapping(target = "storeId", source = "leadsOrderInfo.storeId")
	@Mapping(target = "paymentAmount", source = "purchaseDTO.paymentPrice")
	@Mapping(target = "prePaymentRecordId", source = "purchaseDTO.prePaymentRecordId")
	RemoteSubmitPaymentDTO toRemoteSubmitPaymentDTO(LeadsOrderInfo leadsOrderInfo,
	                                                LeadsPurchaseDTO purchaseDTO,
	                                                RemoteSubmitPaymentDTO.BusinessExtend businessExtend);

	@Mapping(target = "cardPackageId", source = "cardPackageOrder.cardPackageId")
	@Mapping(target = "cardPackageOrderId", source = "cardPackageOrder.id")
	@Mapping(target = "cardPackageOrderCode", source = "cardPackageOrder.cardPackageOrderCode")
	@Mapping(target = "storeId", source = "cardPackageOrder.storeId")
	@Mapping(target = "paymentAmount", source = "cardPackageOrder.paymentAmount")
	CardPackagePurchaseExtParam toCardPackagePurchaseExtParam(CardPackageOrder cardPackageOrder);

	@Mapping(target = "leadsInfoPurchaseExtParam", source = "leadsInfoPurchaseExtParam")
	@Mapping(target = "cardPackagePurchaseExtParam", source = "cardPackagePurchaseExtParam")
	LeadsInfoAndCardPackagePurchaseExtParam toLeadsInfoAndCardPackagePurchaseExtParam(LeadsInfoPurchaseExtParam leadsInfoPurchaseExtParam,
	                                                                                  CardPackagePurchaseExtParam cardPackagePurchaseExtParam);

	@Mapping(target = "couponPrice", source = "leadsOrderInfo.couponPrice")
	@Mapping(target = "couponDiscountAmount", source = "leadsOrderInfo.couponDiscountAmount")
	LeadsInfoPurchaseExtParam.LeadsInfoOrderParam toLeadsInfoOrderParam(LeadsOrderInfo leadsOrderInfo,Long leadsMerchantRelationId);

	@Mapping(target = "couponRedeemRequest", source = "redeemRequestDTO")
	@Mapping(target = "leadsOrderInfo", expression = "java(toLeadsInfoOrderParam(leadsOrderInfo,leadsMerchantRelationId))")
	LeadsInfoPurchaseExtParam toLeadsInfoPurchaseExtParam(LeadsOrderInfo leadsOrderInfo,
	                                                      LeadsPurchaseDTO purchaseDTO,
	                                                      RemoteCouponRedeemRequestDTO redeemRequestDTO,
	                                                      Long leadsMerchantRelationId);
}
