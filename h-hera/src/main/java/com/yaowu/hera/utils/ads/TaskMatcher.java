package com.yaowu.hera.utils.ads;


import com.yaowu.hera.model.entity.ads.AdsTask;
import com.yaowu.hera.model.entity.ads.CategoryInfoUsedScope;
import com.yaowu.hera.model.entity.ads.CategoryInfoUsedScope.CategoryInfo;
import com.yaowu.hera.model.entity.ads.CityInfoUsedScope;
import com.yaowu.hera.model.entity.ads.CityInfoUsedScope.CityInfo;
import com.yaowu.hera.utils.StreamUtil;
import com.yaowu.heraapi.model.dto.ads.RemoteAdsLoadReqDTO.AdsCategoryModel;
import com.yaowu.heraapi.model.pojo.common.AreaAddressModel;
import jodd.util.StringUtil;
import org.springframework.util.CollectionUtils;

import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;

import static com.yaowu.heraapi.enums.mtl.HeraCommonUsedScopeTypeEnum.NOT_LIMIT;

/**
 * 任务匹配工具类
 * <AUTHOR>
 */
public class TaskMatcher {

    /**
     * 根据地址匹配任务
     */
    public static List<AdsTask> matchByAddress(List<AdsTask> tasks, AreaAddressModel addressModel) {
        if (CollectionUtils.isEmpty(tasks)) {
            return Collections.emptyList();
        }
        if (Objects.isNull(addressModel)){
            return StreamUtil.of(tasks).filter(e->
                            Objects.nonNull(e.getTargetConfig()) &&
                                    Objects.nonNull(e.getTargetConfig().getCityInfoUsedScope()) &&
                                    e.getTargetConfig().getCityInfoUsedScope().getCityUsedScopeType() != NOT_LIMIT)
                    .collect(Collectors.toList());
        }

        return tasks.stream()
                .filter(task -> isAddressMatch(task, addressModel))
                .collect(Collectors.toList());
    }

    /**
     * 根据品类匹配任务
     */
    public static List<AdsTask> matchByCategory(List<AdsTask> tasks, List<AdsCategoryModel> categoryModels) {
        if (CollectionUtils.isEmpty(tasks)) {
            return Collections.emptyList();
        }
        if (CollectionUtils.isEmpty(categoryModels)){
            return StreamUtil.of(tasks).filter(e->
                            Objects.nonNull(e.getTargetConfig()) &&
                                    Objects.nonNull(e.getTargetConfig().getCategoryInfoUsedScope()) &&
                                    e.getTargetConfig().getCategoryInfoUsedScope().getCategoryUsedScopeType() != NOT_LIMIT)
                    .collect(Collectors.toList());
        }

        Set<Long> targetCategoryIds = categoryModels.stream()
                .map(AdsCategoryModel::getSecondCategoryIds)
                .flatMap(Set::stream)
                .collect(Collectors.toSet());

        return tasks.stream()
                .filter(task -> isCategoryMatch(task, targetCategoryIds))
                .collect(Collectors.toList());
    }

    /**
     * 判断任务是否匹配地址
     */
    private static boolean isAddressMatch(AdsTask task, AreaAddressModel addressModel) {
        if (task == null || task.getTargetConfig() == null
                || task.getTargetConfig().getCityInfoUsedScope() == null) {
            return false;
        }

        CityInfoUsedScope cityScope = task.getTargetConfig().getCityInfoUsedScope();

        // 不限制直接返回true
        if (cityScope.getCityUsedScopeType() == NOT_LIMIT) {
            return true;
        }

        // 检查城市编码是否匹配
        return !CollectionUtils.isEmpty(cityScope.getCityInfos())
                && cityScope.getCityInfos().stream()
                .map(CityInfo::getCityCodes)
                .flatMap(Set::stream)
                .anyMatch(cityCode -> StringUtil.equals(cityCode, addressModel.getCityCode()));
    }

    /**
     * 判断任务是否匹配品类
     */
    private static boolean isCategoryMatch(AdsTask task, Set<Long> targetCategoryIds) {
        if (task == null || task.getTargetConfig() == null
                || task.getTargetConfig().getCategoryInfoUsedScope() == null) {
            return false;
        }

        CategoryInfoUsedScope categoryScope = task.getTargetConfig().getCategoryInfoUsedScope();

        // 不限制直接返回true
        if (categoryScope.getCategoryUsedScopeType() == NOT_LIMIT) {
            return true;
        }

        // 检查是否有品类交集
        if (CollectionUtils.isEmpty(categoryScope.getCategoryInfos())) {
            return false;
        }

        Set<Long> taskCategoryIds = categoryScope.getCategoryInfos().stream()
                .map(CategoryInfo::getSecondCategoryIds)
                .flatMap(Set::stream)
                .collect(Collectors.toSet());

        return !Collections.disjoint(taskCategoryIds, targetCategoryIds);
    }
}