package com.yaowu.hera.utils;

import com.freedom.web.model.resp.BasePage;
import com.yaowu.hera.model.vo.clue.MelinaStorePageVO;
import com.yaowu.heraapi.enums.mtl.LeadsStoreRecommendTypeEnum;
import lombok.extern.slf4j.Slf4j;
import org.springframework.util.CollectionUtils;

import java.math.BigDecimal;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.yaowu.hera.utils.CollectUtil.getPreIndexAndMoveElementToTargetIndex;

@Slf4j
public class LeadsMerchantRecommendSortUtil {

    /**
     * 推荐商户-无地址排序
     *
     * @param remoteMelinaStorePageVOS
     * @param storeRecommendType
     * @param seedMerchantToPosition
     */
    public static void sortForNoAddress(List<MelinaStorePageVO> remoteMelinaStorePageVOS, BigDecimal smartRecommendDistance, Integer storeRecommendType, Map<Long, Integer> seedMerchantToPosition) {
        //默认排序
        remoteMelinaStorePageVOS.sort(Comparator
                .comparing(MelinaStorePageVO::getSettleInSort)
                .thenComparing(MelinaStorePageVO::getAllCount).reversed()
                .thenComparing(MelinaStorePageVO::getId));

        LeadsStoreRecommendTypeEnum leadsStoreRecommendTypeEnum = LeadsStoreRecommendTypeEnum.codeOfNullable(storeRecommendType);
        if (leadsStoreRecommendTypeEnum == null || LeadsStoreRecommendTypeEnum.DEFAULT_RECOMMEND == leadsStoreRecommendTypeEnum) {
            return;
        }
        seedMerchantSort(seedMerchantToPosition, remoteMelinaStorePageVOS);
        //smartSort(smartRecommendDistance, remoteMelinaStorePageVOS);
    }

    /**
     * 推荐商户-有地址排序
     *
     * @param remoteMelinaStorePageVOBasePage
     * @param seedMerchantIdToPosition
     */
    public static void sortForHasAddress(BasePage<MelinaStorePageVO> remoteMelinaStorePageVOBasePage, BigDecimal smartRecommendDistance,
                                         Integer storeRecommendType, Map<Long, Integer> seedMerchantIdToPosition) {

        remoteMelinaStorePageVOBasePage.getRecords().sort(Comparator
                .comparing(MelinaStorePageVO::getDistance)
                .thenComparing(MelinaStorePageVO::getSettleInSort)
                .thenComparing(MelinaStorePageVO::getId));

        LeadsStoreRecommendTypeEnum leadsStoreRecommendTypeEnum = LeadsStoreRecommendTypeEnum.codeOfNullable(storeRecommendType);
        if (leadsStoreRecommendTypeEnum == null || LeadsStoreRecommendTypeEnum.DEFAULT_RECOMMEND == leadsStoreRecommendTypeEnum) {
            return;
        }
        smartSort(smartRecommendDistance, remoteMelinaStorePageVOBasePage.getRecords());
        seedMerchantSort(seedMerchantIdToPosition, remoteMelinaStorePageVOBasePage.getRecords());
    }

    /**
     * 种子商户排序
     */
    private static void seedMerchantSort(Map<Long, Integer> seedMerchantIdToPosition, List<MelinaStorePageVO> recommendMerchantList) {
        if (CollectionUtils.isEmpty(seedMerchantIdToPosition) || CollectionUtils.isEmpty(recommendMerchantList)) {
            return;
        }
        Map<Long, MelinaStorePageVO> idToStoreVO = StreamUtil.of(recommendMerchantList)
                .collect(Collectors.toMap(MelinaStorePageVO::getId, Function.identity()));
        LinkedHashMap<Long, Integer> sortedLinkedHashMap = seedMerchantIdToPosition.entrySet()
                .stream()
                .sorted(Map.Entry.comparingByValue())
                .collect(Collectors.toMap(Map.Entry::getKey, Map.Entry::getValue, (e1, e2) -> e1, LinkedHashMap::new));

        //推荐商户列表插入资源位排序去重
        sortedLinkedHashMap.forEach((storeId, position) -> {
            MelinaStorePageVO melinaStorePageVO = idToStoreVO.get(storeId);
            if (position > 0 && position <= recommendMerchantList.size() && Objects.nonNull(melinaStorePageVO)) {
                try {
                    int preIndexAndMoveElementToTargetIndex = getPreIndexAndMoveElementToTargetIndex(recommendMerchantList, melinaStorePageVO, position - 1);
                    melinaStorePageVO.setPreMarketingStoreSort(preIndexAndMoveElementToTargetIndex + 1 );
                } catch (IndexOutOfBoundsException e) {
                    log.error("资源位置不合法,当前位置:{},当前商户id:{},当前商户名称:{}", position, melinaStorePageVO.getId(), melinaStorePageVO.getName());
                }
            } else if (position > 0 && Objects.nonNull(melinaStorePageVO)) {
                int preIndexAndMoveElementToTargetIndex = getPreIndexAndMoveElementToTargetIndex(recommendMerchantList, melinaStorePageVO, recommendMerchantList.size());
                melinaStorePageVO.setPreMarketingStoreSort(preIndexAndMoveElementToTargetIndex + 1);
            }
        });
    }

    private static void smartSort(BigDecimal smartRecommendDistance, List<MelinaStorePageVO> records) {
        if (smartRecommendDistance == null || smartRecommendDistance.compareTo(BigDecimal.ZERO) == 0) {
            return;
        }
        BigDecimal smartRecommendDistanceMeter = smartRecommendDistance.multiply(BigDecimal.valueOf(1000));
        //为空就不过滤了 , 在指定公里数以内
        records.sort((store1, store2) -> {
            if (store1.getDistance() == null && store2.getDistance() == null) {
                return 0;
            }
            // 距离都在 20 公里以内
            if ((store1.getDistance() == null ? BigDecimal.valueOf(9999999) : store1.getDistance()).compareTo(smartRecommendDistanceMeter) <= 0
                    && (store2.getDistance() == null ? BigDecimal.valueOf(9999999) : store2.getDistance()).compareTo(smartRecommendDistanceMeter) <= 0) {
                // 按照入驻状态排序
                return Integer.compare(store1.getSettleInSort(), store2.getSettleInSort());
            } else {
                // 其他情况保持原来的排序
                return 0;
            }
        });
    }
}
