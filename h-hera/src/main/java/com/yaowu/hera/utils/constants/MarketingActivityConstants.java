package com.yaowu.hera.utils.constants;

import lombok.AccessLevel;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;
import java.time.LocalTime;

/**
 * <AUTHOR>
 */
@NoArgsConstructor(access = AccessLevel.PRIVATE)
public class MarketingActivityConstants {

    public static final Integer DEFAULT_ACTIVITY_PRIORITY = 0;

    public static final Integer PRESET_ACTIVITY_FLAG = 1;

    // -- 活动规则配置校验

    private static final long DEFAULT_START_TIME_DIFF_MINUTES = 2L;

    private static final long DEFAULT_END_TIME_DIFF_DAYS = 366L;

    private static final Integer MAX_COMMISSION_SHARING_DURATION = 366;

    private static final Integer MIN_COMMISSION_PERCENTAGE = 0;

    private static final Integer MAX_COMMISSION_PERCENTAGE = 20;

    public static boolean isActivityStartTimeValid(LocalDateTime activityStartTime) {
        return activityStartTime != null &&
                activityStartTime.isAfter(LocalDateTime.now().minusMinutes(DEFAULT_START_TIME_DIFF_MINUTES));
    }

    public static boolean isActivityEndTimeValid(LocalDateTime activityEndTime) {
        return activityEndTime != null &&
                activityEndTime.isBefore(LocalDateTime.now().with(LocalTime.MAX).plusDays(DEFAULT_END_TIME_DIFF_DAYS));
    }

    public static boolean isCommissionDurationInRange(Integer duration) {
        return duration >= 1 && duration <= MAX_COMMISSION_SHARING_DURATION;
    }

    public static boolean isCommissionPercentageInRange(Integer commission) {
        return commission >= MIN_COMMISSION_PERCENTAGE && commission <= MAX_COMMISSION_PERCENTAGE;
    }

}
