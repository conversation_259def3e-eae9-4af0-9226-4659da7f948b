package com.yaowu.hera.utils.mapstruct.log;

import com.yaowu.hera.model.entity.business.ClueConvertTraceLog;
import com.yaowu.heraapi.enums.business.ClueConvertStatusEnum;
import com.yaowu.heraapi.model.dto.business.CreateClueConvertLogDTO;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.factory.Mappers;

import java.util.Objects;

/**
 * <AUTHOR>
 */
@Mapper(componentModel = "spring", imports = {ClueConvertStatusEnum.class, Objects.class})
public interface ClueConvertLogMapStruct {

    ClueConvertLogMapStruct INSTANCE = Mappers.getMapper(ClueConvertLogMapStruct.class);

    @Mapping(target = "sourceStatus",expression = "java(dto.getSourceStatus().getCode())")
    @Mapping(target = "targetStatus",expression = "java(dto.getTargetStatus().getCode())")
    ClueConvertTraceLog toClueConvertLog(CreateClueConvertLogDTO dto);
}
