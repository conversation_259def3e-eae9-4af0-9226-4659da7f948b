package com.yaowu.hera.utils.mapstruct.audio;

import cn.hutool.core.collection.CollectionUtil;
import com.yaowu.hera.model.bo.audio.QuotationRelatedInfoBO;
import com.yaowu.hera.model.bo.leads.LeadsChannelExtBO;
import com.yaowu.hera.model.entity.audio.AudioStructuredData;
import com.yaowu.hera.model.entity.audio.TranscribedBizTraceData;
import com.yaowu.hera.model.entity.leads.LeadsDeviceCategory;
import com.yaowu.hera.model.entity.leads.LeadsInfo;
import com.yaowu.hera.model.entity.price.localrentprice.QuotationRentalConfig;
import com.yaowu.hera.model.entity.put.PutUnit;
import com.yaowu.hera.model.vo.audio.QuotationRelatedInfoExportVO;
import com.yaowu.hera.model.vo.clue.MelinaStorePageVO;
import com.yaowu.hera.utils.StreamUtil;
import com.yaowu.hera.utils.StringUtil;
import com.yaowu.heraapi.enums.mtl.LeadsCountTypeEnum;
import com.yaowu.heraapi.enums.mtl.LeadsDurationTypeEnum;
import com.yaowu.heraapi.model.pojo.business.HeraLeadBizParamExt;
import com.yaowu.heraapi.model.pojo.business.HeraLeadBizParamExt.DeviceParam;
import com.yaowu.heraapi.model.pojo.common.AreaAddressModel;
import com.yaowu.heraapi.model.vo.mtl.leadsinfo.RemoteLeadsInfoPageVO;
import com.yaowu.heraapi.model.vo.mtl.pricing.RemoteTranscribedDataVO;
import com.yaowu.heraapi.utils.LeadsInfoUtils;
import com.yaowu.passportapi.model.vo.user.UserInfoVO;
import org.apache.commons.lang3.StringUtils;

import java.time.format.DateTimeFormatter;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;

import static com.yaowu.hera.utils.constants.Constants.DATE_TIME_FORMAT;
import static com.yaowu.hera.utils.constants.Constants.METER;
import static com.yaowu.hera.utils.constants.Constants.TONE;
import static com.yaowu.hera.utils.constants.MtlLeadsConstants.QUANTITY;
import static com.yaowu.hera.utils.constants.MtlLeadsConstants.USAGE_DURATION;

/**
 * <AUTHOR>
 */
public class TranscribedBizTraceDataConverter {
    public static void fillCallRecordTextInfo(AudioStructuredData structuredData,
                                              QuotationRelatedInfoExportVO exportDataVO,
                                              MelinaStorePageVO melinaStorePageVO) {
        exportDataVO.setAudioDeviceUsage(structuredData.getDeviceUsage());
        exportDataVO.setAudioScene(structuredData.getScene());
        exportDataVO.setAudioDeviceCategory(getAudioDeviceCategoryDesc(structuredData));
        exportDataVO.setAudioDeviceCategorySpecialRequirements(structuredData.getDeviceCategorySpecialRequirements());
        exportDataVO.setAudioDeviceQuantity(structuredData.getDeviceQuantity());
        exportDataVO.setAudioCarRentalTime(structuredData.getCarRentalTime());
        exportDataVO.setAudioCarRentalCycle(structuredData.getCarRentalCycle());
        exportDataVO.setAudioMerchantQuote(structuredData.getMerchantQuote());
        exportDataVO.setAudioCustomerExpectedPrice(structuredData.getCustomerExpectedPrice());
        exportDataVO.setAudioExchangeContactInformation(structuredData.getExchangeContactInformation());
        exportDataVO.setAudioDealPrediction(structuredData.getDealPrediction());
        exportDataVO.setAudioFailurePrediction(structuredData.getDefeatReasonPrediction());
        exportDataVO.setAudioTonnage(structuredData.getAudioTonnage());
        exportDataVO.setAudioMeters(structuredData.getAudioMeters());
        exportDataVO.setAudioWatts(structuredData.getAudioWatts());
        exportDataVO.setDelDailyRentalPrice(structuredData.getDelDailyRentalPrice());
        exportDataVO.setDelMonthlyRentalPrice(structuredData.getDelMonthlyRentalPrice());
        exportDataVO.setPredictedGmv(structuredData.getPredictedGmv());
        exportDataVO.setOtherCost(structuredData.getRentalNote());
        //成交预测信息
        processDealPredictionInfo(structuredData.getDealPrediction(),exportDataVO,melinaStorePageVO);
    }

    /**
     * 处理报价单线索的成交信息
     */
    private static void processDealPredictionInfo(String dealPrediction, QuotationRelatedInfoExportVO exportDataVO,
                                                  MelinaStorePageVO melinaStorePageVO) {
        if (StringUtils.isBlank(dealPrediction)) {
            return;
        }
        try {
            Integer predictionValue = Integer.parseInt(dealPrediction);
            //如果当前话单的成交预测为成交，则填充成交商户的信息（商户电话、商户名称）
            if (predictionValue == 3) {
                exportDataVO.setDealMerchantName(melinaStorePageVO.getMerchantName());
                exportDataVO.setDealMerchantPhone(melinaStorePageVO.getMerchantPhone());
            }
        } catch (Exception e) {
            return;
        }

    }

    public static String getAudioDeviceCategoryDesc(AudioStructuredData structuredData){
        if (Objects.isNull(structuredData)){
           return "";
        }
        return structuredData.getDeviceFirstCategoryName() + structuredData.getDeviceSecondCategoryName()+structuredData.getDeviceSpuName();
    }

    public static QuotationRelatedInfoExportVO fillQuotationRelatedInfoExportVO(LeadsInfo leadsInfo,
                                                                                QuotationRelatedInfoBO quoteRelInfoBO,
                                                                                String callRecordType,
                                                                                MelinaStorePageVO melinaStorePageVO) {
        QuotationRelatedInfoExportVO exportDataVO = new QuotationRelatedInfoExportVO();
        //填充基础信息
        fillBaseLeadsInfo(leadsInfo, quoteRelInfoBO, callRecordType, melinaStorePageVO, exportDataVO);
        //填充用车地址信息
        fillCarUseAddressInfo(leadsInfo, quoteRelInfoBO.getLeadsInfoAreaAddressMap(), exportDataVO);
        //填充报价单线索时间信息
        fillLeadsInfoTimeInfo(leadsInfo, exportDataVO);
        //填充报价单线索渠道信息
        fillLeadsInfoChannelInfo(leadsInfo,quoteRelInfoBO.getPutUnitList(), exportDataVO);
        return exportDataVO;
    }

    private static void fillBaseLeadsInfo(LeadsInfo leadsInfo, QuotationRelatedInfoBO quoteRelInfoBO, String callRecordType,
                                          MelinaStorePageVO melinaStorePageVO, QuotationRelatedInfoExportVO exportDataVO) {
        exportDataVO.setAudioType(callRecordType);
        exportDataVO.setLeadsInfoId(leadsInfo.getId());
        exportDataVO.setDeviceCategoryOne(leadsInfo.getFirstCategoryName());
        //查询设备信息
        Map<Long, LeadsDeviceCategory> leadsCategoryInfo = quoteRelInfoBO.getLeadsCategoryInfo();
        Optional.ofNullable(leadsCategoryInfo.get(leadsInfo.getSecondCategoryId())).ifPresent(deviceCategory->{
            exportDataVO.setDeviceCategoryTwo(deviceCategory.getCategoryName());
        });
        //租赁设备数量
        exportDataVO.setDeviceQuantity(getRequirementCount(leadsInfo));
        if (Objects.nonNull(melinaStorePageVO)) {
            exportDataVO.setMerchantName(melinaStorePageVO.getMerchantName());
            exportDataVO.setMerchantPhone(melinaStorePageVO.getMerchantPhone());
        }
        //填充用户手机号信息
        Map<Long, UserInfoVO> bizUserIdToPhone = quoteRelInfoBO.getBizUserIdToPhone();
        if (CollectionUtil.isNotEmpty(bizUserIdToPhone) && bizUserIdToPhone.containsKey(leadsInfo.getCustomerUserId())) {
            exportDataVO.setCustomerPhone(bizUserIdToPhone.get(leadsInfo.getCustomerUserId()).getPhone());
        }
    }

    private static void fillLeadsInfoTimeInfo(LeadsInfo leadsInfo, QuotationRelatedInfoExportVO exportDataVO) {
        if (Objects.isNull(leadsInfo) || Objects.isNull(exportDataVO)) {
            return;
        }
        Optional.ofNullable(leadsInfo.getCreateTime())
                .ifPresent(dateTime -> exportDataVO.setClueCreateTime(dateTime.format(DateTimeFormatter.ofPattern(DATE_TIME_FORMAT))));
        Optional.ofNullable(leadsInfo.getUpdateTime())
                .ifPresent(dateTime -> exportDataVO.setClueUpdateTime(dateTime.format(DateTimeFormatter.ofPattern(DATE_TIME_FORMAT))));
        //填充预计进场时间(租赁开始时间)
        exportDataVO.setExpectedArrivalTime(getCarRentalTime(leadsInfo));
        //填充使用时长(租赁周期)
        exportDataVO.setDeviceUseDuration(getUsageDuration(leadsInfo));
    }

    /**
     * 填充报价单线索渠道信息
     */
    private static void fillLeadsInfoChannelInfo(LeadsInfo leadsInfo,
                                                 List<PutUnit> putUnitList,
                                                 QuotationRelatedInfoExportVO exportDataVO) {
        // 渠道信息
        exportDataVO.setClueChannel(leadsInfo.getChannelTier1());
        LeadsChannelExtBO channelExt = leadsInfo.getChannelExt();

        if (CollectionUtil.isEmpty(putUnitList) || Objects.isNull(channelExt)) {
            return;
        }
        List<PutUnit> targetUnitList = filterUnitsByArgs(putUnitList, channelExt.getArg1(),
                "date", channelExt.getArg2(), "category1", channelExt.getArg3(), "category2");
        if (CollectionUtil.isEmpty(targetUnitList)) {
            return;
        }

        if (Objects.nonNull(targetUnitList.get(0).getClassificationJson())){
            // 二级渠道信息
            exportDataVO.setChannelTwo(targetUnitList.get(0).getClassificationJson().getSecondClassification());
        }
        // 三级渠道信息
        exportDataVO.setChannelThree(targetUnitList.get(0).getPutUnitName());
    }

    private static List<PutUnit> filterUnitsByArgs(List<PutUnit> putUnitList,
                                                   String arg1, String key1,
                                                   String arg2, String key2,
                                                   String arg3, String key3) {
        return StreamUtil.of(putUnitList)
                .filter(e -> {
                    boolean matchesArg1 = StringUtil.isBlank(arg1) ||
                            StreamUtil.of(e.getPutChannelJson())
                                    .anyMatch(f -> key1.equals(f.getChannelKey()) && arg1.equals(f.getChannelValue()));

                    boolean matchesArg2 = StringUtil.isBlank(arg2) ||
                            StreamUtil.of(e.getPutChannelJson())
                                    .anyMatch(f -> key2.equals(f.getChannelKey()) && arg2.equals(f.getChannelValue()));

                    boolean matchesArg3 = StringUtil.isBlank(arg3) ||
                            StreamUtil.of(e.getPutChannelJson())
                                    .anyMatch(f -> key3.equals(f.getChannelKey()) && arg3.equals(f.getChannelValue()));

                    return matchesArg1 && matchesArg2 && matchesArg3;
                })
                .collect(Collectors.toList());
    }

    private static String getRequirementCount(LeadsInfo source) {
        if (source == null) {
            return "";
        }
        if (Objects.nonNull(source.getCountType()) && LeadsCountTypeEnum.CUSTOM_INPUT == source.getCountType()) {
            List<DeviceParam> deviceParams = Optional.ofNullable(source.getBizExt())
                    .map(HeraLeadBizParamExt::getDeviceParams).orElse(Collections.emptyList());
            if (CollectionUtil.isEmpty(deviceParams)) {
                return "";
            }
            DeviceParam deviceParam = StreamUtil.of(deviceParams)
                    .filter(e -> QUANTITY.equals(e.getName()))
                    .findFirst().orElse(null);
            if (Objects.isNull(deviceParam) || Objects.isNull(deviceParam.getValue())) {
                return "";
            }
            String deviceParamValue = deviceParam.getValue().toString();
            if (StringUtil.isBlank(deviceParamValue)) {
                return "";
            }
            return deviceParam.getValue() + deviceParam.getUnit();
        }
        return Objects.nonNull(source.getCountType()) ? source.getCountType().getDesc() : "";
    }

    private static void fillCarUseAddressInfo(LeadsInfo leadsInfo,
                                              Map<Long, AreaAddressModel> leadsInfoAreaAddressMap,
                                              QuotationRelatedInfoExportVO exportDataVO) {
        //填充用车地址信息-省
        exportDataVO.setCarUseAddressProvince(getCarUseAddressProvinceStr(leadsInfo, leadsInfoAreaAddressMap));
        //填充用车地址信息-市
        exportDataVO.setCarUseAddressCity(getCarUseAddressCityStr(leadsInfo, leadsInfoAreaAddressMap));
        //填充用车地址信息-区
        exportDataVO.setCarUseAddressDistract(getCarUseAddressDistractStr(leadsInfo, leadsInfoAreaAddressMap));
        //填充用车地址信息-详细地址信息
        exportDataVO.setCarUseAddressDetail(getCarUseAddressDetailStr(leadsInfo,leadsInfoAreaAddressMap));
    }

    private static String getCarRentalTime(LeadsInfo source) {
        if (source == null) {
            return "";
        }
        return LeadsInfoUtils.buildCustomDateDesc(source.getStartEnterType(), source.getCustomStartDate(), source.getCustomEndDate());
    }

    /**
     * 获取预计租赁时长
     */
    private static String getUsageDuration(LeadsInfo source) {
        if (source == null) {
            return "";
        }
        if (Objects.nonNull(source.getDurationType()) && LeadsDurationTypeEnum.CUSTOM_INPUT == source.getDurationType()) {
            List<DeviceParam> deviceParams = Optional.ofNullable(source.getBizExt())
                    .map(HeraLeadBizParamExt::getDeviceParams).orElse(Collections.emptyList());
            if (CollectionUtil.isEmpty(deviceParams)) {
                return "";
            }
            DeviceParam deviceParam = StreamUtil.of(deviceParams)
                    .filter(e -> USAGE_DURATION.equals(e.getName()))
                    .findFirst().orElse(null);
            if (Objects.isNull(deviceParam) || Objects.isNull(deviceParam.getValue())) {
                return "";
            }
            String deviceParamValue = deviceParam.getValue().toString();
            if (StringUtil.isBlank(deviceParamValue)) {
                return "";
            }
            return deviceParam.getValue() + deviceParam.getUnit();
        }
        return Objects.nonNull(source.getDurationType()) ? source.getDurationType().getDesc() : "";
    }

    private static String getCarUseAddressProvinceStr(LeadsInfo leadsInfo, Map<Long, AreaAddressModel> leadsInfoAreaAddressMap) {
        //填充用车地址信息
        if (CollectionUtil.isEmpty(leadsInfoAreaAddressMap) || !leadsInfoAreaAddressMap.containsKey(leadsInfo.getId())) {
            return "";
        }
        AreaAddressModel areaAddressModel = leadsInfoAreaAddressMap.get(leadsInfo.getId());
        return areaAddressModel.getProvinceName();
    }

    private static String getCarUseAddressCityStr(LeadsInfo leadsInfo, Map<Long, AreaAddressModel> leadsInfoAreaAddressMap) {
        //填充用车地址信息
        if (CollectionUtil.isEmpty(leadsInfoAreaAddressMap) || !leadsInfoAreaAddressMap.containsKey(leadsInfo.getId())) {
            return "";
        }
        AreaAddressModel areaAddressModel = leadsInfoAreaAddressMap.get(leadsInfo.getId());
        return areaAddressModel.getCityName();
    }



    private static String getCarUseAddressDistractStr(LeadsInfo leadsInfo, Map<Long, AreaAddressModel> leadsInfoAreaAddressMap) {
        //填充用车地址信息
        if (CollectionUtil.isEmpty(leadsInfoAreaAddressMap) || !leadsInfoAreaAddressMap.containsKey(leadsInfo.getId())) {
            return "";
        }
        AreaAddressModel areaAddressModel = leadsInfoAreaAddressMap.get(leadsInfo.getId());
        return areaAddressModel.getDistrictName();
    }


    private static String getCarUseAddressDetailStr(LeadsInfo leadsInfo, Map<Long, AreaAddressModel> leadsInfoAreaAddressMap) {
        //填充用车地址信息
        if (CollectionUtil.isEmpty(leadsInfoAreaAddressMap) || !leadsInfoAreaAddressMap.containsKey(leadsInfo.getId())) {
            return "";
        }
        AreaAddressModel areaAddressModel = leadsInfoAreaAddressMap.get(leadsInfo.getId());

        String baseAddress = areaAddressModel.getAddress();
        if (StringUtil.isNotBlank(areaAddressModel.getAddressTitle())) {
            baseAddress = baseAddress + "(" + areaAddressModel.getAddressTitle() + ")";
        }
        return baseAddress;
    }


    /**
     * 计算deviceName 和quotationRentalConfigs 中名称最接近的spu信息
     */
    public static QuotationRentalConfig getClosestMatch(List<QuotationRentalConfig> configs, String deviceName) {
        QuotationRentalConfig bestMatch = null;
        if (CollectionUtil.isEmpty(configs) || StringUtil.isBlank(deviceName)){
           return bestMatch;
        }
        double highestSimilarity = 0.0;
        //遍历配置spu,获取名称最接近的spu信息
        for (QuotationRentalConfig config : configs) {
            double similarity = StringUtil.calculateSimilarity(deviceName,config.getSpuName());
            if (similarity > highestSimilarity) {
                highestSimilarity = similarity;
                bestMatch = config;
            }
        }
        return bestMatch;
    }


    public static RemoteTranscribedDataVO convertTranscribedBizTraceDataVO(TranscribedBizTraceData transData,
                                                                           RemoteLeadsInfoPageVO leadsInfoVO,
                                                                           List<QuotationRentalConfig> deviceConfigs) {
        if (Objects.isNull(leadsInfoVO)) {
            return null;
        }

        RemoteTranscribedDataVO remoteTranscribedDataVO = new RemoteTranscribedDataVO();
        fillBasicLeadsInfo(remoteTranscribedDataVO, leadsInfoVO);
        fillMergedStructuredData(remoteTranscribedDataVO, transData, deviceConfigs);

        return remoteTranscribedDataVO;
    }

    private static void fillBasicLeadsInfo(RemoteTranscribedDataVO remoteTranscribedDataVO, RemoteLeadsInfoPageVO leadsInfoVO) {
        remoteTranscribedDataVO.setId(leadsInfoVO.getId());
        Optional.ofNullable(leadsInfoVO.getSubmitTime()).ifPresent(e -> remoteTranscribedDataVO.setSubmitTime(e.toLocalDate()));
        Optional.ofNullable(leadsInfoVO.getAddress()).ifPresent(e -> {
            remoteTranscribedDataVO.setCityCode(e.getCityCode());
            remoteTranscribedDataVO.setCityName(e.getCityName());
        });
    }

    private static void fillMergedStructuredData(RemoteTranscribedDataVO remoteTranscribedDataVO,
                                                 TranscribedBizTraceData transData,
                                                 List<QuotationRentalConfig> deviceConfigs) {
        AudioStructuredData mergedData = Optional.ofNullable(transData).map(TranscribedBizTraceData::getMergedStructuredData).orElse(null);
        if (Objects.isNull(mergedData)|| isValidQuoteInfo(mergedData) || isValidDeviceUnitInfo(mergedData) || CollectionUtil.isEmpty(deviceConfigs)) {
            return;
        }

        String targetDeviceName = buildTargetDeviceName(mergedData);
        QuotationRentalConfig closestMatch = getClosestMatch(deviceConfigs, targetDeviceName);
        if (Objects.isNull(closestMatch)) {
            return;
        }

        remoteTranscribedDataVO.setFirstCategoryId(closestMatch.getFirstCategoryId());
        remoteTranscribedDataVO.setSecondCategoryId(closestMatch.getSecondCategoryId());
        remoteTranscribedDataVO.setSpuId(closestMatch.getSpuId());
        remoteTranscribedDataVO.setDelDailyPrice(convertToInteger(mergedData.getDelDailyRentalPrice()));
        remoteTranscribedDataVO.setDelMonthlyPrice(convertToInteger(mergedData.getDelMonthlyRentalPrice()));
    }

    private static boolean isValidQuoteInfo(AudioStructuredData mergedData) {
        String delDailyPrice = mergedData.getDelDailyRentalPrice();
        String delMonthlyPrice = mergedData.getDelMonthlyRentalPrice();
        return (StringUtil.isBlank(delDailyPrice) && StringUtil.isBlank(delMonthlyPrice)) ||
                !(isRightFormData(delDailyPrice) || isRightFormData(delMonthlyPrice));
    }

    private static boolean isValidDeviceUnitInfo(AudioStructuredData mergedData) {
        String audioMeters = mergedData.getAudioMeters();
        String audioWatts = mergedData.getAudioTonnage();
        return (StringUtil.isBlank(audioMeters) && StringUtil.isBlank(audioWatts));
    }

    private static String buildTargetDeviceName(AudioStructuredData mergedData) {
        String secondCategoryName = mergedData.getDeviceSecondCategoryName();
        String deviceSpuName = mergedData.getDeviceSpuName();
        String targetDeviceName =  secondCategoryName + deviceSpuName;

        if (StringUtil.isNotBlank(mergedData.getAudioMeters())) {
            targetDeviceName = mergedData.getAudioMeters() +TONE+ targetDeviceName;
        }
        if (StringUtil.isNotBlank(mergedData.getAudioTonnage())) {
            targetDeviceName = mergedData.getAudioTonnage() +METER + targetDeviceName;
        }

        return targetDeviceName;
    }


    private static Integer convertToInteger(String delDailyRentalPrice) {
        if (StringUtil.isBlank(delDailyRentalPrice)) {
            return 0;
        }
        return Integer.valueOf(delDailyRentalPrice);
    }


    private static boolean isRightFormData(String data) {
        // 检查是否为空字符串
        if (data.isEmpty()) {
            return true;
        }
        // 检查是否全为数字
        return data.matches("\\d*");
    }
}
