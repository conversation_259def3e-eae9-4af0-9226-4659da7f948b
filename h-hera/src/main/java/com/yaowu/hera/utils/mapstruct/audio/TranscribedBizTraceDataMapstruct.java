package com.yaowu.hera.utils.mapstruct.audio;

import cn.hutool.core.collection.CollectionUtil;
import com.yaowu.hera.enums.audio.TranscribedBizTypeEnum;
import com.yaowu.hera.model.bo.audio.LlmBaseResultBO;
import com.yaowu.hera.model.bo.audio.TranscribedBizTraceDataBO;
import com.yaowu.hera.model.entity.audio.AudioStructuredData;
import com.yaowu.hera.model.entity.audio.PricingToolProcessData;
import com.yaowu.hera.model.entity.audio.TranscribedBizTraceData;
import com.yaowu.hera.utils.StreamUtil;
import com.yaowu.hera.utils.StringUtil;
import com.yaowu.heraapi.model.pojo.common.AreaAddressModel;
import com.yaowu.heraapi.model.vo.mtl.leadsinfo.RemoteLeadsInfoPageVO;
import com.yaowu.heraapi.model.vo.mtl.pricing.RemoteTranscribedDataVO;
import com.yaowu.heraapi.model.vo.audio.filed.LlmDeviceTimesVO;
import com.yaowu.heraapi.model.vo.audio.filed.LlmDeviceUsageVO;
import com.yaowu.heraapi.model.vo.audio.filed.LlmDeviceVO;
import com.yaowu.heraapi.model.vo.audio.filed.LlmPredictVO;
import com.yaowu.heraapi.model.vo.audio.filed.LlmPriceVO;
import com.yaowu.notice.model.vo.RemotePrivateNumberCallRecodeVO;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.MappingConstants;
import org.mapstruct.factory.Mappers;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.function.Function;
import java.util.stream.Collectors;


/**
 * <AUTHOR>
 */
@Mapper(componentModel = MappingConstants.ComponentModel.SPRING,
        imports = {Optional.class, LlmDeviceVO.class, LlmDeviceTimesVO.class, LlmPriceVO.class, LlmDeviceUsageVO.class, LlmPredictVO.class}
)
public interface TranscribedBizTraceDataMapstruct {

    TranscribedBizTraceDataMapstruct INSTANCE = Mappers.getMapper(TranscribedBizTraceDataMapstruct.class);

    @Mapping(target = "recordStartTime", expression = "java(getRecordStartTime(transcribedBizTraceData,idToCallRecodeVO))")
    TranscribedBizTraceDataBO toTranscribedBizTraceDataBO(TranscribedBizTraceData transcribedBizTraceData,
                                                          Map<Long, RemotePrivateNumberCallRecodeVO> idToCallRecodeVO);

    default LocalDateTime getRecordStartTime(TranscribedBizTraceData transcribedBizTraceData,
                                             Map<Long, RemotePrivateNumberCallRecodeVO> idToCallRecodeVO) {
        if (Objects.isNull(transcribedBizTraceData) || Objects.isNull(idToCallRecodeVO)) {
            return null;
        }
        return Optional.ofNullable(idToCallRecodeVO.get(transcribedBizTraceData.getId()))
                .map(RemotePrivateNumberCallRecodeVO::getRecordStartTime).orElse(null);
    }

    default List<TranscribedBizTraceDataBO> toTranscribedBizTraceDataBOList(List<TranscribedBizTraceData> transcribedBizTraceDataList,
                                                                            Map<Long, RemotePrivateNumberCallRecodeVO> idToCallRecodeVO) {
        if (CollectionUtil.isEmpty(transcribedBizTraceDataList)) {
            return Collections.emptyList();
        }
        return StreamUtil.of(transcribedBizTraceDataList).map(data -> toTranscribedBizTraceDataBO(data, idToCallRecodeVO))
                .collect(Collectors.toList());
    }

    @Mapping(target = "bizType", expression = "java(transcribedBizTypeEnum == null ? 0 : transcribedBizTypeEnum.getCode())")
    @Mapping(target = "bizId", source = "bizId")
    @Mapping(target = "mergedStructuredData", source = "audioStructuredData")
    TranscribedBizTraceData toTranscribedBizTraceData(Long bizId,
                                                      TranscribedBizTypeEnum transcribedBizTypeEnum,
                                                      AudioStructuredData audioStructuredData);

    @Mapping(target = "deviceFirstCategoryName", expression = "java(getDeviceField(llmBaseResultBO, LlmDeviceVO::getDeviceFirstCategoryName))")
    @Mapping(target = "deviceSecondCategoryName", expression = "java(getDeviceField(llmBaseResultBO, LlmDeviceVO::getDeviceSecondCategoryName))")
    @Mapping(target = "deviceSpuName", expression = "java(getDeviceField(llmBaseResultBO, LlmDeviceVO::getDeviceSpuName))")
    @Mapping(target = "deviceCategorySpecialRequirements", expression = "java(getDeviceField(llmBaseResultBO, LlmDeviceVO::getDeviceCategorySpecialRequirements))")
    @Mapping(target = "deviceQuantity", expression = "java(getDeviceField(llmBaseResultBO, LlmDeviceVO::getDeviceQuantity))")
    @Mapping(target = "carRentalTime", expression = "java(getDeviceTimesField(llmBaseResultBO, LlmDeviceTimesVO::getCarRentalTime))")
    @Mapping(target = "carRentalCycle", expression = "java(getDeviceTimesField(llmBaseResultBO, LlmDeviceTimesVO::getCarRentalCycle))")
    @Mapping(target = "rentalNote", expression = "java(getPriceField(llmBaseResultBO, LlmPriceVO::getRentalNote))")
    @Mapping(target = "deviceUsage", expression = "java(getDeviceUsagesField(llmBaseResultBO, LlmDeviceUsageVO::getDeviceUsage))")
    @Mapping(target = "scene", expression = "java(getDeviceUsagesField(llmBaseResultBO, LlmDeviceUsageVO::getScene))")
    @Mapping(target = "carUseAddress", expression = "java(getDeviceUsagesField(llmBaseResultBO, LlmDeviceUsageVO::getCarUseAddress))")
    @Mapping(target = "exchangeContactInformation", expression = "java(getDeviceUsagesField(llmBaseResultBO, LlmDeviceUsageVO::getExchangeContactInformation))")
    @Mapping(target = "dealPrediction", expression = "java(getPredictField(llmBaseResultBO, LlmPredictVO::getDealPrediction))")
    @Mapping(target = "defeatReasonPrediction", expression = "java(getPredictField(llmBaseResultBO, LlmPredictVO::getDefeatReasonPrediction))")
    @Mapping(target = "merchantQuote", expression = "java(getPriceField(llmBaseResultBO, LlmPriceVO::getMerchantQuote))")
    @Mapping(target = "customerExpectedPrice", expression = "java(getPriceField(llmBaseResultBO, LlmPriceVO::getCustomerExpectedPrice))")
    @Mapping(target = "delDailyRentalPrice", expression = "java(getPriceField(llmBaseResultBO, LlmPriceVO::getDelDailyRentalPrice))")
    @Mapping(target = "delMonthlyRentalPrice", expression = "java(getPriceField(llmBaseResultBO, LlmPriceVO::getDelMonthlyRentalPrice))")
    @Mapping(target = "audioTonnage", expression = "java(getDeviceField(llmBaseResultBO, LlmDeviceVO::getAudioTonnage))")
    @Mapping(target = "audioMeters", expression = "java(getDeviceField(llmBaseResultBO, LlmDeviceVO::getAudioMeters))")
    @Mapping(target = "audioWatts", expression = "java(getDeviceField(llmBaseResultBO, LlmDeviceVO::getAudioWatts))")
    AudioStructuredData toAudioStructuredData(LlmBaseResultBO llmBaseResultBO);

    default <T> String getDeviceField(LlmBaseResultBO llmBaseResultBO, Function<LlmDeviceVO, T> mapper) {
        return Optional.ofNullable(llmBaseResultBO)
                .map(LlmBaseResultBO::getLlmDevices)
                .map(mapper)
                .map(Object::toString)
                .orElse("");
    }

    default <T> String getDeviceTimesField(LlmBaseResultBO llmBaseResultBO, Function<LlmDeviceTimesVO, T> mapper) {
        return Optional.ofNullable(llmBaseResultBO)
                .map(LlmBaseResultBO::getDeviceTimes)
                .map(mapper)
                .map(Object::toString)
                .orElse("");
    }

    default <T> String getPriceField(LlmBaseResultBO llmBaseResultBO, Function<LlmPriceVO, T> mapper) {
        return Optional.ofNullable(llmBaseResultBO)
                .map(LlmBaseResultBO::getPriceVO)
                .map(mapper)
                .map(Object::toString)
                .orElse("");
    }

    default <T> String getDeviceUsagesField(LlmBaseResultBO llmBaseResultBO, Function<LlmDeviceUsageVO, T> mapper) {
        return Optional.ofNullable(llmBaseResultBO)
                .map(LlmBaseResultBO::getDeviceUsages)
                .map(mapper)
                .map(Object::toString)
                .orElse("");
    }

    default <T> String getPredictField(LlmBaseResultBO llmBaseResultBO, Function<LlmPredictVO, T> mapper) {
        return Optional.ofNullable(llmBaseResultBO)
                .map(LlmBaseResultBO::getPredictVO)
                .map(mapper)
                .map(Object::toString)
                .orElse("");
    }

    PricingToolProcessData toPricingToolProcessData(TranscribedBizTraceData transcribedBizTraceData);

    List<PricingToolProcessData> toPricingToolProcessDataList(List<TranscribedBizTraceData> transcribedBizTraceDataList);

    private Integer convertToInteger(String delDailyRentalPrice) {
        if (StringUtil.isBlank(delDailyRentalPrice)) {
            return 0;
        }
        return Integer.valueOf(delDailyRentalPrice);
    }

    private BigDecimal convertToBigDecimal(String audioTonnage) {
        if (StringUtil.isBlank(audioTonnage)) {
            return BigDecimal.ZERO;
        }
        return new BigDecimal(audioTonnage);
    }

    private boolean rightData(String data) {
        // 检查是否为空字符串
        if (data.isEmpty()) {
            return true;
        }
        // 检查是否全为数字
        return data.matches("\\d*");
    }

    default RemoteTranscribedDataVO convertTranscribedBizTraceDataVO(TranscribedBizTraceData transcribedBizTraceData,
                                                                     RemoteLeadsInfoPageVO leadsInfoPageVO) {
        if (Objects.isNull(transcribedBizTraceData)) {
            return null;
        }
        AudioStructuredData mergedData = transcribedBizTraceData.getMergedStructuredData();
        if (Objects.isNull(mergedData)) {
            return null;
        }
        if (StringUtil.isBlank(mergedData.getAudioMeters()) && StringUtil.isBlank(mergedData.getAudioWatts())) {
            return null;
        }
        if (StringUtil.isBlank(mergedData.getDeviceSecondCategoryName())) {
            return null;
        }
        if (StringUtil.isBlank(mergedData.getDelDailyRentalPrice()) && StringUtil.isBlank(mergedData.getDelMonthlyRentalPrice())) {
            return null;
        }
        return Optional.of(mergedData).map(e -> {
            RemoteTranscribedDataVO remoteTranscribedDataVO = null;
            if (rightData(mergedData.getAudioTonnage()) && rightData(mergedData.getAudioMeters()) &&
                    rightData(mergedData.getDelDailyRentalPrice()) && rightData(mergedData.getDelMonthlyRentalPrice())) {
                remoteTranscribedDataVO = new RemoteTranscribedDataVO();
                remoteTranscribedDataVO.setDeviceSecondCategoryName(mergedData.getDeviceSecondCategoryName());
                remoteTranscribedDataVO.setAudioTonnage(convertToBigDecimal(mergedData.getAudioTonnage()));
                remoteTranscribedDataVO.setAudioMeters(convertToBigDecimal(mergedData.getAudioMeters()));
                remoteTranscribedDataVO.setDelDailyPrice(convertToInteger(mergedData.getDelDailyRentalPrice()));
                remoteTranscribedDataVO.setDelMonthlyPrice(convertToInteger(mergedData.getDelMonthlyRentalPrice()));

                Optional.ofNullable(leadsInfoPageVO)
                        .map(RemoteLeadsInfoPageVO::getAddress)
                        .map(AreaAddressModel::getCityCode)
                        .ifPresent(remoteTranscribedDataVO::setCityCode);
                Optional.ofNullable(leadsInfoPageVO)
                        .map(RemoteLeadsInfoPageVO::getAddress)
                        .map(AreaAddressModel::getCityName)
                        .ifPresent(remoteTranscribedDataVO::setCityName);
                if (Optional.ofNullable(leadsInfoPageVO).map(RemoteLeadsInfoPageVO::getSubmitTime).isPresent()) {
                    remoteTranscribedDataVO.setSubmitTime(leadsInfoPageVO.getSubmitTime().toLocalDate());
                }
            }
            return remoteTranscribedDataVO;
        }).orElse(null);
    }}