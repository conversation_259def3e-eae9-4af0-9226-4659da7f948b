package com.yaowu.hera.utils.mapstruct.common;

import com.yaowu.heraapi.enums.common.*;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

/**
 * <AUTHOR>
 * @date 2023/2/13
 */
@Mapper
public interface EnumMapstruct {

    EnumMapstruct INSTANCE = Mappers.getMapper(EnumMapstruct.class);

    /**
     * 租赁模式
     *
     * @param leasingModelEnum
     * @return
     */
    com.yaowu.omsapi.enums.common.LeasingModelEnum toLeasingModel(LeasingModelEnum leasingModelEnum);

    /**
     * 是否含税转换
     *
     * @param quoteTaxEnum
     * @return
     */
    com.yaowu.omsapi.enums.common.QuoteTaxEnum toQuoteTax(QuoteTaxEnum quoteTaxEnum);

    /**
     * 转换开票节点
     *
     * @param invoiceNodeEnum
     * @return
     */
    com.yaowu.omsapi.enums.common.InvoiceNodeEnum toInvoiceNode(InvoiceNodeEnum invoiceNodeEnum);

    /**
     * 开票类型转换
     *
     * @param invoiceTypeEnum
     * @return
     */
    com.yaowu.omsapi.enums.common.InvoiceTypeEnum toInvoiceType(InvoiceTypeEnum invoiceTypeEnum);

    /**
     * 计价方式转换
     *
     * @param valuationWayEnum
     * @return
     */
    com.yaowu.omsapi.enums.common.ValuationWayEnum toValuationWay(ValuationWayEnum valuationWayEnum);

    /**
     * 食宿转换
     *
     * @param foodBedEnum
     * @return
     */
    com.yaowu.omsapi.enums.common.FoodBedEnum toFoodBed(FoodBedEnum foodBedEnum);
}
