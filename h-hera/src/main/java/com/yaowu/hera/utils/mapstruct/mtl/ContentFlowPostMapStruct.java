package com.yaowu.hera.utils.mapstruct.mtl;

import cn.hutool.core.collection.CollectionUtil;
import com.yaowu.hera.config.nacos.mtl.ContentFlowPostConfig.TopicDetailBannerConfig;
import com.yaowu.hera.model.bo.content.ContentPostBaseBO;
import com.yaowu.hera.model.bo.content.ContentTagBaseInfoVO;
import com.yaowu.hera.model.dto.user.CustomerAppletInfoUserBatchAddDTO;
import com.yaowu.hera.model.entity.content.*;
import com.yaowu.hera.model.entity.content.ContentArticleSnapshot;
import com.yaowu.hera.model.entity.content.ContentFlowPost;
import com.yaowu.hera.model.entity.content.ContentFlowTag;
import com.yaowu.hera.model.entity.content.CustomerAppletInfo;
import com.yaowu.hera.model.entity.content.MultiMediaInfoGroups;
import com.yaowu.hera.model.entity.content.MultiMediaInfoGroups.MultiMediaInfo;
import com.yaowu.hera.utils.LongUtils;
import com.yaowu.hera.utils.StreamUtil;
import com.yaowu.hera.utils.convertor.content.ContentFlowPostConvertor;
import com.yaowu.heraapi.enums.content.RemoteContentFlowStatusEnum;
import com.yaowu.heraapi.enums.content.RemoteContentPostPushTypeEnum;
import com.yaowu.heraapi.enums.content.RemoteContentPostTypeEnum;
import com.yaowu.heraapi.model.dto.content.*;
import com.yaowu.heraapi.model.dto.content.RemoteVirtualUserAddDTO.VirtualUserInfo;
import com.yaowu.heraapi.model.pojo.common.UploadFileModel;
import com.yaowu.heraapi.model.vo.content.*;
import com.yaowu.passportapi.model.dto.user.UserAddDTO;
import com.yaowu.passportapi.model.vo.user.UserInfoVO;
import com.yaowu.heraapi.model.vo.content.RemoteContentFlowPageVO;
import com.yaowu.heraapi.model.vo.content.RemoteRecommendContentPageVO.RecommendContentPageInfo;
import com.yaowu.heraapi.model.vo.content.RemoteRecommendedPageVO;
import com.yaowu.heraapi.model.vo.content.RemoteTopicBannerInfoVO;
import com.yaowu.heraapi.model.vo.content.RemoteRecommendedPageVO.RecommendedPageInfo;
import com.yaowu.heraapi.model.vo.mtl.marketing.HeraPageRecommendPositionConfigVO;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.MappingConstants;
import org.mapstruct.Named;

import java.time.LocalDateTime;
import java.util.*;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;

import static com.yaowu.heraapi.enums.content.RemoteContentFlowStatusButtonEnum.BLOCK_BUTTON;
import static com.yaowu.heraapi.enums.content.RemoteContentFlowStatusButtonEnum.EDIT_CONTENT_BUTTON;
import static com.yaowu.heraapi.enums.content.RemoteContentFlowStatusButtonEnum.EDIT_TAGS_AND_CATEGORIES_BUTTON;
import static com.yaowu.heraapi.enums.content.RemoteContentFlowStatusButtonEnum.RESTORE_BUTTON;
import static com.yaowu.heraapi.enums.content.RemoteContentFlowStatusButtonEnum.REVIEW_BUTTON;
import static com.yaowu.heraapi.enums.content.RemoteContentPostPushTypeEnum.ADMIN_PUSH;
import static com.yaowu.heraapi.enums.content.RemoteContentPostPushTypeEnum.INTERNAL_PUSH;

/**
 * <AUTHOR>
 */
@Mapper(
        componentModel = MappingConstants.ComponentModel.SPRING,
        imports = {
                RemoteContentFlowStatusEnum.class,
                Boolean.class,
                ContentFlowPostConvertor.class,
                Optional.class
        }
)
public interface ContentFlowPostMapStruct {


    @Mapping(target = "multiMediaInfoGroups", expression = "java(getMultiMediaInfoGroups(addDTO))")
    @Mapping(target = "contentDesc", expression = "java(addDTO.getTitle())")
    @Mapping(target = "mainImage", expression = "java(getMainImage(addDTO))")
    @Mapping(target = "mainImageHeight", expression = "java(getMainImageHeight(addDTO))")
    @Mapping(target = "mainImageWidth", expression = "java(getMainImageWidth(addDTO))")
    @Mapping(target = "publishTime", expression = "java(getPublishTime(addDTO))")
    @Mapping(target = "blockTime", expression = "java(getBlockTime(addDTO))")
    @Mapping(target = "operatorId", source = "addDTO.operatorId")
    @Mapping(target = "operator", source = "addDTO.operatorName")
    @Mapping(target = "createdUserId ", source = "addDTO.operatorId")
    @Mapping(target = "createdUser", source = "addDTO.operatorName")
    @Mapping(target = "operateTime", source = "now")
    ContentFlowPost toContentFlowPost(RemoteAddContentFlowDTO addDTO, LocalDateTime now);

    default String getMainImage(RemoteAddContentFlowDTO addDTO) {
        return Optional.ofNullable(addDTO).map(RemoteAddContentFlowDTO::getCoverImage).map(UploadFileModel::getUrl).orElse("");
    }

    default Integer getMainImageHeight(RemoteAddContentFlowDTO addDTO) {
        return Optional.ofNullable(addDTO).map(RemoteAddContentFlowDTO::getCoverImage).map(UploadFileModel::getHeight).orElse(null);
    }

    default Integer getMainImageWidth(RemoteAddContentFlowDTO addDTO) {
        return Optional.ofNullable(addDTO).map(RemoteAddContentFlowDTO::getCoverImage).map(UploadFileModel::getWidth).orElse(null);
    }

    default MultiMediaInfoGroups getMultiMediaInfoGroups(RemoteAddContentFlowDTO addDTO) {
        if (Objects.isNull(addDTO) || (CollectionUtil.isEmpty(addDTO.getImages()) && CollectionUtil.isEmpty(addDTO.getVideos()))) {
            return null;
        }
        MultiMediaInfoGroups multiMediaInfoGroups = new MultiMediaInfoGroups();
        List<MultiMediaInfo> multiMediaInfoList = new ArrayList<>();
        fillFilesByFileType(addDTO.getImages(), 1, multiMediaInfoList);
        fillFilesByFileType(addDTO.getVideos(), 2, multiMediaInfoList);
        multiMediaInfoGroups.setMultiMediaInfoList(multiMediaInfoList);
        return multiMediaInfoGroups;
    }

    private static void fillFilesByFileType(List<UploadFileModel> uploadFiles, int fileType, List<MultiMediaInfo> multiMediaInfoList) {
        if (CollectionUtil.isNotEmpty(uploadFiles)) {
            List<MultiMediaInfo> images = new ArrayList<>();
            for (int i = 0; i < uploadFiles.size(); i++) {
                int finalI = i;
                Optional.ofNullable(uploadFiles.get(i)).ifPresent(e -> {
                    MultiMediaInfo image = new MultiMediaInfo();
                    image.setSort(finalI);
                    image.setFileName(e.getFileName());
                    image.setUrl(e.getUrl());
                    image.setExtension(e.getExtension());
                    image.setFileType(fileType);
                    image.setFileSize(e.getFileSize());
                    image.setWidth(e.getWidth());
                    image.setHeight(e.getHeight());
                    images.add(image);
                });
            }
            multiMediaInfoList.addAll(images);
        }
    }


    default LocalDateTime getPublishTime(RemoteAddContentFlowDTO addDTO) {
        if (Objects.isNull(addDTO) || !Objects.equals(addDTO.getPostStatus(), RemoteContentFlowStatusEnum.NORMAL)) {
            return null;
        }
        return LocalDateTime.now();
    }


    default LocalDateTime getBlockTime(RemoteAddContentFlowDTO addDTO) {
        if (Objects.isNull(addDTO) || !Objects.equals(addDTO.getPostStatus(), RemoteContentFlowStatusEnum.BLOCKED)) {
            return null;
        }
        return LocalDateTime.now();
    }

    default ContentFlowPost toUpdatedContentFlowPost(ContentFlowPost oldPostData,
                                                     RemoteEditContentFlowDTO editContentFlowDTO,
                                                     CustomerAppletInfo customerAppletInfo) {
        if (Objects.isNull(oldPostData) || Objects.isNull(editContentFlowDTO)) {
            return null;
        }
        if (oldPostData.getPostPushType() == RemoteContentPostPushTypeEnum.INTERNAL_PUSH &&
                editContentFlowDTO.getPostStatus() == RemoteContentFlowStatusEnum.NORMAL) {
            oldPostData.setPublishAccountId(editContentFlowDTO.getOperatorId());
            oldPostData.setPublishAccountName(editContentFlowDTO.getOperatorName());
        }
        if ((oldPostData.getPostPushType() == INTERNAL_PUSH || oldPostData.getPostPushType() == ADMIN_PUSH) &&
                (editContentFlowDTO.getPostStatus() == RemoteContentFlowStatusEnum.NORMAL && LongUtils.isInvalid(oldPostData.getAuthorId()))) {
            Optional.ofNullable(customerAppletInfo).ifPresent(user -> {
                oldPostData.setAuthorId(customerAppletInfo.getUserId());
                oldPostData.setAuthorName(customerAppletInfo.getNickName());
                oldPostData.setAuthorPicture(customerAppletInfo.getProfilePhoto());
            });
        }
        oldPostData.setTitle(editContentFlowDTO.getTitle())
                .setOperateTime(LocalDateTime.now())
                .setOperator(editContentFlowDTO.getOperatorName())
                .setOperatorId(editContentFlowDTO.getOperatorId())
                .setPostStatus(editContentFlowDTO.getPostStatus())
                .setPostType(editContentFlowDTO.getPostType())
                .setPostPushType(editContentFlowDTO.getPostPushType())
                .setContentDesc(editContentFlowDTO.getTitle())
                .setContent(editContentFlowDTO.getContent())
                .setMainImage(getMainImage(editContentFlowDTO))
                .setMainImageHeight(getMainImageHeight(editContentFlowDTO))
                .setMainImageWidth(getMainImageWidth(editContentFlowDTO))
                .setMultiMediaInfoGroups(getMultiMediaInfoGroups(editContentFlowDTO))
                .setPublishTime(getPublishTime(editContentFlowDTO))
                .setBlockTime(getBlockTime(editContentFlowDTO))
                .setId(editContentFlowDTO.getId());
        return oldPostData;
    }

    @Mapping(target = "multiMediaInfoGroups", expression = "java(getMultiMediaInfoGroups(addDTO))")
    @Mapping(target = "contentDesc", expression = "java(addDTO.getTitle())")
    @Mapping(target = "mainImage", expression = "java(getMainImage(addDTO))")
    @Mapping(target = "publishTime", expression = "java(getPublishTime(addDTO))")
    @Mapping(target = "blockTime", expression = "java(getBlockTime(addDTO))")
    ContentFlowPost toEditContentFlowPost(RemoteAddContentFlowDTO addDTO);

    @Mapping(target = "behaviorInfo", source = "behaviorBaseInfoVO")
    @Mapping(target = "contentFlowButtons", expression = "java(computeContentFlowButtons(contentFlowPost))")
    @Mapping(target = "authorNickname", source = "contentFlowPost.authorName")
    @Mapping(target = "status", expression = "java(contentFlowPost.getPostStatus() == null ? null:contentFlowPost.getPostStatus().getCode())")
    @Mapping(target = "statusDesc", expression = "java(contentFlowPost.getPostStatus() != null ? contentFlowPost.getPostStatus().getMsg(): \"\")")
    @Mapping(target = "contentPublishType", expression = "java(contentFlowPost.getPostPushType() == null ? null:contentFlowPost.getPostPushType().getCode())")
    @Mapping(target = "contentPublishTypeDesc", expression = "java(contentFlowPost.getPostPushType() != null ? contentFlowPost.getPostPushType().getMsg() : \"\")")
    @Mapping(target = "contentTagList", expression = "java(computeContentTagList(contentTagBaseInfoVO))")
    @Mapping(target = "id", source = "contentFlowPost.id")
    @Mapping(target = "operator", source = "contentFlowPost.operator")
    @Mapping(target = "lastOperateTime", source = "contentFlowPost.operateTime")
    @Mapping(target = "postType", source = "contentFlowPost.postType", qualifiedByName = "toPostTypeCode")
    @Mapping(target = "postTypeDesc", source = "contentFlowPost.postType", qualifiedByName = "toPostTypeDesc")
    RemoteContentFlowPageVO toRemoteContentFlowPageVO(ContentFlowPost contentFlowPost,
                                                      ContentTagBaseInfoVO contentTagBaseInfoVO,
                                                      RemoteContentBehaviorBaseInfoVO behaviorBaseInfoVO);

    default List<String> computeContentTagList(ContentTagBaseInfoVO contentTagBaseInfoVO) {
        if (Objects.isNull(contentTagBaseInfoVO)) {
            return Collections.emptyList();
        }
        return StreamUtil.of(contentTagBaseInfoVO.getContentFlowTags()).map(ContentFlowTag::getTagContent).toList();
    }

    default List<String> computeContentFlowButtons(ContentFlowPost contentFlowPost) {
        if (Objects.isNull(contentFlowPost)) {
            return Collections.emptyList();
        }
        List<String> buttons = new ArrayList<>();
        switch (contentFlowPost.getPostStatus()) {
            case PENDING_REVIEW -> buttons.addAll(List.of(REVIEW_BUTTON.name(), BLOCK_BUTTON.name()));
            case NORMAL ->
                    buttons.addAll(List.of(EDIT_TAGS_AND_CATEGORIES_BUTTON.name(), EDIT_CONTENT_BUTTON.name(), BLOCK_BUTTON.name()));
            case BLOCKED -> buttons.add(RESTORE_BUTTON.name());
        }
        return buttons;
    }

    @Mapping(target = "postStatus", expression = "java(com.yaowu.heraapi.enums.content.RemoteContentFlowStatusEnum.PENDING_REVIEW)")
    @Mapping(target = "postPushType", expression = "java(com.yaowu.heraapi.enums.content.RemoteContentPostPushTypeEnum.INTERNAL_PUSH)")
    ContentFlowPost toContentFlowPost(RemoteAddAiContentFlowDTO addDTO);


    @Mapping(target = "userId", source = "virtualUserInfo.bizUserId")
    @Mapping(target = "nickName", source = "virtualUserInfo.virtualUserName")
    @Mapping(target = "profilePhoto", source = "virtualUserInfo.virtualPhoto")
    @Mapping(target = "virtualFlag", expression = "java(Boolean.TRUE)")
    CustomerAppletInfo toCustomerAppletInfo(VirtualUserInfo virtualUserInfo);


    @Mapping(target = "publishAccountId", ignore = true)// 不返回发布人信息
    @Mapping(target = "publishAccountName", ignore = true)
// 不返回发布人信息
    RemoteRecommendedPageVO.RecommendedPageInfo toRecommendedPageInfo(ContentFlowPost contentFlowPost);


    List<RemoteRecommendedPageVO.RecommendedPageInfo> toRecommendedPageInfoList(List<ContentFlowPost> contentFlowPosts);

    default ContentFlowPost toUpdatedStatusContentFlowPost(RemoteEditContentFlowStatusDTO editStatusDTO, ContentFlowPost contentFlowPost, LocalDateTime now) {
        contentFlowPost.setPostStatus(RemoteContentFlowStatusEnum.codeOf(editStatusDTO.getContentStatus()));
        contentFlowPost.setOperator(editStatusDTO.getOperatorName());
        contentFlowPost.setOperatorId(editStatusDTO.getOperatorId());
        contentFlowPost.setOperateTime(now);
        return contentFlowPost;
    }


    @Mapping(target = "bannerConfigs", expression = "java(ContentFlowPostConvertor.toBannerConfigs(bannerConfigs))")
    RemoteTopicBannerInfoVO toRemoteTopicBannerInfoVO(Long topicId, List<TopicDetailBannerConfig> bannerConfigs);

    @Mapping(target = "sort", expression = "java(contentIdToSort.get(post.getId()) ==null ? null : contentIdToSort.get(post.getId()).getSort())")
    @Mapping(target = "positionFlag", expression = "java(Boolean.TRUE)")
    @Mapping(target = "tagContent", expression = "java(contentIdToSort.get(post.getId()) ==null ? null : contentIdToSort.get(post.getId()).getTagContent())")
    ContentPostBaseBO toContentPostBaseBO(Map<Long, HeraPageRecommendPositionConfigVO> contentIdToSort, ContentFlowPost post);

    RecommendedPageInfo toRecommendedPageInfo(ContentArticleSnapshot contentArticleSnapshot);

    RecommendContentPageInfo toRecommendedPageInfo(ContentPostBaseBO contentPostBaseBO);

    RecommendContentPageInfo toRecommendPageInfo(ContentFlowPost contentFlowPost, Boolean positionFlag, String tagContent);

    ContentPostBaseBO toContentPostBaseBO(ContentFlowPost one, int sort);

    @Mapping(target = "id", source = "contentArticleSnapshot.contentId")
    ContentPostBaseBO toContentPostBaseBO(ContentArticleSnapshot contentArticleSnapshot);


    List<UserAddDTO> toUserAddDTOList(List<RemoteBackDoorCustomerAppletInfoAddDTO.UserInfo> virtualUserInfos);

    @Mapping(target = "testFlag", ignore = true)
    @Mapping(target = "password", ignore = true)
    @Mapping(target = "name", source = "userInfo.userName")
    @Mapping(target = "identity", expression = "java(com.yaowu.passportapi.enums.user.UserIdentityEnum.CUSTOMER)")
    @Mapping(target = "idCardNo", ignore = true)
    @Mapping(target = "ext", ignore = true)
    @Mapping(target = "createEsignAccount", ignore = true)
    @Mapping(target = "bizUserSourceType", ignore = true)
    UserAddDTO toUserAddDTO(RemoteBackDoorCustomerAppletInfoAddDTO.UserInfo userInfo);


    @Mapping(target = "userBizId", source = "userInfoVO.id")
    @Mapping(target = "phone", source = "userInfoVO.phone")
    @Mapping(target = "userName", source = "addUser.userName")
    @Mapping(target = "photo", source = "addUser.photo")
    CustomerAppletInfoUserBatchAddDTO.UserInfo toCustomerAppletInfoUserBatchAddDTOUserInfo(UserInfoVO userInfoVO, RemoteBackDoorCustomerAppletInfoAddDTO.UserInfo addUser);


    @Mapping(target = "userId", source = "userInfo.userBizId")
    @Mapping(target = "profilePhoto", source = "userInfo.photo")
    @Mapping(target = "openId", ignore = true)
    @Mapping(target = "nickName", source = "userInfo.userName")
    @Mapping(target = "version", ignore = true)
    @Mapping(target = "updateTime", ignore = true)
    @Mapping(target = "testFlag", ignore = true)
    @Mapping(target = "modifierId", ignore = true)
    @Mapping(target = "modifier", ignore = true)
    @Mapping(target = "id", ignore = true)
    @Mapping(target = "deleteFlag", ignore = true)
    @Mapping(target = "creatorId", ignore = true)
    @Mapping(target = "creator", ignore = true)
    @Mapping(target = "createTime", ignore = true)
    CustomerAppletInfo toCustomerAppletInfo(CustomerAppletInfoUserBatchAddDTO.UserInfo userInfo, boolean virtualFlag, Integer userMark);


    RemoteCustomerAppletInfoSimpleVO toRemoteCustomerAppletInfoSimpleVO(CustomerAppletInfo customerAppletInfo);


    @Mapping(target = "hasLikedUserIds", source = "likeUserIds")
    @Mapping(target = "hasCommentedUserIds", source = "commentUserIds")
    @Mapping(target = "contentId", source = "content.id")
    @Mapping(target = "contentText", source = "content.content")
    RemoteContentSimpleVO toRemoteContentSimpleVO(ContentFlowPost content, Set<Long> likeUserIds, Set<Long> commentUserIds);

    @Named("toPostTypeCode")
    default Integer toPostTypeCode(RemoteContentPostTypeEnum postTypeEnum) {
        return postTypeEnum == null ? null : postTypeEnum.getCode();
    }

    @Named("toPostTypeDesc")
    default String toPostTypeDesc(RemoteContentPostTypeEnum postTypeEnum) {
        return postTypeEnum == null ? null : postTypeEnum.getMsg();
    }
}

