package com.yaowu.hera.model.entity.share;

import com.baomidou.mybatisplus.annotation.TableName;
import com.freedom.web.domain.BaseLogicTable;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * <p>
 * 推荐线索设备表
 * </p>
 *
 * <AUTHOR>
 * @since 2023-11-21
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@TableName(value = "b_store_share_record", autoResultMap = true)
@Schema(title = "StoreShareRecord对象")
public class StoreShareRecord extends BaseLogicTable {

    private static final long serialVersionUID = 1L;

    @Schema(title = "门店id")
    private Long storeId;

    @Schema(title = "用户id")
    private Long shareUserId;

    @Schema(title = "分享类型")
    private String shareType;

    @Schema(title = "分享场景")
    private String shareScene;

    @Schema(title = "分享日期")
    private LocalDate shareDate;

    @Schema(title = "分享时间")
    private LocalDateTime shareTime;

    @Schema(title = "分享码--用于追溯")
    private String shareCode;

}
