package com.yaowu.hera.model.entity.leads;

import com.baomidou.mybatisplus.annotation.TableName;
import com.freedom.web.domain.BaseLogicTable;
import com.yaowu.hera.enums.mtl.BizToCustomerStatusEnum;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.math.BigDecimal;
import java.time.LocalDateTime;


/**
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@TableName(value = "b_invoice_order_relation", autoResultMap = true)
@Schema(title = "b_invoice_order_relation对象")
public class InvoiceOrderRelation extends BaseLogicTable {

    private static final long serialVersionUID = 1L;
    @Schema(title = "发票id")
    private Long invoiceId;

    @Schema(title = "订单id")
    private Long orderId;

    @Schema(title = "商户id")
    private Long merchantId;

    @Schema(title = "门店id")
    private Long storeId;

    @Schema(title = "发票订单关系状态，1-未生效，2-已生效")
    private Integer enabled;
}
