package com.yaowu.hera.model.bo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * <AUTHOR>
 * @version 1.0.0
 */
@Data
@Accessors(chain = true)
@Schema(title = "弹窗动态扩展参数BO")
public class ToastBizParamsBO {

    @Schema(title = "业务id")
    private Long id;

    @Schema(title = "业务名称，该名称需要和前端对齐")
    private String initKeyword;

    @Schema(title = "uri")
    private String uri;

    @Schema(title = "title")
    private String title;
}
