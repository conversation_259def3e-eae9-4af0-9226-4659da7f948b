package com.yaowu.hera.model.vo.user;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * <AUTHOR>
 * @description
 * @date 2023:07:24 14:57:02
 */
@Data
@Schema(title = "MokaEmployeeVO")
public class EmployeeVO {

    @Schema(title = "员工姓名")
    private String realName;

    @Schema(title = "id")
    private Long id;

    @Schema(title = "员工在People系统的唯一id")
    private Long employeeId;

    @Schema(title = "要务平台ID")
    private Long ywId;

    @Schema(title = "员工工号#由HR在People中设置的")
    private String employeeNo;

    @Schema(title = "员工所在的部门名称")
    private String deptName;

    @Schema(title = "员工状态:在职/离职")
    private String employeeStatusDesc;

    @Schema(title = "员工状态#ON_JOB:1:在职#LEAVE_OFFICE:2:离职#")
    private EmployeeStatusEnum employeeStatus;

    @Schema(title = "个人手机号")
    private String telephone;

    public enum EmployeeStatusEnum {

        /**
         * 在职
         */
        ON_JOB(1, "在职"),
        /**
         * 离职
         */
        LEAVE_OFFICE(2, "离职"),
        ;

        private final int code;
        private final String desc;

        EmployeeStatusEnum(int code, String desc) {
            this.code = code;
            this.desc = desc;
        }


    }
}