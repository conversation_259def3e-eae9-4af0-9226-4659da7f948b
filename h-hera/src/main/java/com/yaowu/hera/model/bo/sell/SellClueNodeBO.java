package com.yaowu.hera.model.bo.sell;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.experimental.Accessors;

import java.time.LocalDateTime;
import java.util.List;

/**
 * <p>
 * 推荐线索节点表
 * </p>
 *
 * <AUTHOR>
 * @since 2023-11-22
 */
@Data
@Accessors(chain = true)
public class SellClueNodeBO {

    @Schema(title = "推荐线索id")
    private Long sellClueId;

    @Schema(title = "节点编码")
    private Integer nodeCode;

    @Schema(title = "节点标题")
    private String nodeTitle;

    @Schema(title = "节点时间")
    private LocalDateTime nodeTime;

    @Schema(title = "节点描述")
    private List<SellClueNodeDescBO> nodeDescList;

    @Schema(title = "业务id")
    private String bizId;

}
