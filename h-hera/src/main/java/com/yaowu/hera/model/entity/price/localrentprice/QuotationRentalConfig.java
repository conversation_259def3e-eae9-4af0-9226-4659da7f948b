package com.yaowu.hera.model.entity.price.localrentprice;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.freedom.web.domain.BaseLogicTable;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p>
 * 华为隐私号话单
 * </p>
 *
 * <AUTHOR>
 * @since 2024-08-12
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@Schema(title = "QuotationRentalConfig对象", description = "租价配置表")
@TableName(value = "b_quotation_rental_config", autoResultMap = true)
public class QuotationRentalConfig extends BaseLogicTable {

    @Schema(title = "一级品类id")
    private Long firstCategoryId;

    @Schema(title = "二级品类id")
    private Long secondCategoryId;

    @Schema(title = "spu id")
    private Long spuId;

    @Schema(title ="spu名称")
    private String spuName;

    @Schema(title = "spu排序")
    private Integer spuSort;

    @Schema(title = "状态:1-启用、2-禁用")
    @TableField(value = "config_status")
    private Integer status;
}
