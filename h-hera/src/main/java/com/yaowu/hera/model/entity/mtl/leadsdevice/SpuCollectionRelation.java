package com.yaowu.hera.model.entity.mtl.leadsdevice;

import com.baomidou.mybatisplus.annotation.TableName;
import com.freedom.web.domain.BaseFlakeTable;
import com.freedom.web.domain.BaseLogicTable;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serial;
import java.io.Serializable;

/**
 * SPU合集关联关系实体
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2024/1/4
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@TableName("mtl_spu_collection_relation")
@Schema(title = "SPU合集关联关系", description = "SPU合集与SPU和二级品类的关联关系")
public class SpuCollectionRelation extends BaseLogicTable implements Serializable {
    @Serial
    private static final long serialVersionUID = 1L;

    @Schema(title = "二级品类ID")
    private Long secondCategoryId;

    @Schema(title = "设备标签ID")
    private Long deviceTagId;

    @Schema(title = "SPU合集ID")
    private Long spuCollectionId;
    
    @Schema(title = "SPU合集名称")
    private String spuCollectionName;

    @Schema(title = "SPU ID")
    private Long spuId;
}