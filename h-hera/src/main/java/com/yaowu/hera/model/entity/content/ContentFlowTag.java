
package com.yaowu.hera.model.entity.content;

import com.baomidou.mybatisplus.annotation.TableName;
import com.freedom.web.domain.BaseLogicTable;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

/**
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@TableName(value = "b_content_flow_tag", autoResultMap = true)
@Schema(title = "ContentFlowTag对象")
public class ContentFlowTag extends BaseLogicTable {

    @Schema(title = "标签内容")
    private String tagContent;

}
