package com.yaowu.hera.model.entity.leads;

import com.baomidou.mybatisplus.annotation.TableName;
import com.freedom.web.domain.BaseLogicTable;
import com.yaowu.heraapi.enums.mtl.LeadsCleanStageResultEnum;
import com.yaowu.hera.enums.mtl.LeadsCleanStageEnum;
import io.swagger.v3.oas.annotations.media.Schema;

import java.time.LocalDateTime;
import lombok.Data;

/**
 * <p>
 * 线索清洗表
 * </p>
 *
 * <AUTHOR>
 * @since 2024-09-18
 */
@TableName("b_leads_rating_record")
@Schema(title = "LeadsRatingRecord对象", description = "线索清洗表")
@Data
public class LeadsRatingRecord extends BaseLogicTable  {

    private static final long serialVersionUID = 1L;

    @Schema(title = "线索表id(b_leads_info.id)")
    private Long leadsInfoId;

    @Schema(title = "线索清洗阶段")
    private LeadsCleanStageEnum ratingStage;

    @Schema(title = "线索清洗阶段描述")
    private String ratingStageDesc;

    @Schema(title = "线索清洗阶段结果")
    private LeadsCleanStageResultEnum ratingStageResult;

    @Schema(title = "线索清洗阶段结果描述")
    private String ratingStageResultDesc;

    @Schema(title = "清洗时间")
    private LocalDateTime ratingTime;

    @Schema(title = "清洗备注")
    private String ratingRemark;

}
