package com.yaowu.hera.model.entity.customeronboarding;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.freedom.web.domain.BaseLogicTable;
import com.yaowu.hera.config.typhandler.LongArrayTypeHandler;
import com.yaowu.heraapi.enums.mtl.CustomerOnboardingEarningType;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

/**
 * <AUTHOR>
 * @email <EMAIL>
 * @creatTime 2024.07.24 10:50:00
 * @description:
 */

@Data
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@TableName(value = "b_customer_super_earning_detail", autoResultMap = true)
@Schema(title = "客户邀请收益明细表")
public class CustomerSuperpositionEarningDetail extends BaseLogicTable {

    @Schema(description = "客户用户ID （b_biz_user.id）", title = "客户用户ID")
    private Long customerBizUserId;

    /**
     * @see com.yaowu.heraapi.enums.mtl.CustomerOnboardingEarningType
     */
    @Schema(description = "收益类型：1.叠加奖等级1，2.叠加奖等级2，3.叠加奖等级3", title = "收益类型")
    private CustomerOnboardingEarningType earningType;

    @TableField(typeHandler = LongArrayTypeHandler.class)
    @Schema(description = "邀请记录ID", title = "邀请记录ID")
    private List<Long> inviteRecordIds;

    @Schema(description = "收益金额", title = "收益金额")
    private BigDecimal earningAmount;

    @Schema(description = "产生收益的时间", title = "产生收益的时间")
    private LocalDateTime createEarningTime;

    @Schema(description = "是否入账：1.已入账，0 未入账", title = "是否入账")
    private Boolean incomeFlag;

    @Schema(description = "入账时间", title = "入账时间")
    private LocalDateTime incomeTime;

    @TableField(exist = false)
    @Schema(description = "客户叠加总收益金额", title = "客户叠加总收益金额")
    private BigDecimal superInviteAmount = BigDecimal.ZERO;

    @TableField(exist = false)
    @Schema(description = "待入账金额", title = "带入账金额")
    private BigDecimal pendingIncomeAmount = BigDecimal.ZERO;

}
