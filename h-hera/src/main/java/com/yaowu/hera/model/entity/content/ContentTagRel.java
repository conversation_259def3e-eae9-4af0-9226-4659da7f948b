
package com.yaowu.hera.model.entity.content;

import com.baomidou.mybatisplus.annotation.TableName;
import com.freedom.web.domain.BaseLogicTable;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

/**
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@TableName(value = "b_content_tag_rel", autoResultMap = true)
@Schema(title = "ContentTagRel对象")
public class ContentTagRel extends BaseLogicTable {
    private static final long serialVersionUID = 1L;


    @Schema(title = "内容id")
    private Long contentId;

    @Schema(title = "标签id")
    private Long tagId;
}
