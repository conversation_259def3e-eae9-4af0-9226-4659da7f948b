package com.yaowu.hera.model.dto.agent;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.mapstruct.Named;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2024/7/20 13:44
 */
@Builder
@Data
@AllArgsConstructor
@NoArgsConstructor
public class BaiduAgentConversationRequestDTO implements Serializable {
    /**
     * threadId : threadId
     * source : appId
     * from : openapi
     * openId : 1341234
     * message : {"content":{"type":"text","value":{"showText":"content"}}}
     */

    /**
     * 应用密钥
     */
    private String secretKey;

    /**
     * 当前会话框的 id 标识，一个 threadId 代表一个会话
     */
    private String threadId;
    /**
     * client_id（智能体 ID，可在「文心智能体平台」平台获得。）
     */
    private String source;
    /**
     * 固定值 openapi
     */
    private String from;
    /**
     * 外部用户ID（串联对话上下文使用，可自行定义，需要保证唯一性）
     * 接入方需要为每位使用接入方服务的用户生成唯一 ID 具体要求如下：
     *   • 唯一性：每个用户具有唯一的 ID,不同用户的 ID 不能相同
     *   • 格式：可以是数字或者字符串（数字、下划线、大小写字母），字符串的长度 <= 100字符
     *   • 可追溯：每个用户 ID，接入方可以追溯到用户
     */
    private String openId;
    /**
     * 会话请求时发送的消息主体
     */
    private MessageBean message;


    @Builder
    @Data
    public static class MessageBean implements Serializable {
        /**
         * content : {"type":"text","value":{"showText":"content"}}
         */

        private ContentBean content;


        @Builder
        @Data
        public static class ContentBean implements Serializable{
            /**
             * type : text
             * value : {"showText":"content"}
             */

            /**
             * 标识 AI 会话请求发送的问题类型，取值范围：
             * text: 标识文本类型
             * image: 标识图片类型
             * file: 标识文件类型
             */
            private String type;

            /**
             * 用来放置不同 type 类型的主体内容，不同 type 对应的 value 内的字段不同
             * text: 文本类型
             * {
             *   "content": {
             *      "type": "text",
             *      "value": {
             *           "showText": "请介绍下 http 协议", // type 为 text 时必传字段，
             *        }
             *    }
             * }
             */
            private ValueBean value;

            @Builder
            @Data
            public static class ValueBean {
                /**
                 * showText : content
                 */

                private String showText;

            }
        }
    }
}
