package com.yaowu.hera.model.entity.agent;

import com.baomidou.mybatisplus.annotation.TableName;

import java.io.Serial;

import com.freedom.web.domain.BaseLogicTable;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p>
 * 测评智能体场景
 * </p>
 *
 * <AUTHOR>
 * @since 2024-09-18
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@TableName("agent_evaluate_scene")
@ApiModel(value = "AgentEvaluateScene对象", description = "测评智能体场景")
public class AgentEvaluateScene extends BaseLogicTable {

    @Serial
    private static final long serialVersionUID = 1L;

    @ApiModelProperty("智能体应用id")
    private Long agentAppId;

    @ApiModelProperty("场景描述")
    private String sceneDescription;

    @ApiModelProperty("场景任务")
    private String sceneTask;
}
