package com.yaowu.hera.model.dto.visit;

import com.freedom.web.annotation.handle.param.Trim;
import com.freedom.web.model.param.BasePageRequest;
import io.swagger.annotations.ApiModel;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalDate;

@EqualsAndHashCode(callSuper = true)
@Data
@ApiModel(description = "注册-回访记录分页请求参数")
public class RegistrationRecordPageDTO extends BasePageRequest {


    @Trim
    @Schema(title = "客户电话")
    private String phone;

    @Schema(title = "线索类型：1-客找商、2-商找客、 3-混合")
    private Integer leadsType;

    @Schema(title = "是否接通：0-空号无效号码 1-不需要拨打 2-3次均未接通 3-接通后直接挂断 4-接通后正常沟通")
    private Integer callBackStatus;

    @Schema(title = "是否重点客户：0-否、1-是")
    private Integer keyUser;

    @Schema(title = "用户身份：0-未明确;1-客户(非租赁商户);2-租赁商户;3-我司内部员工;4-厂家")
    private Integer userIdentity;

    @Schema(title = "是否下线索工单：0-否、1-是")
    private Integer leadsOrder;

    @Trim
    @Schema(title = "客服专员姓名")
    private String customerServiceName;

    @Schema(title = "回访开始日期")
    private LocalDate visitStartDate;

    @Schema(title = "#回访结束时间#", example = "2022-03-31")
    private LocalDate visitEndDate;

    @Schema(title = "#线索注册开始时间#", example = "2022-03-31")
    private LocalDate submitStartDate;

    @Schema(title = "#线索注册结束时间#", example = "2022-03-31")
    private LocalDate submitEndDate;

    @Schema(title = "不下线索原因-一级:0-空白,1-客户原因;2-商户原因;3-平台原因 4-无效数据")
    private Integer firstLeadsFailReasonId;


}
