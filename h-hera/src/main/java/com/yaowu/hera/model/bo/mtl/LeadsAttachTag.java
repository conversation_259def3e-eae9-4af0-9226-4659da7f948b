package com.yaowu.hera.model.bo.mtl;

import com.yaowu.heraapi.enums.mtl.LeadsAttributeBitsEnum;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Set;

/**
 * <AUTHOR>
 * @email <EMAIL>
 * @creatTime 2025.01.07 09:52:00
 * @description:
 */

@Data
@AllArgsConstructor
@NoArgsConstructor
public class LeadsAttachTag {

    private Long leadsInfoId;

    private LeadsAttributeBitsEnum  attribute;


}
