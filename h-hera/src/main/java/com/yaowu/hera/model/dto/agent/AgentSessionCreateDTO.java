package com.yaowu.hera.model.dto.agent;

import io.swagger.annotations.ApiModelProperty;
import jakarta.validation.constraints.NotNull;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2024/7/20 15:28
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class AgentSessionCreateDTO {
    @ApiModelProperty(value = "应用id")
    @NotNull(message = "用户id不能为空")
    private Long appId;

    @ApiModelProperty("用户id")
    @NotNull(message = "用户id不能为空")
    private Long userId;
}
