package com.yaowu.hera.model.bo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.HashMap;
import java.util.Map;

/**
 * 审批结束消息体
 *
 * <AUTHOR> 2023/2/9
 */
@Data
public class ApproveResultBO {

    @Schema(title = "流程实例id")
    private Long processInstanceId;

    @Schema(title = "业务ID")
    private String bizNo;

    @Schema(title = "是否审批通过#true：通过#false：驳回")
    private Boolean pass;

    @Schema(title = "原因")
    private String reason;

    @Schema(title = "扩展信息")
    private
Map<String, Object> extendInfo = new HashMap<>(16);
}
