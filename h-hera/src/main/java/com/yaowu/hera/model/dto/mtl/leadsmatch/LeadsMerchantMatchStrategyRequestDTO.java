package com.yaowu.hera.model.dto.mtl.leadsmatch;

import io.swagger.v3.oas.annotations.media.Schema;
import java.util.Objects;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Set;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2024/12/1
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class LeadsMerchantMatchStrategyRequestDTO extends LeadsMatchStrategyRequestDTO {
    @Schema(description = "指定的门店ID列表")
    private Set<Long> storeIds;

    /**
     * ABCD类的筛选距离，单位米
     */
    @Schema(description = "ABCD类的筛选距离，单位米")
    private Integer abcdDistanceWithin;


    /**
     * ABCD类的匹配数量
     */
    @Schema(description = "ABCD类的匹配数量")
    private Integer abcdCount;

    /**
     * ABCD类的TOP数量
     */
    @Schema(description = "ABCD类的TOP数量")
    private Integer abcdTopCount;

    /**
     * E类的筛选距离，单位米
     */
    @Schema(description = "E类的筛选距离，单位米")
    private Integer eDistanceWithin;

    /**
     * E类的匹配数量
     */
    @Schema(description = "E类的匹配数量")
    private Integer eCount;

    @Schema(description = "是否匹配灰度")
    private Boolean isMatchGray;

    @Schema(description = "是否扩展匹配灰度")
    private Boolean isMatchExpandGray;


    /**
     * 是否扩展匹配灰度
     * @param isMatchExpandGray
     * @param expandDistance
     * @return
     */
    public LeadsMerchantMatchStrategyRequestDTO expandGray(Boolean isMatchExpandGray, Integer expandDistance) {
        if (!Objects.equals(isMatchExpandGray, Boolean.TRUE)) {
            return this;
        }
        this.setIsMatchExpandGray(isMatchExpandGray);
        this.setAbcdDistanceWithin(expandDistance);
        return this;
    }

}
