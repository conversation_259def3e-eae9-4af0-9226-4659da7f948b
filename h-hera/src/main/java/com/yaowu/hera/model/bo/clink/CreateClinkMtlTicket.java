package com.yaowu.hera.model.bo.clink;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.Pattern;
import lombok.Data;
import lombok.experimental.Accessors;

import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @email <EMAIL>
 * @creatTime 2024.09.19 20:53:00
 * @description:
 */

@Data
@Accessors(chain = true)
public class CreateClinkMtlTicket extends BaseCreateClinkTicket {

    /**
     * 线索id
     */
    private String leadsInfoId;

    /**
     * 天润工单id
     */
    private String clinkTicketId;

    /**
     * 地址
     */
    @NotBlank(message = "地址信息不能为空")
    private String address;

    /**
     * 电话
     */
    @NotBlank(message = "电话信息不能为空")
    private String phone;

    /**
     * 设备品类 多级逗号分割 高空设备,直臂车
     */
    @Pattern(regexp = "^[^,]+,[^,]+$", message = "设备品类必须精确到二级品类")
    @NotBlank(message = "设备品类信息不能为空")
    private String categoryName;

    /**
     * 设备扩展参数
     */
    private String deviceParams;

    /**
     * 设备数量
     */
    private String deviceCount;

    /**
     * 清洗结果
     */
    @NotBlank(message = "清洗结果信息不能为空")
    private String ratingResult;

    /**
     * 用车时间
     */
    @NotBlank(message = "用车时间信息不能为空")
    private String serviceTime;

    /**
     * 报价单线索提交时间
     */
    @Schema(title = "报价单线索提交时间")
    private LocalDateTime submitTime;

    /**
     * 使用时长
     */
    @Schema(title = "使用时长")
    private Integer usageDuration;


    /**
     * MTL线索渠道  字典表.bizType=MTL_LEADS_CLINK_CHANNEL
     */
    @Schema(title = "MTL线索渠道")
    private String clinkChannel;

    /**
     * 备注
     */
    private String clinkRemark;

    @Override
    public String getBusinessId() {
        return String.valueOf(leadsInfoId);
    }
}
