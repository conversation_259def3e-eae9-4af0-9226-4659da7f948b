package com.yaowu.hera.model.entity.agent;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2024/7/18 19:22
 */
@Data
public class AgentUserChatMessage {
    @ApiModelProperty("会话请求发送的问题类型，取值范围：text: 标识文本类型 image: 标识图片类型 file: 标识文件类型 multimodal: 标识多模态类型")
    private String type;

    @ApiModelProperty("会话请求发送的文本内容")
    private AgentContentText text;

    @ApiModelProperty("会话请求发送的图片内容")
    private AgentContentImage image;

    @ApiModelProperty("会话请求发送的文件内容")
    private AgentContentFile file;

    @ApiModelProperty("会话请求发送的多模态内容")
    private AgentContentMultimodal multimodal;
}
