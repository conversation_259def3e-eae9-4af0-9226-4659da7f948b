package com.yaowu.hera.model.dto.merchent;

import com.yaowu.hera.enums.merchant.BusinessCheckStatusEnum;
import com.yaowu.hera.enums.merchant.BusinessCheckTypeEnum;
import com.yaowu.hera.model.entity.merchant.BusinessCheckInfoEntity;
import com.yaowu.hera.model.entity.merchant.check.StoreImageCheckContentEntity;
import lombok.Data;

import java.util.Set;

/**
 * <AUTHOR>
 * @date 2024/9/24-18:52
 */
@Data
public class StoreMainImageCheckDTO {

    /**
     * 数据库主键id
     */
    private Long id;

    /**
     * 数据库主键id
     */
    private Set<Long> ids;
    
    /**
     * 审核备注
     */
    private String checkRemark;

    /**
     * 审核状态
     */
    private BusinessCheckStatusEnum checkStatus;
}
