package com.yaowu.hera.model.entity.leads;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.freedom.mybatisplus.handler.CustomJsonTypeHandler;
import com.freedom.web.domain.BaseLogicTable;
import com.yaowu.hera.config.typhandler.CustomizeLocalDateTimeTypeHandler;
import com.yaowu.hera.enums.mtl.BooleanEnum;
import com.yaowu.hera.enums.mtl.LeadsRequirementTypeEnum;
import com.yaowu.heraapi.enums.mtl.SceneCodeEnum;
import com.yaowu.hera.model.bo.leads.LeadsChannelExtBO;
import com.yaowu.hera.model.bo.leads.LeadsEstimateExtBO;
import com.yaowu.hera.utils.constants.Constants;
import com.yaowu.heraapi.enums.device.ChannelTypeEnum;
import com.yaowu.heraapi.enums.mtl.*;
import com.yaowu.heraapi.model.pojo.business.HeraLeadBizParamExt;
import io.swagger.v3.oas.annotations.media.Schema;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@TableName(value = "b_leads_info", autoResultMap = true)
@Schema(title = "LeadsInfo对象")
public class LeadsInfo extends BaseLogicTable {

    public static final Integer VIEWED = 2;
    public static final Integer NOT_VIEW = 1;

    private static final long serialVersionUID = 3757687891411539702L;

    @Schema(title = "提交用户的 userId")
    private Long customerUserId;

    @Schema(title = "设备需求类型：标准设备需求 or 非标特殊需求")
    private LeadsRequirementTypeEnum requirementType;

    @Schema(title = "一级品类ID")
    private Long firstCategoryId;

    @Schema(title = "一级品类名称")
    private String firstCategoryName;

    @Schema(title = "二级品类ID")
    private Long secondCategoryId;

    @Schema(title = "二级品类名称")
    private String secondCategoryName;

    @Schema(title = "一级 SPU ID")
    private Long spuId;

    @Schema(title = "一级 SPU 名称")
    private String spuName;

    @Schema(title = "设备数量类型")
    private LeadsCountTypeEnum countType;

    @Schema(title = "预计开始使用时间，按类型枚举")
    private LeadsStartTypeEnum startType;

    @Schema(title = "预计使用时长，按类型枚举")
    private LeadsDurationTypeEnum durationType;

    @Schema(title = "开始进场时间类型 0.使用时间枚举 1.时间点:使用customStartDate 2.区间customStartDate-customEndDate")
    @TableField(exist = false)
    private Integer startEnterType;

    @Schema(title = "自定义输入开始使用日期")
    @TableField(typeHandler = CustomizeLocalDateTimeTypeHandler.class)
    private LocalDateTime customStartDate;

    @Schema(title = "自定义输入结束使用日期")
    @TableField(typeHandler = CustomizeLocalDateTimeTypeHandler.class)
    private LocalDateTime customEndDate;

    @Schema(title = "需求地址定位类型：1-自动定位获取；2-手动修改；3-未授权；4-IP解析地址")
    private Integer addressDataType;

    @Schema(title = "报价单线索提交时间")
    private LocalDateTime submitTime;

    @Schema(title = "特殊自定义设备信息（非标需求）")
    private String specialRequirements;

    @Schema(title = "额外需求备注（非标需求）")
    private String notes;

    @Schema(title = "当前线索最终分配模式")
    private LeadsDispatchModeEnum dispatchMode;

    @Schema(title = "当前线索分配原因描述")
    private String dispatchDesc;

    @Schema(title = "当前报价单预估金额")
    private BigDecimal estimateAmount;

    @Schema(title = "报价单预估详情")
    @TableField(typeHandler = CustomJsonTypeHandler.class)
    private LeadsEstimateExtBO estimateExt;

    @Schema(title = "LTC模式对应的销售团队：1-电销团队；2-撮合团队")
    private Integer salesTeamType;

    @Schema(title = "LTC模式对应的销售 userId")
    private Long salesUserId;

    @Schema(title = "LTC模式是否命中频控")
    private Boolean rateLimitFlag;

    @Schema(title = "LTC模式对应的 clueTicketId")
    private Long clueTicketId;

    /**
     * @see ChannelTypeEnum
     */
    @Schema(title = "一级渠道代码")
    private String channelTier1;

    @Schema(title = "二级级渠道代码")
    private String channelTier2;

    @Schema(title = "二级渠道扩展信息")
    @TableField(typeHandler = CustomJsonTypeHandler.class)
    private LeadsChannelExtBO channelExt;

    @Schema(title = "微信分享场景值代码")
    private String wxScenario;

    /**
     * @see SceneCodeEnum
     */
    @Schema(title = "前端页面场景码")
    private String sceneCode;

    @Schema(title = "前端场景码对应的业务id（埋点）")
    private Long sceneCodeContentId;

    @Schema(title = "推荐人ID")
    private Long referrerId;

    @Schema(title = "推荐人OpenId")
    private String referrerOpenId;

    @Schema(title = "推荐商户数量")
    private Integer recommendMerchantCount;

    @Schema(title = "是否已过期：0-否、1-是")
    private BooleanEnum expired;

    @Schema(title = "过期时间")
    @TableField(typeHandler = CustomizeLocalDateTimeTypeHandler.class)
    private LocalDateTime expireTime;

    @Schema(title = "线索类型：1-客找商、2-商找客")
    private LeadsTypeEnum leadsType;

    @Schema(title = "关联的来源线索id")
    private Long sourceLeadsInfoId;

    @Schema(title = "企微通知次数")
    private Integer weComNoticedNum;

    @Schema(title = "流程类型：1-A版流程、2-B版流程")
    private Integer processType;

    @Schema(title = "线索扩展信息：品类扩展参数等")
    @TableField(typeHandler = CustomJsonTypeHandler.class)
    private HeraLeadBizParamExt bizExt;

    @Schema(title = "消息通知类型")
    private MessageNotifyTypeEnum msgNotifyType;


    /**
     * {@link LeadsInfo#VIEWED,LeadsInfo#NOT_VIEW}
     */
    @Schema(title = "线索是否被查看 0:未知 1:没有查看, 2:已经查看")
    private Integer leadsViewed;

    @Schema(title = "标准价格")
    private BigDecimal standardPrice;

    @Schema(title = "折扣价格")
    private BigDecimal discountedPrice;

    @Schema(title = "购买上限数量")
    private Integer purchaseMaxNum;

    @Schema(title = "客找商定价")
    private BigDecimal c2bPrice;

    public Integer getStartEnterType() {
        //为空 or 默认值
        if ((this.getCustomStartDate() == null && this.getCustomEndDate() == null)
                || (this.getCustomStartDate().equals(Constants.DEFAULT_DATE_TIME)
                && this.getCustomEndDate().equals(Constants.DEFAULT_DATE_TIME))) {
            return 0;
        }
        //开始时间为空 or 默认值（数据不正常的）
        if (this.getCustomStartDate()==null || this.getCustomStartDate().equals(Constants.DEFAULT_DATE_TIME)) {
            return 0;
        }
        //结束时间为空 or 默认值（走时间点）
        if(this.getCustomEndDate() == null || this.getCustomEndDate().equals(Constants.DEFAULT_DATE_TIME)){
            return 1;
        }

        //结束时间有具体值
        return 2;
    }

}
