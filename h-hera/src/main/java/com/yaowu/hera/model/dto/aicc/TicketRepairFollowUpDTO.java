package com.yaowu.hera.model.dto.aicc;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @since 2023/11/22 20:30
 **/
@Data
@AllArgsConstructor
@NoArgsConstructor
@Schema(title = "TicketRepairFollowUpDTO")
public class TicketRepairFollowUpDTO extends TicketFollowUpBaseDTO {

    @Schema(title = "履约合同编码")
    private String orderContractCode;

    @Schema(title = "销售BD")
    private String saleOrderContractMaitainBdName;

    @Schema(title = "维修BD")
    private String repairBdName;

    @Schema(title = "客户名称")
    private String orderContractSignedTenant;

    @Schema(title = "项目名称")
    private String projectName;

    @Schema(title = "联系号码")
    private String orderContractSignedTenantPhone;

    @Schema(title = "报修工单联系人电话")
    private String repairTicketContactsPhone;

    @Schema(title = "设备品类")
    private String equipmentSpuLevel1Name;

    @Schema(title = "设备维修单编码")
    private String repairWorkOrderCode;

    @Schema(title = "设备机械星球自编码")
    private String equipmentMstarNo;

    @Schema(title = "门店")
    private String merchantStoreName;

    @Schema(title = "设备品牌名称")
    private String equipmentBrandName;

    @Schema(title = "工单处理方式")
    private String repairWorkOrderSolutionType;

    @Schema(title = "设备维修结果备注说明")
    private String repairResultRemark;

    @Schema(title = "设备维修单创建时间")
    private String repairWorkOrderCreateTime;

    @Schema(title = "设备维修工单完成时间")
    private String repairWorkOrderCompleteTime;

    @Schema(title = "备注")
    private String remark;
}
