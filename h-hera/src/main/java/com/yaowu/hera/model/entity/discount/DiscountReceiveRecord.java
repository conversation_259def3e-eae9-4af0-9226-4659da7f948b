package com.yaowu.hera.model.entity.discount;

import com.baomidou.mybatisplus.annotation.TableName;
import com.freedom.web.domain.BaseLogicTable;
import io.swagger.annotations.ApiModel;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.time.LocalDateTime;

/**
 * <p>
 * 兑换码领取记录表
 * </p>
 *
 * <AUTHOR>
 * @since 2024-07-23
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@TableName("b_discount_receive_record")
@ApiModel(value = "DiscountReceiveRecord对象", description = "兑换码领取记录表")
public class DiscountReceiveRecord extends BaseLogicTable {

    @Schema(title ="兑换码id")
    private Long codeId;

    @Schema(title ="领取商户id")
    private Long receiveMerchantId;

    @Schema(title ="领取门店id")
    private Long receiveStoreId;

    @Schema(title ="领取时间")
    private LocalDateTime receiveTime;

    @Schema(title ="领取用户id")
    private Long receiveUserId;

    @Schema(title ="领取位置类型")
    private Integer bannerType;

    @Schema(title ="兑换码类型")
    private Integer codeType;

    @Schema(title ="活动id")
    private Long activityId;
}
