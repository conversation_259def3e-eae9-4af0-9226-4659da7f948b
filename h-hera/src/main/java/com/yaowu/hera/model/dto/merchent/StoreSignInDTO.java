package com.yaowu.hera.model.dto.merchent;

import com.freedom.web.exception.BusinessException;
import com.yaowu.hera.enums.merchant.SignInTypeEnum;
import lombok.Data;

import java.time.LocalDate;
import java.util.Objects;

/**
 * <AUTHOR>
 * @date 2024/9/12-16:06
 */
@Data
public class StoreSignInDTO {

    private Long storeId;

    /**
     * 签到日期
     */
    private LocalDate signInTime;

    /**
     * 签到类型
     */
    private SignInTypeEnum signInType;

    /**
     * 签到人id
     */
    private Long signInUserId;

    public void check() {
        // 判断当前类型
        if (Objects.requireNonNull(signInType) == SignInTypeEnum.MANUAL) {
            if (!LocalDate.now().equals(signInTime)) {
                throw new BusinessException("签到日期非法");
            }
        }
    }
}
