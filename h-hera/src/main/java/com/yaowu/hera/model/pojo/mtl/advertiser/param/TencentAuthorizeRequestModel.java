package com.yaowu.hera.model.pojo.mtl.advertiser.param;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

/**
 * @Author: liuzhenpeng
 * @Date: 2025/1/22 11:59
 */
@Data
public class TencentAuthorizeRequestModel {

    /**
     * 应用回调地址，仅支持 http 和 https，不支持指定端口号，且主域名必须与创建应用时登记的回调域名一致，若地址携带参数，需要对地址进行 urlencode
     * 字段长度最小 1 字节，长度最大 1024 字节
     */
    @Schema(title ="应用回调地址")
    @NotEmpty(message = "应用回调地址不能为空")
    private String redirectUri;

    /**
     * 验证请求有效性参数，值为用户自取，用于阻止跨站请求伪造攻击
     * 字段长度最小 0 字节，长度最大 512 字节
     */
    @Schema(title ="验证请求有效性参数，值为用户自取，用于阻止跨站请求伪造攻击")
    private String state;

    /**
     * 授权范围，可选值：ads_management（广告投放）、ads_insights（数据洞察）、account_management（帐号服务）、audience_management（人群管理）、user_actions（用户行为数据接入），不传即为授权全部权限
     * 字段长度最小 1 字节，长度最大 64 字节
     */
    @Schema(title ="授权范围")
    private String scope;

    /**
     * 授权账号类型，登录账号类型 QQ/微信，[枚举详情]
     * 可选值：{ ACCOUNT_TYPE_WECHAT, ACCOUNT_TYPE_QQ }
     * 默认值：ACCOUNT_TYPE_QQ
     */
    @Schema(title ="授权账号类型")
    private String accountType;
}
