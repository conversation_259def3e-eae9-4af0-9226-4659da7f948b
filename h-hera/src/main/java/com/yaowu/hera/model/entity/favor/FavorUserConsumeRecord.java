package com.yaowu.hera.model.entity.favor;

import com.baomidou.mybatisplus.annotation.TableName;
import com.freedom.web.domain.BaseLogicTable;
import com.yaowu.heraapi.enums.favor.FavorTypeEnum;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <AUTHOR>
 * @since 2023/7/29 14:27
 **/
@Data
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@TableName(value = "b_favor_consume_record", autoResultMap = true)
@Schema(title = "FavorUserConsumeRecord")
public class FavorUserConsumeRecord extends BaseLogicTable {

    @Schema(title = "权益的凭证号，唯一")
    private String couponId;

    @Schema(title = "关联的权益表的id")
    private Long favorId;

    @Schema(title = "关联的权益表, 1-stock代金券表")
    private FavorTypeEnum favorType;
}
