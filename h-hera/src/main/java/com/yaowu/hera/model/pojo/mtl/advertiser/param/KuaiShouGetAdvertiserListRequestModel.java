package com.yaowu.hera.model.pojo.mtl.advertiser.param;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import lombok.Builder;
import lombok.Data;

/**
 * @Author: liuzhenpeng
 * @Date: 2025/2/7 19:53
 */
@Data
@Builder
public class KuaiShouGetAdvertiserListRequestModel {

    @Schema(title ="分页页码，必填")
    @NotNull(message = "分页页码不能为空")
    private Integer pageNo;

    @Schema(title ="分页每页展示条数，必填，最大值为200")
    @NotNull(message = "分页每页展示条数不能为空")
    private Integer pageSize;
}
