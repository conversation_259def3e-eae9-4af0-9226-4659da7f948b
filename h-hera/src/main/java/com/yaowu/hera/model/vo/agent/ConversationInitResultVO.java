package com.yaowu.hera.model.vo.agent;

import com.yaowu.hera.model.vo.llm.AgentSuggestedQuestionVO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2024/7/20 14:53
 */
@Data
public class ConversationInitResultVO {
    @ApiModelProperty("应用id")
    private String agentAppId;

    @ApiModelProperty("用户id")
    private String userId;

    @ApiModelProperty("会话id")
    private Long agentSessionId;

    @ApiModelProperty("客服id")
    private Long customerServiceId;

    @ApiModelProperty("客服头像URL")
    private String customerServiceName;

    @ApiModelProperty("客服工号")
    private String customerServiceStaffNumber;

    @ApiModelProperty("客服头像URL")
    private String customerServiceAvatar;

    @ApiModelProperty("最近的聊天消息列表")
    private List<AgentChatMessageVO> recentChatMessages;

    @ApiModelProperty("推荐问题列表")
    private List<AgentSuggestedQuestionVO> suggestedQuestions;
}
