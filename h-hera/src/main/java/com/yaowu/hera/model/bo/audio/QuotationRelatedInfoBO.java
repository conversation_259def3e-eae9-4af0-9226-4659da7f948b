package com.yaowu.hera.model.bo.audio;

import cn.hutool.core.collection.CollectionUtil;
import com.yaowu.hera.model.entity.audio.TranscribedBizTraceData;
import com.yaowu.hera.model.entity.leads.LeadsDeviceCategory;
import com.yaowu.hera.model.entity.leads.LeadsInfo;
import com.yaowu.hera.model.entity.put.PutUnit;
import com.yaowu.hera.model.vo.clue.MelinaStorePageVO;
import com.yaowu.hera.utils.StreamUtil;
import com.yaowu.hera.utils.StringUtil;
import com.yaowu.heraapi.model.pojo.common.AreaAddressModel;
import com.yaowu.notice.model.vo.RemotePrivateNumberCallRecodeVO;
import com.yaowu.passportapi.model.vo.user.UserInfoVO;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.util.CollectionUtils;

import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class QuotationRelatedInfoBO {
    @ApiModelProperty(value = "线索报价单信息")
    private List<LeadsInfo> leadsInfoList;

    @ApiModelProperty(value = "远程电话回访记录")
    private List<RemotePrivateNumberCallRecodeVO> privateNumberCallRecords;

    @ApiModelProperty(value = "通话录音信息转录数据")
    private List<TranscribedBizTraceData> transcribedBizTraceDataList;

    @ApiModelProperty(value = "线索报价单用车地址信息")
    private Map<Long, AreaAddressModel> leadsInfoAreaAddressMap;

    @ApiModelProperty(value = "线索报价单客户信息")
    Map<Long, UserInfoVO> bizUserIdToPhone;

    @ApiModelProperty(value = "通话录音信息合并后的转录数据")
    private List<TranscribedBizTraceData> mergedStructuredDataList;

    @ApiModelProperty(value = "通话录音信息合并后的转录数据")
    private Map<String, MelinaStorePageVO> subIdToStoreInfo;

    @ApiModelProperty(value = "线索报价单设备信息")
    private Map<Long, LeadsDeviceCategory> leadsCategoryInfo;

    @ApiModelProperty(value = "渠道信息")
    private List<PutUnit> putUnitList;

    public Map<Long, List<RemotePrivateNumberCallRecodeVO>> getLeadsId2StructCallLogs() {
        if (CollectionUtil.isEmpty(leadsInfoList) || CollectionUtil.isEmpty(privateNumberCallRecords) ||
                CollectionUtils.isEmpty(transcribedBizTraceDataList)) {
            return Collections.emptyMap();
        }
        List<Long> privateNumberFeeIds = StreamUtil.of(transcribedBizTraceDataList)
                .filter(e -> Objects.nonNull(e) && StringUtil.isNotBlank(e.getAudioFileUrl()))
                .map(TranscribedBizTraceData::getPrivateNumberFeeId)
                .toList();
        return StreamUtil.of(privateNumberCallRecords)
                .filter(leadsInfo -> privateNumberFeeIds.contains(leadsInfo.getId()))
                .collect(Collectors.groupingBy(RemotePrivateNumberCallRecodeVO::getBizContentId));
    }

    public Map<Long, TranscribedBizTraceData> getNumberFeeIdToBusData() {
        return StreamUtil.of(transcribedBizTraceDataList)
                .collect(Collectors.toMap(TranscribedBizTraceData::getPrivateNumberFeeId, Function.identity()));
    }

    /**
     * 获取提取到机构化录音数据的线索报价单集合
     */
    public List<LeadsInfo> findQuoteLeadsWithStructData() {
        if (CollectionUtil.isEmpty(leadsInfoList) || CollectionUtil.isEmpty(privateNumberCallRecords) ||
                CollectionUtils.isEmpty(transcribedBizTraceDataList)) {
            return Collections.emptyList();
        }
        List<Long> bizContentIds = StreamUtil.of(transcribedBizTraceDataList)
                .filter(e -> Objects.nonNull(e) && StringUtil.isNotBlank(e.getAudioFileUrl()))
                .map(TranscribedBizTraceData::getBizId)
                .toList();
        return StreamUtil.of(leadsInfoList)
                .filter(leadsInfo -> bizContentIds.contains(leadsInfo.getId()))
                .collect(Collectors.toList());
    }

    public Map<Long, TranscribedBizTraceData> getLeadsId2TransData() {
        if (CollectionUtil.isEmpty(privateNumberCallRecords) || CollectionUtil.isEmpty(mergedStructuredDataList)) {
            return Collections.emptyMap();
        }
        //存在对应话单URL并且URL的数量大于2时才将对应的数据导出
        Map<Long, List<RemotePrivateNumberCallRecodeVO>> bizIdToCallLogs = getLeadsId2StructCallLogs();
        return StreamUtil.of(mergedStructuredDataList)
                .filter(e -> {
                    List<RemotePrivateNumberCallRecodeVO> callLogs = bizIdToCallLogs.get(e.getBizId());
                    return callLogs != null && callLogs.size() > 1;
                })
                .collect(Collectors.toMap(TranscribedBizTraceData::getBizId, Function.identity()));
    }


    public Map<Long, TranscribedBizTraceData> getSingleLeadsId2TransData() {
        if (CollectionUtil.isEmpty(privateNumberCallRecords) || CollectionUtil.isEmpty(mergedStructuredDataList)) {
            return Collections.emptyMap();
        }
        //针对单条话单导出的数据
        Map<Long, List<RemotePrivateNumberCallRecodeVO>> bizIdToCallLogs = getLeadsId2StructCallLogs();
        return StreamUtil.of(mergedStructuredDataList)
                .filter(e -> {
                    List<RemotePrivateNumberCallRecodeVO> callLogs = bizIdToCallLogs.get(e.getBizId());
                    return CollectionUtil.isNotEmpty(callLogs);
                })
                .collect(Collectors.toMap(TranscribedBizTraceData::getBizId, Function.identity()));
    }

    public List<RemotePrivateNumberCallRecodeVO> getStructCallRecordsByLeadsId(Long leadsInfoId) {
        return getLeadsId2StructCallLogs().get(leadsInfoId);
    }

    public TranscribedBizTraceData getMergedTransDataByLeadsId(Long leadsInfoId) {
        return getLeadsId2TransData().get(leadsInfoId);
    }

    public TranscribedBizTraceData getSingleMergedTransDataByLeadsId(Long leadsInfoId) {
        return getSingleLeadsId2TransData().get(leadsInfoId);
    }

    public MelinaStorePageVO getStoreInfoBySubId(String subscriptionId) {
        return getSubIdToStoreInfo().get(subscriptionId);
    }

    public TranscribedBizTraceData getTransDataByCallRecordId(Long callRecordId) {
        return  getNumberFeeIdToBusData().get(callRecordId);
    }
}

