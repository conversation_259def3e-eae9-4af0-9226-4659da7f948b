package com.yaowu.hera.model.entity.common;

import com.baomidou.mybatisplus.annotation.TableName;
import com.freedom.biz.common.enums.BizGroupEnum;
import com.freedom.biz.common.enums.BizTypeEnum;
import com.freedom.web.domain.BaseLogicTable;
import com.yaowu.heraapi.enums.common.AddressBookTypeEnum;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p>
 * 地址薄表
 * </p>
 *
 * <AUTHOR>
 * @since 2023-02-16
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@TableName("b_address_book")
@Schema(title = "AddressBook对象")
public class AddressBook extends BaseLogicTable {

    private static final long serialVersionUID = 1L;

    @Schema(title = "业务组，1：设备租赁和服务，2：创新业务")
    private BizGroupEnum bizGroup;

    @Schema(title = "业务类型，1：转租模式，2：居间模式，3：加盟模式，4：低价供给")
    private BizTypeEnum bizType;

    @Schema(title = "业务id")
    private Long bizId;

    @Schema(title = "地址类型，1：商机客户地址")
    private AddressBookTypeEnum type;

    @Schema(title = "地址-省code")
    private String provinceCode;

    @Schema(title = "地址-省名称")
    private String provinceName;

    @Schema(title = "地址-市code")
    private String cityCode;

    @Schema(title = "地址-市名称")
    private String cityName;

    @Schema(title = "地址-区code")
    private String districtCode;

    @Schema(title = "地址-区名称")
    private String districtName;

    @Schema(title = "地址")
    private String address;

    @Schema(title = "地址标题")
    private String addressTitle;

    @Schema(title = "地址经度")
    private String lng;

    @Schema(title = "地址纬度")
    private String lat;

    @Schema(title = "完整地址（人工输入）")
    private String fullAddress;

}

