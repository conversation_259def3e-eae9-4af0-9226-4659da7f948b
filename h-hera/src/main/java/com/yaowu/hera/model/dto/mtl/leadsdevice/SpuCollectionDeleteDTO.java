package com.yaowu.hera.model.dto.mtl.leadsdevice;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Schema(description = "删除SPU合集请求参数")
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class SpuCollectionDeleteDTO {

    @Schema(description = "SPU合集ID", required = true)
    @NotNull(message = "SPU合集ID不能为空")
    private Long spuCollectionId;
}
