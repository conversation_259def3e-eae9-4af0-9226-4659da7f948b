package com.yaowu.hera.model.bo.mtl;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2024/12/20 15:27
 */
@Data
public class AiCallTimeoutResultBO {
    @Schema(title = "线索信息id")
    private Long leadsInfoId;

    @Schema(title = "是否触发外呼是否成功")
    private boolean isAiCallTriggered;

    @Schema(title = "用户id")
    private Long userId;

    @Schema(title = "手机号")
    private String phone;
}
