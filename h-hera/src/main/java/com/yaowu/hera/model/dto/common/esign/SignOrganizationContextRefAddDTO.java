package com.yaowu.hera.model.dto.common.esign;

import com.yaowu.hera.enums.common.esign.SignPlatformTypeEnum;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import jakarta.validation.Valid;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;

/**
 * @author: liuzhenpeng 2022/1/26 13:35
 */
@Data
@Schema(title = "SignOrganizationContextRefAddDTO-请求参数")
public class SignOrganizationContextRefAddDTO {

    @Schema(title = "#对接方业务上下文id#")
    @NotNull(message = "对接方业务上下文id不能为空")
    private Long contextId;

    @Schema(title = "#平台类型#ENUM#0:ESIGN:e签宝,1::上上签#")
    @NotNull(message = "平台类型不能为空")
    private SignPlatformTypeEnum platformType;

    @Valid
    @Schema(title = "#企业信息#")
    @NotNull(message = "企业信息不能为空")
    private EsignOrganizationInfoAddDTO orgInfo;

    @Schema(title = "回调通知地址", required = true)
    @NotEmpty(message = "回调通知地址不能为空")
    private String notifyUrl;
}
