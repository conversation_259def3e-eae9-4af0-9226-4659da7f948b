package com.yaowu.hera.model.entity.leads;

import com.baomidou.mybatisplus.annotation.TableName;
import com.freedom.web.domain.BaseLogicTable;
import com.yaowu.heraapi.enums.mtl.RemoteTaskSourceEnum;
import com.yaowu.heraapi.enums.mtl.RemoteTaskStatusEnum;
import com.yaowu.heraapi.enums.mtl.RemoteTaskTypeEnum;
import io.swagger.v3.oas.annotations.media.Schema;
import java.time.LocalDateTime;
import lombok.Data;

/**
 * <p>
 * 线索跟进提醒任务表
 * </p>
 *
 * <AUTHOR>
 * @since 2024-11-22
 */
@Data
@TableName("b_leads_remind_task")
@Schema(title = "LeadsRemindTask对象", description = "线索跟进提醒任务表")
public class LeadsRemindTask extends BaseLogicTable {

    private static final long serialVersionUID = 1L;

    @Schema(title = "报价单线索id")
    private Long leadsInfoId;

    @Schema(title = "线索对应门店id，商找客会绑定线索，客找商联系后会关联上")
    private Long storeId;

    @Schema(title = "提醒时间")
    private LocalDateTime remindTime;

    @Schema(title = "提醒类型：1-首页卡片")
    private RemoteTaskTypeEnum taskType;

    @Schema(title = "提醒状态：1-有效、2-无效")
    private RemoteTaskStatusEnum taskStatus;

    @Schema(title = "提醒任务来源：1-未拨打的线索、2-商户拨打过1次但未接通的线索、3-客商之间最近一次话单为未接的客户来电的线索")
    private RemoteTaskSourceEnum taskSource;

    @Schema(title = "任务开始有效时间")
    private LocalDateTime effectiveStartTime;

    @Schema(title = "任务截止有效时间")
    private LocalDateTime effectiveEndTime;

    @Schema(title = "任务完结时间")
    private LocalDateTime finishTime;

    @Schema(title = "话单id")
    private Long hwPnFeeAggregateId;


}
