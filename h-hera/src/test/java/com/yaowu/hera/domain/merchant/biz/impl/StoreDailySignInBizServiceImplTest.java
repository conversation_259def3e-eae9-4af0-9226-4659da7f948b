package com.yaowu.hera.domain.merchant.biz.impl;

import com.yaowu.hera.domain.merchant.biz.IStoreDailySignInBizService;
import jakarta.annotation.Resource;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;

import static org.junit.jupiter.api.Assertions.*;

/**
 * @author: huangyuheng
 * @date: 2024-10-25 17:28
 * @desc:
 */
@SpringBootTest
class StoreDailySignInBizServiceImplTest {

    @Resource
    private IStoreDailySignInBizService storeDailySignInBizService;

    @Test
    void storeSignIn() {
    }

    @Test
    void isSignInToday() {
    }

    @Test
    void isSignInByDays() {
    }

    @Test
    void listByCondition() {
    }

    @Test
    void getStoreRecentContinueSignInCount() {
        Integer storeRecentContinueSignInCount = storeDailySignInBizService.getStoreRecentContinueSignInCount(1L);
        System.out.println(storeRecentContinueSignInCount);
    }
}