package com.yaowu.hera.domain.mtl.card;

import com.yaowu.hera.HeraApplicationTest;
import com.yaowu.hera.domain.mtl.card.service.batis.service.ICardPackageOrderRefundService;
import com.yaowu.hera.model.entity.mtl.card.CardPackageOrderRefund;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.test.annotation.Rollback;
import org.springframework.transaction.annotation.Transactional;

/**
 * <AUTHOR>
 * @email <EMAIL>
 * @creatTime 2025.01.23 19:50:00
 * @description:
 */


public class ICardPackageOrderRefundServiceTest extends HeraApplicationTest {
	@Autowired
	ICardPackageOrderRefundService mtlCardPackageOrderRefundService;

	@Test
	@Transactional
	@Rollback
	void testCrud() {
		CardPackageOrderRefund mtlCardPackageOrderRefund = new CardPackageOrderRefund();
		Assertions.assertTrue(mtlCardPackageOrderRefundService.save(mtlCardPackageOrderRefund));
		Assertions.assertNotNull(mtlCardPackageOrderRefundService.getById(mtlCardPackageOrderRefund.getId()));
	}
}
