package com.yaowu.hera.domain.content.biz;

import cn.hutool.json.JSONUtil;
import com.freedom.toolscommon.utils.JacksonUtils;
import com.yaowu.heraapi.model.dto.content.RemoteEditContentFlowDTO;
import com.yaowu.heraapi.model.dto.content.RemoteRecommendedPageConditionDTO;
import com.yaowu.heraapi.model.vo.content.RemoteContentSimpleVO;
import com.yaowu.heraapi.model.vo.content.RemoteRecommendContentPageVO;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;

@SpringBootTest
public class IContentFlowPostBizServiceTest {

    @Autowired
    private IContentFlowPostBizService contentFlowPostBizService;

    @Test
    void getRecommendedPageByConditionV2() {
        RemoteRecommendedPageConditionDTO remoteRecommendedPageConditionDTO = new RemoteRecommendedPageConditionDTO();
        remoteRecommendedPageConditionDTO.setPage(1);
        remoteRecommendedPageConditionDTO.setSize(8);
        RemoteRecommendContentPageVO recommendedPageByConditionV2 =
                contentFlowPostBizService.getRecommendedPageByConditionV2(remoteRecommendedPageConditionDTO);
        System.out.println(JSONUtil.toJsonStr(recommendedPageByConditionV2));
    }

    @Test
    void test_getRandomContentWithComments() {
        RemoteContentSimpleVO oneRandomRecommendContent = contentFlowPostBizService.getOneRandomRecommendContent();
        System.out.println(JSONUtil.toJsonStr(oneRandomRecommendContent));
        Assertions.assertNotNull(oneRandomRecommendContent);
    }

    @Test
    void testedit() {
        String dto = "{\"title\":\"333\",\"content\":\"ZZZ\\nXXX\\nCCC\",\"postStatus\":\"NORMAL\",\"postType\":\"IMAGE\",\"postPushType\":null,\"coverImage\":{\"fileId\":null," +
                "\"fileName\":\"a1ae62f4158a06e835e51ffcdd322b6d.JPG\",\"url\":\"biz-common-offline/h-crm/common/file/048924e8a13a31e6d80a885721a92494.JPG\",\"extension\":\"JPG\"," +
                "\"fileSize\":\"416807\",\"height\":1280,\"width\":1707},\"images\":[{\"fileId\":null,\"fileName\":\"a1ae62f4158a06e835e51ffcdd322b6d.JPG\"," +
                "\"url\":\"biz-common-offline/h-crm/common/file/79eac006ec5c5d28fed336adc32505e5.JPG\",\"extension\":\"JPG\",\"fileSize\":\"416807\",\"height\":1280,\"width\":1707}," +
                "{\"fileId\":null,\"fileName\":\"<EMAIL>\",\"url\":\"biz-common-offline/h-crm/common/file/1e34faa579e02e56e447a8d443084812.png\",\"extension\":\"png\"," +
                "\"fileSize\":\"416194\",\"height\":608,\"width\":476}],\"videos\":null,\"contentTagIds\":[\"1876522773364318209\"],\"recommendCategoryIds\":null," +
                "\"operatorId\":\"1657940372961980417\",\"operatorName\":\"jxxqadmin\",\"displaySide\":null,\"id\":\"1876522558309769217\"}";
        RemoteEditContentFlowDTO remoteEditContentFlowDTO = JacksonUtils.jsonStrToObject(dto, RemoteEditContentFlowDTO.class);
        System.out.println(contentFlowPostBizService.editContentFlow(remoteEditContentFlowDTO));
    }
}