package com.yaowu.hera.domain.mtl.leadsclean.biz.impl.processor.impl.cleanstage.impl;

import com.yaowu.hera.api.v1.common.ApiTest;
import com.yaowu.hera.domain.leads.service.batis.service.ILeadsInfoService;
import com.yaowu.hera.domain.mtl.leadsclean.biz.impl.processor.impl.common.LeadsCleanContext;
import com.yaowu.hera.domain.mtl.leadsclean.service.batis.service.IValueLeadsConfigItemService;
import com.yaowu.hera.domain.mtl.leadsclean.service.batis.service.IValueLeadsConfigService;
import com.yaowu.hera.enums.mtl.LeadsCleanStageEnum;
import com.yaowu.hera.enums.mtl.LeadsDispatchActionEnum;
import com.yaowu.hera.enums.mtl.RentalTypeEnum;
import com.yaowu.hera.model.dto.mtl.leadsclean.LeadsStageCleanRequestDTO;
import com.yaowu.hera.model.entity.leads.LeadsInfo;
import com.yaowu.hera.model.entity.mtl.leadsclean.ValueLeadsConfig;
import com.yaowu.hera.model.entity.mtl.leadsclean.ValueLeadsConfigItem;
import com.yaowu.hera.model.vo.mtl.LeadsStageCleanResultVO;
import com.yaowu.heraapi.enums.mtl.LeadsCleanStageResultEnum;
import com.yaowu.heraapi.model.pojo.business.HeraLeadBizParamExt;
import com.yaowu.heraapi.model.pojo.common.AreaAddressModel;
import jakarta.annotation.Resource;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.time.LocalTime;

import static org.junit.jupiter.api.Assertions.*;

@Transactional
class HighValueLeadCleanStageTest extends ApiTest {
    @Resource
    private HighValueLeadCleanStage highValueLeadCleanStage;
    @Resource
    private IValueLeadsConfigService valueLeadsConfigService;
    @Resource
    private IValueLeadsConfigItemService valueLeadsConfigItemService;
    @Resource
    private ILeadsInfoService leadsInfoService;

    private ValueLeadsConfig valueLeadsConfig;
    private ValueLeadsConfigItem valueLeadsConfigItem;
    private LeadsInfo leadsInfo;
    private LeadsCleanContext context;

    @BeforeEach
    void setUp() {
        // 准备基础配置数据
        valueLeadsConfig = new ValueLeadsConfig();
        valueLeadsConfig.setMinAmount(new BigDecimal("10000"));
        valueLeadsConfig.setOnlineSalesStartTime(LocalTime.of(9, 0));
        valueLeadsConfig.setOnlineSalesEndTime(LocalTime.of(18, 0));
        valueLeadsConfigService.save(valueLeadsConfig);

        // 准备设备配置数据
        valueLeadsConfigItem = new ValueLeadsConfigItem();
        valueLeadsConfigItemService.save(valueLeadsConfigItem);

        // 准备线索数据
        leadsInfo = new LeadsInfo();
        leadsInfo.setId(1L);
        leadsInfo.setSecondCategoryId(1L);
        leadsInfo.setSubmitTime(LocalTime.of(10, 0).atDate(java.time.LocalDate.now()));
        leadsInfoService.save(leadsInfo);

        // 准备上下文
        context = new LeadsCleanContext();
        context.setLeadsInfo(leadsInfo);
        context.setLeadsAddress(new AreaAddressModel());
    }

    @Test
    void process() {
        // 准备请求数据
        LeadsStageCleanRequestDTO requestDTO = new LeadsStageCleanRequestDTO();
        requestDTO.setLeadsInfoId(leadsInfo.getId());
        requestDTO.setCleanStageType(LeadsCleanStageEnum.HIGHER_VALUE_LEADS_CLEAN.getCode());

        // 测试高价值线索场景
        HeraLeadBizParamExt bizExt = new HeraLeadBizParamExt();
        HeraLeadBizParamExt.DeviceParam quantityParam = new HeraLeadBizParamExt.DeviceParam();
        quantityParam.setName("quantity");
        quantityParam.setValue("10");
        HeraLeadBizParamExt.DeviceParam rentalTypeParam = new HeraLeadBizParamExt.DeviceParam();
        rentalTypeParam.setName("rentalType");
        rentalTypeParam.setValue(String.valueOf(RentalTypeEnum.BY_MONTH.getCode()));
        HeraLeadBizParamExt.DeviceParam usageDurationParam = new HeraLeadBizParamExt.DeviceParam();
        usageDurationParam.setName("usageDuration");
        usageDurationParam.setValue("12");
        bizExt.setDeviceParams(java.util.Arrays.asList(quantityParam, rentalTypeParam, usageDurationParam));
        leadsInfo.setBizExt(bizExt);

        LeadsStageCleanResultVO result = highValueLeadCleanStage.process(requestDTO, context);
        assertEquals(LeadsCleanStageResultEnum.END.getCode(), result.getCleanResultStatus());
        assertTrue(result.getCleanResults().contains(LeadsDispatchActionEnum.TO_ONLINE_SALES.getCode()));
    }

    @Test
    void processWithLowValue() {
        // 准备请求数据
        LeadsStageCleanRequestDTO requestDTO = new LeadsStageCleanRequestDTO();
        requestDTO.setLeadsInfoId(leadsInfo.getId());
        requestDTO.setCleanStageType(LeadsCleanStageEnum.HIGHER_VALUE_LEADS_CLEAN.getCode());

        // 测试低价值线索场景
        HeraLeadBizParamExt bizExt = new HeraLeadBizParamExt();
        HeraLeadBizParamExt.DeviceParam quantityParam = new HeraLeadBizParamExt.DeviceParam();
        quantityParam.setName("quantity");
        quantityParam.setValue("1");
        HeraLeadBizParamExt.DeviceParam rentalTypeParam = new HeraLeadBizParamExt.DeviceParam();
        rentalTypeParam.setName("rentalType");
        rentalTypeParam.setValue(String.valueOf(RentalTypeEnum.BY_DAY.getCode()));
        HeraLeadBizParamExt.DeviceParam usageDurationParam = new HeraLeadBizParamExt.DeviceParam();
        usageDurationParam.setName("usageDuration");
        usageDurationParam.setValue("1");
        bizExt.setDeviceParams(java.util.Arrays.asList(quantityParam, rentalTypeParam, usageDurationParam));
        leadsInfo.setBizExt(bizExt);

        LeadsStageCleanResultVO result = highValueLeadCleanStage.process(requestDTO, context);
        assertEquals(LeadsCleanStageResultEnum.CONTINUE.getCode(), result.getCleanResultStatus());
        assertTrue(result.getCleanResults() == null || result.getCleanResults().isEmpty());

    }
}