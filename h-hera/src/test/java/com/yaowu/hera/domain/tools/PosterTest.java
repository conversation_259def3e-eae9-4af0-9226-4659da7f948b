package com.yaowu.hera.domain.tools;

import com.freedom.objectstorage.FileUploadResultVO;
import com.freedom.toolscommon.utils.JacksonUtils;
import com.google.common.collect.Sets;
import com.yaowu.hera.domain.tools.biz.IMerchantSettleInToolsBizService;
import com.yaowu.hera.utils.constants.PushConstants;
import com.yaowu.heraapi.model.dto.tools.RemoteGeneratePosterDTO;
import com.yaowu.heraapi.model.vo.tools.RemoteGeneratePosterVO;
import com.yaowu.notice.constant.enums.ChannelEnum;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;

import java.util.HashMap;
import java.util.Map;
import java.util.Set;

/**
 * @Author: liuzhenpeng
 * @Date: 2025/2/14 17:37
 */
@SpringBootTest
@Slf4j
public class PosterTest {
    @Resource
    private IMerchantSettleInToolsBizService iMerchantSettleInToolsBizService;

    /**
     * 发送客找商app消息
     */
    @Test
    public void generatePosterTest() {
        RemoteGeneratePosterDTO dto = new RemoteGeneratePosterDTO();
        dto.setPosterId(1L);
        RemoteGeneratePosterVO vo = iMerchantSettleInToolsBizService.generatePoster(dto);
        log.info("生成的海报：{}", JacksonUtils.toJsonStr(vo));
    }
}
