package com.yaowu.hera.domain.leads.service.batis.service.impl;

import com.yaowu.hera.domain.leads.service.batis.service.ILeadsOrderInfoService;
import com.yaowu.hera.model.entity.leads.LeadsOrderInfo;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;

import java.util.HashSet;
import java.util.List;
import java.util.Set;

import static org.junit.jupiter.api.Assertions.*;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2025/1/1
 */
@SpringBootTest
class LeadsOrderInfoServiceImplTest {

    @Autowired
    private ILeadsOrderInfoService leadsOrderInfoService;

    @Test
    void listStoreTodayOrderAmountSummaries() {
        Set<Long> storeIds = Set.of(1810910574820933634L, 1812692782015401985L);
        List<LeadsOrderInfo> leadsOrderInfos = leadsOrderInfoService.listStoreTodayOrderAmountSummaries(storeIds);
        System.out.println(leadsOrderInfos);
    }
}