package com.yaowu.hera.domain.visit.biz;

import com.freedom.web.model.resp.BasePage;
import com.yaowu.hera.HeraApplication;
import com.yaowu.hera.HeraApplicationTest;
import com.yaowu.heraapi.model.dto.mtl.visit.*;
import com.yaowu.heraapi.model.vo.visit.RemotePhoneVisitRecordVO;
import com.yaowu.heraapi.model.vo.visit.RemoteRegistrationVisitRecordVO;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;

import java.util.ArrayList;
import java.util.List;


@Slf4j
@SpringBootTest(classes = {HeraApplication.class})
class VisitBizServiceTest extends HeraApplicationTest {

    @Autowired
    private IVisitBizService visitBizService;

    @Test
    void pageRegistration() {
        RemoteRegistrationVisitRecordPageDTO dto = new RemoteRegistrationVisitRecordPageDTO();
        dto.setPhone("13500000000");
        dto.setLeadsType(1);
        BasePage<RemoteRegistrationVisitRecordVO> page = visitBizService.pageRegistrationVisitRecord(dto);
        pagePhone();
    }

    @Test
    void pagePhone() {
        RemotePhoneVisitRecordPageDTO dto = new RemotePhoneVisitRecordPageDTO();
        BasePage<RemotePhoneVisitRecordVO> page = visitBizService.pagePhoneVisitRecord(dto);
        System.out.println(page.getSize());
    }

    @Test
    void addPhoneVisitRecord() {
        RemoteAddPhoneVisitDTO dto = new RemoteAddPhoneVisitDTO();
        dto.setVisitDate("2023-05-18");
        dto.setSubmitDate("2023-05-19");
        dto.setLeadsType(1);
        dto.setSubmitTimeLast("2023-05-18 11:11:11");
        dto.setPhone("13500000000");
        dto.setChannel1("渠道一");
        dto.setChannel2("渠道二");
        dto.setChannel3("渠道三");
        dto.setCallBackStatus(2);
        dto.setUserIdentity(1);
        dto.setLeadsOrder(0);
        dto.setCallBackResult("未接通");
        dto.setCustomerServiceName("客服1");
        visitBizService.addPhoneVisitRecord(dto);
        System.out.println(true);
    }

    @Test
    void importPhoneVisitRecord() {
        List<RemotePhoneTransactionVisitImportDTO> datas = new ArrayList<>();
        RemotePhoneTransactionVisitImportDTO data = new RemotePhoneTransactionVisitImportDTO();
        data.setLeadsType("商找客");
        data.setSubmitDate("2023-05-12");
        data.setSubmitTimeLast("2023-05-18 11:11:11");
        data.setPhone("13500000002");
        data.setChannel1("渠道一");
        data.setChannel2("渠道二");
        data.setChannel3("渠道三");
        data.setCallBackStatus("3次均未接通");
        data.setUserIdentity("未明确");
        data.setCallBackResult("未接通");
        data.setCustomerServiceName("客服1");
        data.setVisitDate("2023-05-18");
        data.setFirstDefeatReason("hhh");
        data.setSecondDefeatReason("未成交");
        data.setMerchantId(1L);
        data.setSceneUse("是");
        data.setExchangeConnectWay("有交换");
        data.setCustomerServiceVerifies("未明确");
        data.setMimiVerifies("未明确");
        data.setFinalTransactionStatus("未明确");
        datas.add(data);
        visitBizService.importPhoneTransactionVisitRecordBatch(datas);
        System.out.println(true);
    }

    @Test
    void importPhone() {
        List<RemotePhoneVisitRecordImportDataDTO> datas = new ArrayList<>();
        RemotePhoneVisitRecordImportDataDTO data = new RemotePhoneVisitRecordImportDataDTO();
        data.setLeadsType("商找客");
        data.setCallClassification("意向客户");
        data.setSubmitDate("2023-05-18");
        data.setSubmitTimeLast("2023-05-18 11:11:11");
        data.setPhone("13500000040");
        data.setChannel1("渠道一");
        data.setChannel2("渠道二");
        data.setChannel3("渠道三");
        data.setCallBackStatus("3次均未接通");
        data.setUserIdentity("未明确");
        data.setCallBackResult("未接通");
        data.setCustomerServiceName("客服1");
        data.setLeadId("11111");
        data.setVisitDate("2023-05-18");
        data.setLeadsOrder("否");
        data.setFirstLeadsFailReason("平台原因");
        data.setSecondLeadsFailReason("未成交");
        datas.add(data);
        visitBizService.importPhoneVisitRecord(datas);

    }

    @Test
    void importRegistrationVisitRecord() {
        List<RemoteRegistrationVisitImportDTO> datas = new ArrayList<>();
        RemoteRegistrationVisitImportDTO data = new RemoteRegistrationVisitImportDTO();
        data.setLeadsType("商找客");
        data.setRegistrationDate("2023-05-18");
        data.setPhone("13500000000");
        data.setChannel1("渠道一");
        data.setChannel2("渠道二");
        data.setChannel3("渠道三");
        data.setCallBackStatus("3次均未接通");
        data.setUserIdentity("未明确");
        data.setLeadsOrder("是");
        data.setCallBackResult("未接通");
        data.setCustomerServiceName("客服1");
        data.setVisitDate("2023-05-18");
        datas.add(data);
        visitBizService.importRegistrationVisitRecord(datas);
        System.out.println(true);
    }

    @Test
    void addRegistrationVisitRecord() {
        RemoteAddRegistrationVisitImportDTO addDTO = new RemoteAddRegistrationVisitImportDTO();
        addDTO.setLeadsType(1);
        addDTO.setCallClassification(2);
        addDTO.setPeriod(1);
        addDTO.setKeyUser(1);
        addDTO.setRegistrationDate("2023-05-18");
        addDTO.setPhone("13500000001");
        addDTO.setChannel1("渠道一");
        addDTO.setChannel2("渠道二");
        addDTO.setChannel3("渠道三");
        addDTO.setCallBackStatus(2);
        addDTO.setUserIdentity(2);
        addDTO.setLeadsOrder(1);
        addDTO.setCallBackResult("未接通");
        addDTO.setCustomerServiceName("客服1");
        addDTO.setVisitDate("2023-05-18");
        addDTO.setFirstLeadsFailReasonId(2);
        addDTO.setSecondLeadsFailReasonDesc("hhh");

        visitBizService.addRegistrationVisitRecord(addDTO);
        addDTO.setPhone("13500000000");
        visitBizService.addRegistrationVisitRecord(addDTO);
        System.out.println(true);
    }
}