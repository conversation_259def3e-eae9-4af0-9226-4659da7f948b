package com.yaowu.hera.domain.feign;

import com.freedom.web.model.resp.BasePage;
import com.yaowu.hera.model.vo.clue.MelinaStorePageVO;
import com.yaowu.melinaapi.model.dto.common.DistanceQueryModel;
import com.yaowu.melinaapi.model.dto.store.RemoteMatchStorePageDTO;
import com.yaowu.melinaapi.model.dto.store.RemoteMelinaStorePageDTO;
import com.yaowu.melinaapi.model.dto.store.RemoteQueryStoreByPageDTO;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;

import java.util.Set;

import static org.junit.jupiter.api.Assertions.*;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2025/1/1
 */
@SpringBootTest
class MelinaFeignBizServiceTest {

    @Autowired
    private MelinaFeignBizService melinaFeignBizService;

    @Test
    void matchStorePage() {
        RemoteMatchStorePageDTO dto = new RemoteMatchStorePageDTO();
        RemoteMatchStorePageDTO.DistanceWithinModel distanceQueryModel = new RemoteMatchStorePageDTO.DistanceWithinModel();
        distanceQueryModel.setLat("23.061894800003053");
        distanceQueryModel.setLng("113.35297013088694");
        dto.setDistanceWithinModel(distanceQueryModel);
        dto.setStoreIds(Set.of(1812692782015401985L,1810910574820933634L));
        melinaFeignBizService.matchStorePage(dto);
    }


    @Test
    public void testPageStore() {
        // 模拟输入参数
        RemoteQueryStoreByPageDTO r = new RemoteQueryStoreByPageDTO();
        r.setIds(Set.of(1831599477276471298L));
//        r.setTestFlag(1);
        // 调用待测函数
        BasePage<MelinaStorePageVO> melinaStorePageVOBasePage = melinaFeignBizService.queryStoresByPage(r);
        // 可以添加断言来验证结果
        System.out.println(melinaStorePageVOBasePage);
    }
}