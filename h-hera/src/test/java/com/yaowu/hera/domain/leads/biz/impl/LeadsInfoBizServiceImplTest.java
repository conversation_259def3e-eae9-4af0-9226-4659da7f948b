package com.yaowu.hera.domain.leads.biz.impl;

import com.yaowu.hera.domain.leads.biz.ILeadsInfoBizService;
import com.yaowu.heraapi.model.dto.mtl.HeraLeadsDetailWithCallRecordDTO;
import com.yaowu.heraapi.model.dto.mtl.RemoteOperateTodoDTO;
import com.yaowu.heraapi.model.vo.mtl.HeraLeadsInfoWithCallRecordVO;
import com.yaowu.heraapi.model.vo.mtl.RemoteLeadsDetailVO;
import jakarta.annotation.Resource;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;


@SpringBootTest
@ActiveProfiles("qa")
class LeadsInfoBizServiceImplTest {

    @Resource
    private ILeadsInfoBizService iLeadsInfoBizService;

    @Test
    void getLatest() {
        RemoteLeadsDetailVO latest = iLeadsInfoBizService.getLatest(1L,null);
        System.out.println(latest);
    }

    @Test
    void testDetail(){
        HeraLeadsDetailWithCallRecordDTO dto = new HeraLeadsDetailWithCallRecordDTO();
        dto.setStoreId(1810141187055833090L);
        dto.setLeadsInfoId(1866102163390676994L);
        dto.setUserId(0L);
        HeraLeadsInfoWithCallRecordVO heraLeadsInfoWithCallRecordVO = iLeadsInfoBizService.leadsDetailWithCallRecord(dto);
        System.out.println(heraLeadsInfoWithCallRecordVO);
    }

    @Test
    void operateTodo() {
        RemoteOperateTodoDTO dto = new RemoteOperateTodoDTO();
        dto.setOperType(1);
        dto.setTodoMsgId(1874707563356893186L);
        iLeadsInfoBizService.operateTodo(dto);
    }
}