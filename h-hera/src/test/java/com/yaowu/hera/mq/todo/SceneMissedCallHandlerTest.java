package com.yaowu.hera.mq.todo;

import cn.hutool.json.JSONUtil;
import com.yaowu.notice.model.bo.HwPrivateNumberCallEventBO;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;

import javax.annotation.Resource;

import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.ZonedDateTime;

import static org.junit.jupiter.api.Assertions.*;
@SpringBootTest
class SceneMissedCallHandlerTest {
    @Resource
    private SceneMissedCallHandler sceneMissedCallHandler;

    public static void main(String[] args) {
        LocalDateTime localDateTime = LocalDateTime.now();

        // 将 LocalDateTime 转换为带有时区的 ZonedDateTime（默认使用系统时区）
        ZonedDateTime zonedDateTime = localDateTime.atZone(ZoneId.systemDefault());

        // 获取时间戳（从1970年1月1日到现在的毫秒数）
        long timestamp = zonedDateTime.toInstant().toEpochMilli();
        System.out.println(timestamp);
    }
    @Test
    void createTodo() {

        String json = """
                {"bizType":1,"bizTypeSource":2,"bizTypeSubSource":2,"bizContentId":1877623004415639553,"orderId":1877623342480736258,"subscriptionId":"ef2ca8c6-9f12-4bc1-8b7b-1c59e5e9b012","sessionId":"SIPv5416370Eu45g621mm3e3km37k63466gje1k7j6m6@10-18-5-64","eventType":"disconnect","eventTime":"2025-01-10 17:00:47","callerNum":"+8618620976774","calleeNum":"+8615397583595","userData":"\\"bizContentId\\":1877623004415639553,\\"bizType\\":1,\\"bizTypeSource\\":2,\\"bizTypeSubSource\\":2,\\"orderId\\":1877623342480736258","testFlag":0}
                """;
        HwPrivateNumberCallEventBO bean = JSONUtil.toBean(json, HwPrivateNumberCallEventBO.class);
        //sceneMissedCallHandler.createTodo(bean);

    }
}