{"wxScenario": "1011", "channelExt": {"arg1": "", "arg2": "", "arg3": ""}, "addressDataType": 1, "address": {"provinceCode": "330000", "provinceName": "浙江省", "cityCode": "330100", "cityName": "杭州市", "districtCode": "330110", "districtName": "余杭区", "address": "", "addressTitle": "", "lat": "30.286957194010416", "lng": "119.9857874891493", "fullAddress": "浙江省杭州市余杭区"}, "curAddress": {"lng": 119.9857874891493, "lat": 30.286957194010416, "provinceCode": "330000", "provinceName": "浙江省", "cityCode": "330100", "cityName": "杭州市", "districtCode": "330110", "districtName": "余杭区", "fullAddress": "浙江省杭州市余杭区"}, "firstCategoryId": "1470569621894307842", "firstCategoryName": "升降车", "secondCategoryId": "1470569621894307843", "secondCategoryName": "剪刀车", "spuId": "1654375337837278001", "spuName": "5.6米剪叉车", "id": "1829446941320867842", "sourceLeadsInfoId": "1829446941320867842", "leadsType": 2, "sceneCode": "6", "triggerReason": 2, "startType": "100", "countType": "100", "durationType": "100"}