# PricingTool 性能优化方案

## 问题背景

### 数据现状
- **总数据量**: 1,113,583 条
- **有效数据**: 35,424 条 (delete_flag=0)，仅占 3.18%
- **慢SQL问题**: IN 查询包含 300+ 个城市编码导致查询超时

### 问题方法
`PricingToolBizServiceImpl#getBatchProcessXxlJobData` 方法中的数据库查询性能问题

## 优化方案

### 1. 应用层优化（已实现）

#### 1.1 分批查询策略
- **原问题**: 一次性查询300+个城市编码，导致IN条件过大
- **解决方案**: 实现智能分批查询
  - 小于50个区域：直接查询
  - 50-200个区域：每批30个
  - 大于200个区域：每批20个

#### 1.2 查询条件优化
- **索引对齐**: 严格按照现有索引字段顺序构建查询条件
- **现有索引**: `idx_quotetime_categoryid_areacode_quoteversion_deleteflag`
- **查询顺序**: quote_time → category_id → area_code → quote_version → delete_flag

#### 1.3 性能监控
- 添加查询耗时监控
- 慢查询告警（>1000ms）
- 详细的日志记录

### 2. 代码修改清单

#### 2.1 PriceDailyQuotesServiceImpl
```java
// 新增优化方法
public List<PriceDailyQuotes> listByConditionOptimized(...)

// 智能批次大小确定
private int determineBatchSize(int totalAreaCodes)
```

#### 2.2 PricingToolBizServiceImpl
```java
// 优化 getBatchProcessXxlJobData 方法
// 添加性能监控和日志记录
```

### 3. 数据库优化建议

#### 3.1 推荐新索引（可选）
```sql
-- 高效索引：将选择性最高的字段放在前面
CREATE INDEX idx_price_daily_quotes_optimized 
ON b_price_daily_quotes (delete_flag, quote_version, quote_time, category_id, area_code);
```

#### 3.2 数据清理建议
- 定期清理 delete_flag=1 的历史数据
- 考虑按时间分区
- 归档超过6个月的数据

### 4. 性能提升预期

#### 4.1 查询性能
- **分批查询**: 将大查询拆分为多个小查询，避免慢SQL
- **索引利用**: 确保查询条件与索引字段顺序一致
- **数据过滤**: 优先过滤选择性高的条件

#### 4.2 监控指标
- 查询耗时从 >5000ms 降低到 <1000ms
- 减少数据库连接占用时间
- 提高并发处理能力

### 5. 使用说明

#### 5.1 代码调用
```java
// 自动使用优化后的查询方法
List<PriceDailyQuotes> result = priceDailyQuotesService.listByConditionOptimized(
    quoteTimeList, categoryIds, areaCodes);
```

#### 5.2 日志监控
```
INFO  - 开始查询已存在的报价数据 - 时间范围:[2025-07-07], 品类数量:1, 区域数量:300
INFO  - 区域编码数量过多(300个)，将分15批查询，每批20个
INFO  - 查询完成: 856ms, 查询到125条已存在数据
```

### 6. 后续优化建议

#### 6.1 短期优化
1. 监控新方案的性能表现
2. 根据实际使用情况调整批次大小
3. 考虑添加缓存机制

#### 6.2 长期优化
1. 评估是否需要创建新的数据库索引
2. 考虑读写分离
3. 数据归档和分区策略

### 7. 风险评估

#### 7.1 低风险
- 应用层优化，不涉及数据库结构变更
- 向后兼容，不影响现有功能
- 可以随时回滚

#### 7.2 注意事项
- 分批查询会增加数据库连接次数
- 需要监控总体查询时间
- 大批量数据处理时注意内存使用

## 总结

通过应用层的智能分批查询和查询条件优化，在不修改数据库结构的前提下，有效解决了慢SQL问题。该方案具有低风险、高收益的特点，可以立即部署使用。
