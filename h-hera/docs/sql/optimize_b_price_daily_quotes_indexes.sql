-- =====================================================
-- b_price_daily_quotes 表性能优化分析和建议
-- =====================================================

-- 数据统计分析：
-- 1. 总数据量：1,113,583 条
-- 2. 有效数据（delete_flag=0）：35,424 条，仅占 3.18%
-- 3. 当前查询涉及多个字段：quote_time, category_id, area_code, quote_version, delete_flag
-- 4. IN查询包含300+个城市编码，导致慢SQL

-- 现有索引分析：
-- PRIMARY KEY (`id`)
-- KEY `idx_generatetime_delflag` (`generate_time`,`delete_flag`)
-- KEY `idx_quotetime_categoryid_areacode_quoteversion_deleteflag` (`quote_time`,`category_id`,`area_code`,`quote_version`,`delete_flag`)

-- 问题根源：
-- 现有索引字段顺序与查询选择性不匹配，delete_flag选择性最高但在索引末尾

-- =====================================================
-- 推荐的索引优化方案（按优先级排序）
-- =====================================================

-- 方案1：创建以delete_flag为前缀的高效索引（推荐）
-- 将选择性最高的delete_flag放在前面，大幅减少扫描数据量
CREATE INDEX idx_price_daily_quotes_optimized
ON b_price_daily_quotes (delete_flag, quote_version, quote_time, category_id, area_code);

-- 方案2：保持现有索引，通过应用层优化（当前采用）
-- 优点：不需要修改数据库结构，通过分批查询减少慢SQL
-- 实现：将大批量IN查询拆分为多个小批次查询

-- 方案3：覆盖索引优化（如果查询性能要求极高）
-- 包含查询中需要的所有字段，避免回表查询，但会占用更多存储空间
CREATE INDEX idx_price_daily_quotes_covering
ON b_price_daily_quotes (delete_flag, quote_version, quote_time, category_id, area_code,
                         id, area_level, quote_type, first_category_id, spu_id,
                         city_base_price, city_float_coefficient, city_float_price,
                         leads_info_id, generate_time, version, test_flag,
                         creator, creator_id, create_time, modifier, modifier_id, update_time);

-- 索引3：时间范围查询优化
-- 针对按时间范围查询的场景
CREATE INDEX idx_price_daily_quotes_time_range 
ON b_price_daily_quotes (delete_flag, quote_version, quote_time, category_id);

-- 索引4：分类查询优化
-- 针对按品类查询的场景
CREATE INDEX idx_price_daily_quotes_category 
ON b_price_daily_quotes (delete_flag, quote_version, category_id, quote_time);

-- =====================================================
-- 可选的单字段索引（如果复合索引空间占用过大）
-- =====================================================

-- 单字段索引：delete_flag（选择性最高）
CREATE INDEX idx_price_daily_quotes_delete_flag ON b_price_daily_quotes (delete_flag);

-- 单字段索引：quote_version
CREATE INDEX idx_price_daily_quotes_quote_version ON b_price_daily_quotes (quote_version);

-- 单字段索引：quote_time
CREATE INDEX idx_price_daily_quotes_quote_time ON b_price_daily_quotes (quote_time);

-- 单字段索引：category_id
CREATE INDEX idx_price_daily_quotes_category_id ON b_price_daily_quotes (category_id);

-- =====================================================
-- 索引使用建议
-- =====================================================

-- 1. 优先创建 idx_price_daily_quotes_core 索引
-- 2. 如果查询性能仍不满足，考虑创建覆盖索引 idx_price_daily_quotes_covering
-- 3. 定期监控索引使用情况，删除未使用的索引
-- 4. 考虑分区表策略，按 quote_time 进行分区

-- =====================================================
-- 查询优化建议
-- =====================================================

-- 1. 确保查询条件顺序与索引字段顺序一致
-- 2. 避免在 WHERE 条件中使用函数
-- 3. 使用 LIMIT 限制返回结果集大小
-- 4. 考虑使用分页查询替代大批量查询

-- =====================================================
-- 监控SQL示例
-- =====================================================

-- 查看索引使用情况
-- SHOW INDEX FROM b_price_daily_quotes;

-- 分析查询执行计划
-- EXPLAIN SELECT * FROM b_price_daily_quotes 
-- WHERE delete_flag=0 AND quote_version=1 
-- AND quote_time IN ('2025-07-07') 
-- AND category_id IN (1470569621894307843) 
-- AND area_code IN ('110100','120100');

-- 查看表统计信息
-- SHOW TABLE STATUS LIKE 'b_price_daily_quotes';

-- =====================================================
-- 数据清理建议
-- =====================================================

-- 1. 定期清理历史数据（已有 deleteHistoryData 方法）
-- 2. 考虑归档超过一定时间的数据
-- 3. 优化 delete_flag 逻辑，考虑物理删除无用数据

-- 归档历史数据示例（谨慎执行）
-- DELETE FROM b_price_daily_quotes 
-- WHERE delete_flag = 1 AND create_time < DATE_SUB(NOW(), INTERVAL 6 MONTH);
