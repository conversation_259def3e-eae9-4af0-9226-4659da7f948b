package com.yaowu.heraapi.enums.clue;

import com.baomidou.mybatisplus.annotation.IEnum;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Objects;

@Getter
@AllArgsConstructor
public enum ClueTypeEnum implements IEnum<Integer> {
    //线索类型：1-大单线索 2-普通线索
    HIGH_VALUE(1, "大单线索"),
    NORMAL(2, "普通线索");

    private final Integer code;
    private final String msg;

    public static ClueTypeEnum getByCode(Integer code) {
        for (ClueTypeEnum e : values()) {
            if (Objects.equals(e.getCode(), code)) {
                return e;
            }
        }
        return null;
    }

    @Override
    public Integer getValue() {
        return this.code;
    }
}