package com.yaowu.heraapi.enums.common;

import com.baomidou.mybatisplus.annotation.IEnum;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 结算条款版本
 *
 * <AUTHOR>
 * @date 2023/4/10
 */
@Getter
@AllArgsConstructor
public enum SettlementTermsVersionEnum implements IEnum<String> {

    /**
     * V1
     */
    V1("V1"),

    /**
     * V2
     */
    V2("V2");

    private final String version;

    /**
     * 枚举数据库存储值
     */
    @Override
    public String getValue() {
        return version;
    }
}
