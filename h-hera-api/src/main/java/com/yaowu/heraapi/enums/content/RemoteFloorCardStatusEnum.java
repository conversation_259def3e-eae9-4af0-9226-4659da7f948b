
package com.yaowu.heraapi.enums.content;

import com.baomidou.mybatisplus.annotation.IEnum;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 卡片当前状态
 *
 * <AUTHOR>
 * @date 2024/12/26-16:34
 */
@AllArgsConstructor
public enum RemoteFloorCardStatusEnum implements IEnum<String> {

    DRAFT("DRAFT", "草稿"),

    EFFECTIVE("EFFECTIVE", "已经生效"),
    ;

    @Getter
    private final String code;

    @Getter
    private final String desc;

    @Override
    public String getValue() {
        return this.code;
    }
}
