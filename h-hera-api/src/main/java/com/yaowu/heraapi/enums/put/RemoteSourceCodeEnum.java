package com.yaowu.heraapi.enums.put;

import com.baomidou.mybatisplus.annotation.IEnum;
import jodd.util.StringUtil;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Objects;

/**
 * @author: 赖春荣
 * 投放单元适用类型
 **/

@Getter
@AllArgsConstructor
@Deprecated
public enum RemoteSourceCodeEnum implements IEnum<Integer>{
    DEFAULT (0, "默认",""),
    CITY_58(1, "58同城",""),
    BAI_DU(2, "百度",""),
    SUBSCRIPTION_ACCOUNT(3, "订阅号",""),
    DOU_YIN(4, "抖音",""),
    XING_TU(5, "抖音(星图)","xingtu"),
    JU_LIANG(6, "抖音(巨量)","douyin"),
    A_MAP(7, "高德地图",""),
    OFFICIAL_WEBSITE(8, "官网",""),
    KUAI_SHOU(9, "快手","kuaishou"),
    SAN_YI(10, "三一",""),
    BUSINESS_APP(11, "商端APP",""),
    MERCHANT_APP(12, "商户APP",""),
    SHENG_SHENG(13, "省省",""),
    DIGITAL_EMPLOYEE(14, "数字员工",""),
    PRIVATE_DOMAIN(15, "私域",""),
    WEI_CHAT(16, "微信","tencent"),
    OFFLINE(17, "线下",""),
    INDUSTRY_COOPERATION(18, "异业",""),
    NATURAL_TRAFFIC(19, "自然流量",""),
    ALIPAY_APPLETS(20, "支付宝",""),
    
    TENCENT(21, "腾讯",""),
    TENCENT_MAP(22, "腾讯地图",""),
    QIAN_KE(23, "潜客",""),
    BAIDU_MAP(24, "百度地图",""),
    BUSINESS_OPERATION(25, "商家运营",""),
    OFFICIAL_ACCOUNT(26, "公众号","")
    ;


    private final Integer code;
    private final String desc;
    private final String callBackCode;


    @Override
    public Integer getValue() {
        return this.code;
    }


    public static RemoteSourceCodeEnum getByCode(Integer code) {
        if (Objects.isNull(code)) {
            return null;
        }
        for (RemoteSourceCodeEnum applyType : values()) {
            if (Objects.equals(applyType.getCode(),code)) {
                return applyType;
            }
        }
        return null;
    }
}
