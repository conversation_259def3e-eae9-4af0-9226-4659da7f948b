package com.yaowu.heraapi.enums.privatenumber;

import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.annotation.IEnum;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * @Author: liuzhenpeng
 * @Date: 2024/8/13 15:07
 */
@Getter
@AllArgsConstructor
public enum CallStatusEnum implements IEnum<Integer> {
    BLOCK_CALL(1, "未接通"),
    CONNECTED(2, "已接通");


    private final Integer code;

    private final String desc;

    public static CallStatusEnum codeOf(int code) {
        for (CallStatusEnum value : CallStatusEnum.values()) {
            if (value.getCode() == code) {
                return value;
            }
        }
        return null;
    }

    public static CallStatusEnum descOf(String desc) {
        for (CallStatusEnum value : CallStatusEnum.values()) {
            if (StrUtil.equals(value.getDesc() , desc)) {
                return value;
            }
        }
        return null;
    }

    @Override
    public Integer getValue() {
        return this.code;
    }
}
