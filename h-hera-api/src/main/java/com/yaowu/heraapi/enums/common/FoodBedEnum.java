package com.yaowu.heraapi.enums.common;

import com.baomidou.mybatisplus.annotation.IEnum;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 食宿
 *
 * <AUTHOR>
 */
@Getter
@AllArgsConstructor
public enum FoodBedEnum implements IEnum<Integer> {

    /**
     * 包食宿
     */
    CONTAIN_ALL(1, "包食宿"),

    /**
     * 包住宿，不包伙食
     */
    BED(2, "包住宿，不包伙食"),

    /**
     * 包伙食，不包住宿
     */
    FOOD(3, "包伙食，不包住宿"),

    /**
     * 不包食宿
     */
    NOT_CONTAIN(4, "不包食宿");

    private final Integer code;

    private final String msg;

    @Override
    public Integer getValue() {
        return this.code;
    }
}
