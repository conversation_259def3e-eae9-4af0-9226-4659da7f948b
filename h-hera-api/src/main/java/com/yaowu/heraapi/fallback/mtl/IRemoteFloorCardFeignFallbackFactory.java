package com.yaowu.heraapi.fallback.mtl;

import com.freedom.web.exception.BusinessException;
import com.yaowu.heraapi.feign.mtl.IRemoteFloorCardFeign;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @email <EMAIL>
 * @creatTime 2024.07.24 15:10:00
 * @description:
 */

@Component
public class IRemoteFloorCardFeignFallbackFactory implements org.springframework.cloud.openfeign.FallbackFactory<IRemoteFloorCardFeign> {

    @Override
    public IRemoteFloorCardFeign create(Throwable cause) {
        throw new BusinessException(cause.getMessage());
    }
}


