package com.yaowu.heraapi.feign.tools;

import com.freedom.feign.configuration.GeneralServiceFeignConfiguration;
import com.freedom.web.model.resp.BasePage;
import com.freedom.web.model.resp.BaseResult;
import com.yaowu.heraapi.fallback.tools.RemoteMerchantSettleInToolsFallbackFactory;
import com.yaowu.heraapi.model.dto.tools.RemoteChannelPosterImgPageDTO;
import com.yaowu.heraapi.model.dto.tools.RemoteGeneratePosterDTO;
import com.yaowu.heraapi.model.dto.tools.RemoteGetByChannelInfoDTO;
import com.yaowu.heraapi.model.vo.tools.RemoteChannelPosterImgPageVO;
import com.yaowu.heraapi.model.vo.tools.RemoteGeneratePosterVO;
import com.yaowu.heraapi.model.vo.tools.RemoteGetByChannelInfoVO;
import io.swagger.v3.oas.annotations.Operation;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;

/**
 * @Author: liuzhenpeng
 * @Date: 2025/2/14 11:35
 */
@FeignClient(value = "h-hera", url = "${remote.service.hera}", contextId = "IRemoteMerchantSettleInToolsFeign", fallbackFactory = RemoteMerchantSettleInToolsFallbackFactory.class, configuration = GeneralServiceFeignConfiguration.class)
@RequestMapping("/v1/api/tools")
public interface IRemoteMerchantSettleInToolsFeign {
    @PostMapping("/get-by-channel-info")
    @Operation(summary = "根据渠道码获取渠道公司信息")
    BaseResult<RemoteGetByChannelInfoVO> getByChannelInfo(@RequestBody @Validated RemoteGetByChannelInfoDTO dto);

//    @PostMapping("/add-log")
//    @Operation(summary = "提交生成海报记录日志")
//    BaseResult<Long> addLog(@RequestBody @Validated RemoteChannelGenerateLogDTO dto);

    @PostMapping("/get-poster-page")
    @Operation(summary = "海报列表分页")
    BaseResult<BasePage<RemoteChannelPosterImgPageVO>> getPosterPage(@RequestBody @Validated RemoteChannelPosterImgPageDTO dto);

    @PostMapping("/generate-poster")
    @Operation(summary = "生成海报（带二维码）")
    BaseResult<RemoteGeneratePosterVO> generatePoster(@RequestBody @Validated RemoteGeneratePosterDTO dto);
}
