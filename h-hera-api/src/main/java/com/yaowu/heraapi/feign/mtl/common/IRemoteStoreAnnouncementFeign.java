package com.yaowu.heraapi.feign.mtl.common;

import com.freedom.feign.configuration.GeneralServiceFeignConfiguration;
import com.freedom.security.resource.config.annotation.PermissionScope;
import com.freedom.security.resource.config.enums.ScopeLevelEnum;
import com.freedom.web.model.resp.BaseResult;
import com.yaowu.heraapi.fallback.leads.RemoteLeadsServiceFallbackFactory;
import com.yaowu.heraapi.fallback.mtl.RemoteStoreAnnouncementServiceFallbackFactory;
import com.yaowu.heraapi.model.dto.mtl.common.RemoteStoreAnnouncementDTO;
import com.yaowu.heraapi.model.vo.common.RemoteStoreAnnouncementVO;
import io.swagger.v3.oas.annotations.Operation;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;

import java.util.List;

/**
 * @Author: liuzhenpeng
 * @Date: 2024/12/25 17:23
 */
@FeignClient(
        value = "h-hera",
        url = "${remote.service.hera}",
        contextId = "IRemoteStoreAnnouncementFeign",
        fallbackFactory = RemoteStoreAnnouncementServiceFallbackFactory.class,
        configuration = GeneralServiceFeignConfiguration.class
)
@RequestMapping("/v1/api/store-announcement")
public interface IRemoteStoreAnnouncementFeign {

    @Operation(summary = "商户app公告")
    @PostMapping("/get")
    BaseResult<List<RemoteStoreAnnouncementVO>> getStoreAnnouncement(@RequestBody RemoteStoreAnnouncementDTO dto);
}
