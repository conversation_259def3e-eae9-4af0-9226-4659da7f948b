package com.yaowu.heraapi.feign.omk;

import com.freedom.feign.configuration.GeneralServiceFeignConfiguration;
import com.freedom.web.model.resp.BaseResult;
import com.yaowu.heraapi.fallback.omk.RemoteOmkOrderServiceFallbackFactory;
import com.yaowu.heraapi.model.dto.omk.RemoteOmkClueAddDTO;
import com.yaowu.heraapi.model.dto.omk.RemoteOmkRepeatOrderDTO;
import com.yaowu.heraapi.model.vo.business.RemoteBusinessCreateOrderCheckVO;
import com.yaowu.heraapi.model.vo.omk.RemoteOmkCreateOrderResultVO;
import io.swagger.v3.oas.annotations.Operation;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;

/**
 * <AUTHOR>
 * @since 2023/8/15 23:02
 **/
@FeignClient(
        value = "h-hera",
        url = "${remote.service.hera}",
        contextId = "IRemoteOmkOrderServiceFeign",
        fallbackFactory = RemoteOmkOrderServiceFallbackFactory.class,
        configuration = GeneralServiceFeignConfiguration.class
)
@RequestMapping("/v1/api/omk/order")
public interface IRemoteOmkOrderServiceFeign {

    @PostMapping("/create-order-pre-check")
    @Operation(summary = "前置校验-创建订单")
    BaseResult<RemoteBusinessCreateOrderCheckVO> addAndCreateOrderPreCheck(@RequestBody @Validated RemoteOmkClueAddDTO dto);

    @PostMapping("/create-order")
    @Operation(summary = "创建订单")
    BaseResult<RemoteOmkCreateOrderResultVO> addAndCreateOrder(@RequestBody @Validated RemoteOmkClueAddDTO dto);

    @PostMapping("/repeat-order")
    @Operation(summary = "复制订单")
    BaseResult<RemoteOmkCreateOrderResultVO> repeatOrder(@RequestBody @Validated RemoteOmkRepeatOrderDTO dto);

}
