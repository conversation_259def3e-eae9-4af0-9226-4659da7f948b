package com.yaowu.heraapi.model.dto.business;

import com.yaowu.heraapi.enums.business.ValuationRuleTypeEnum;
import com.yaowu.heraapi.enums.common.ValuationWayEnum;
import com.yaowu.heraapi.model.dto.common.RemoteSpuMappingDTO;
import com.yaowu.heraapi.model.pojo.business.BusinessDeviceExtInfo;
import com.yaowu.heraapi.model.pojo.business.BusinessSpuPriceExtInfo;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import jakarta.validation.Valid;
import jakarta.validation.constraints.Min;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import java.math.BigDecimal;
import java.time.LocalDate;

/**
 * <AUTHOR>
 * @date 2023/2/8
 */
@Data
@Schema(title = "商机设备参数")
public class RemoteBusinessDeviceDTO {

    @NotNull(message = "设备分类id不能为空")
    @Schema(title = "设备分类id", required = true)
    public Long categoryId;

    @NotBlank(message = "设备分类名称不能为空")
    @Schema(title = "设备品类名称", required = true)
    private String categoryName;

    @NotNull(message = "设备SPU id不能为空")
    @Schema(title = "设备SPU id", required = true)
    private Long spuId;

    @NotBlank(message = "设备SPU名称不能为空")
    @Schema(title = "设备SPU名称", required = true)
    private String spuName;

    @NotNull(message = "设备数量不能为空")
    @Min(value = 1, message = "数量必须大于1")
    @Schema(title = "设备数量", required = true)
    private Integer num;

    /**
     * 挖掘机业务中，进场时间按照设备维度
     */
    @Schema(title = "预计进场时间")
    private LocalDate entryDate;

    /**
     * @see ValuationWayEnum
     */
    @Schema(title = "计价方式：1-按月；2-台班")
    private Integer valuationWay;

    /**
     * @see ValuationRuleTypeEnum
     */
    @Schema(title = "计价方式对应的规则")
    private Integer valuationRuleType;

    @Schema(title = "计价规则对应的条件阈值")
    private BigDecimal valuationRuleValue;

    /**
     * 高机业务rentFloor表达的是至少租n天
     * 挖机业务，月租场景rentFloor表达的是n%
     * 挖机业务，台班场景rentFloor的是每日n台班(n可以为0)
     * 也就是，只表示n这个数值
     */
    @Schema(title = "保底下限数值")
    private BigDecimal rentFloor;

    @Schema(title = "预计租赁时间单位")
    private Integer leasingUnit;

    @Schema(title = "预计租赁时长，单位为天")
    private BigDecimal leasingDays;

    @Schema(title = "预计租赁时长，单位为月")
    private BigDecimal leasingMonths;

    @Schema(title = "月租单价(含税)")
    private BigDecimal taxMonthPrice;

    @Schema(title = "日租单价(含税)")
    private BigDecimal taxDayPrice;

    @Schema(title = "台班单价(含税)")
    private BigDecimal taxMachineTeamPrice;

    @Schema(title = "月租单价（不含税）")
    private BigDecimal monthPrice;

    @Schema(title = "台班单价（不含税）")
    private BigDecimal machineTeamPrice;

    @Schema(title = "报价是否含税：1-含税；2-不含税")
    private Integer quoteTax;

    @Valid
    @NotNull(message = "多级spu信息不能为空")
    @Schema(title = "多级spu", required = true)
    private RemoteSpuMappingDTO spuMapping;

    /**
     * 电销线索下，商户对平台报价
     * @see BusinessDeviceExtInfo
     * @see BusinessSpuPriceExtInfo
     */
    @Schema(title = "设备扩展信息")
    private String extInfo;

}
