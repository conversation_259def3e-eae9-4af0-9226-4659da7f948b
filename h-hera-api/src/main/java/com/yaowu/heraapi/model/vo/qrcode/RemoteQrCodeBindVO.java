package com.yaowu.heraapi.model.vo.qrcode;

import com.yaowu.heraapi.model.pojo.common.UploadFileModel;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 */
@Data
@Schema(title = "二维码绑定信息VO")
public class RemoteQrCodeBindVO {

    @Schema(title = "id")
    private Long id;

    @Schema(title = "二维码内容")
    private String qrCodeContent;

    @Schema(title = "二维码编号")
    private String qrCodeNo;

    @Schema(title = "场景")
    private Integer usageScenario;

    @Schema(title = "场景")
    private String usageScenarioDesc;

    @Schema(title = "位置")
    private List<Integer> positions;

    @Schema(title = "位置（中文）")
    private List<String> positionsDesc;

    @Schema(title = "备注")
    private String remark;

    @Schema(title = "绑定内容")
    private String bindContent;

    @Schema(title = "外观图片")
    private List<UploadFileModel> appearancePictures;
}
