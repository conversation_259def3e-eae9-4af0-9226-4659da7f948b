package com.yaowu.heraapi.model.dto.adtracking;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2024/11/26 13:44
 */
@Data
public class RemoteKuaishouLeadCallbackDTO {
    @ApiModelProperty("线索ID")
    private String clueId;

    @ApiModelProperty("广告账户id")
    private String accountId;

    @ApiModelProperty("预计开始时间")
    private String estimatedStartTime;

    @ApiModelProperty("二级品类名称")
    private String secondCategoryName;

    @ApiModelProperty("地址信息")
    private String addressInfo;

    @ApiModelProperty("电话号码")
    private String phoneNum;
}
