package com.yaowu.heraapi.model.vo.sell;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2023/11/21
 */
@Data
@Schema(title = "推荐线索状态VO")
public class RemoteSellClueStatusVO {

    @Schema(title = "状态值，0：全部，1：跟进中，2：已签约，3：已付款，4：已战败")
    private Integer status;

    @Schema(title = "状态描述")
    private String statusDesc;

    @Schema(title = "统计数量")
    private Integer count;

}
