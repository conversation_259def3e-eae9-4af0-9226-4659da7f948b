package com.yaowu.heraapi.model.vo.mtl;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.datatype.jsr310.ser.LocalDateTimeSerializer;
import com.yaowu.heraapi.model.pojo.common.AreaAddressModel;
import io.swagger.v3.oas.annotations.media.Schema;

import java.beans.Transient;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

import lombok.Data;

@Data
public class HeraLeadsInfoWithMerchantRelationVO {

    /**
     * 这里是需要给前端解析，所以传string，后续要用long的就加字段
     */
    @Schema(title = "线索商户关联关系id")
    private String merchantLeadRelationId;

    @Schema(title = "线索id")
    private String leadId;

    @Schema(title = "门店id")
    private String storeId;

    @Schema(title = "线索需求描述--xx地xx车xx台")
    private String leadsDesc;

    @Schema(title = "距离")
    private String distance;

    @Schema(title = "距离计算类型是否为导航：true-导航 false-直线")
    private boolean distanceCalcFlag ;

    @Schema(title = "用车时间")
    private String startType;

    @Schema(title = "线索提交时间--距当前")
    private String submitTimeAgoFromNow;

    @Schema(title = "线索使用设备地点描述（市区）")
    private String leadUseDevicePlaceDesc;

    @Schema(title = "线索使用设备地点")
    private AreaAddressModel leadUseDevicePlace;

    @Schema(title = "客户手机号")
    private String consumerPhone;

    @Schema(title = "报价单线索提交时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonSerialize(using = LocalDateTimeSerializer.class)
    private LocalDateTime submitTime;

    @Schema(title = "线索下发时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonSerialize(using = LocalDateTimeSerializer.class)
    private LocalDateTime issuedTime;

    @Schema(title = "进场时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonSerialize(using = LocalDateTimeSerializer.class)
    private LocalDateTime customStartDate;

    @Schema(title = "进场结束时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonSerialize(using = LocalDateTimeSerializer.class)
    private LocalDateTime customEndDate;

    @Schema(title = "使用日期")
    private String customDateDesc;

    @Schema(title = "线索扩展信息：品类扩展参数+施工现场图片都在扩展信息内")
    private HeraLeadBizParamExtVO bizExt;

    @Schema(title = "线索是否被查看 0:未知 1:没有查看, 2:已经查看")
    private Integer leadsViewed;

    @Schema(title = "0.未联系 1.已联系")
    private Integer contactType;

    @Schema(title = "客找商的线索生成时间已超过24h true；商找客的线索生成时间已超过72h true")
    private boolean overShowTimeFlag;
    @Schema(title = "客找商的线索生成时间已超过24h true；商找客的线索生成时间已超过72h true")
    private Integer overShowTime;

    @Schema(title = "标准价格")
    private BigDecimal standardPrice;

    @Schema(title = "折扣价格")
    private BigDecimal discountedPrice;

    @Schema(description = "券后价格")
    private BigDecimal couponPrice;

    @Schema(title = "卡实例优惠后价格")
    private BigDecimal cardInstancePrice;

    @Schema(title = "可购买卡包价格")
    private BigDecimal cardPackagePrice;

    @Schema(description = "线索购买状态 线索大厅的状态\n" +
            "1 可购买\n" +
            "2 来晚了（被取消，没人买）\n" +
            "3 已被抢（被取消，有人买，不管是否我买）\n" +
            "4 已被抢（达到购买上限）\n" +
            "5 过期了" +
            "")
    private Integer leadsPurchaseStatus;

    @Schema(title = "显示智能优化派单距离")
    private Boolean displayOptimizeMatchDistance;

    @Schema(title = "排序分值")
    private Integer orderScore;

    @Schema(title = "标签")
    private List<String> bizTags;



}
