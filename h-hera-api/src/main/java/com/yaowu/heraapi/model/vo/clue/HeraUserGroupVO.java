package com.yaowu.heraapi.model.vo.clue;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.experimental.Accessors;

import java.util.List;

/**
 * <AUTHOR>
 * @email <EMAIL>
 * @description 功能：
 * @creatTime 2024.05.27 18:40:00
 */

@Data
@Accessors(chain = true)
public class HeraUserGroupVO {

    @Schema(title = "技能组code")
    private Integer groupCode;

    @Schema(title = "技能组名称")
    private String groupName;

    @Schema(title = "技能人员名称")
    private List<String> userInfos;

}
