package com.yaowu.heraapi.model.vo.aicc;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.Date;

/**
 * @Author: Keyshawn
 * @Date: 2023/4/25 20:34
 */
@Data
public class RemoteTicketHistoryFormVO {
    @Schema(title = "表单id")
    private Integer formId;
    @Schema(title = "表单名称")
    private String formName;
    @Schema(title = "审核人")
    private String operator;
    @Schema(title = "操作人")
    private Integer operatorId;
    @Schema(title = "当前节点的名称")
    private String taskName;
    @Schema(title = "流转过程中表单的唯一标识")
    private String taskId;
    @Schema(title = "字段名称及字段值")
    private RemoteTicketHistoryFieldVO[] fields;
    @Schema(title = "节点流入时间")
    private Date nodeBeginTime;
    @Schema(title = "节点完成时间")
    private Date nodeEndTime;
    @Schema(title = "节点唯一标识")
    private String taskKey;
}
