package com.yaowu.heraapi.model.vo.aicc;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.Date;

/**
 * @Author: Keyshawn
 * @Date: 2023/4/25 20:40
 */
@Data
public class RemoteTicketSystemFormVO {
    @Schema(title = "修改人类型")
    private Integer modifierType;
    @Schema(title = "修改人类型 Id")
    private Integer modifierId;
    @Schema(title = "字段名称及字段值")
    private RemoteTicketHistoryFieldVO[] fields;
    @Schema(title = "表单更新时间")
    private Date updateTime;
}
