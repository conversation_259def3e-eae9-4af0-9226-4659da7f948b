package com.yaowu.heraapi.model.dto.favor;

import com.freedom.web.model.param.BasePageRequest;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import jakarta.validation.constraints.NotEmpty;

/**
 * <AUTHOR>
 * @since 2023/7/26 15:08
 **/
@EqualsAndHashCode(callSuper = true)
@Data
@Schema(title = "分页获取代金券批次发放明细的参数")
public class RemoteFavorStockBindDetailPageDTO extends BasePageRequest {
    @NotEmpty(message = "代金券批次的数据库id不能为空")
    @Schema(title = "代金券批次的数据库id", required = true)
    private Long id;

    @Schema(title = "指定要查询的手机号码")
    private String phoneNumber;
}
