package com.yaowu.heraapi.model.dto.mtl.aicall;

import com.freedom.web.model.param.BasePageRequest;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;

/**
 * <AUTHOR>
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class PageAiCallTaskDTO extends BasePageRequest {

    @Schema(description = "开始创建时间")
    private LocalDateTime startCreateTime;

    @Schema(description = "结束创建时间")
    private LocalDateTime endCreateTime;

    @Schema(description = "是否回调 (0=未回调，1=已回调)")
    private Boolean callbackFlag;
}
