package com.yaowu.heraapi.model.vo.mtl;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

/**
 * <AUTHOR>
 * @email <EMAIL>
 * @creatTime 2024.07.26 16:40:00
 * @description:
 */

@Data
public class HeraCustomerEarningVO {

    @Schema(title = "收益明细id", description = "收益明细id")
    private Long id;

    @Schema(title = "受邀人电话", description = "受邀人电话")
    private String inviteePhone;

    @Schema(description = "邀请记录ID", title = "邀请记录ID")
    private Long inviteRecordId;

    @Schema(description = "邀请记录ID", title = "邀请记录ID")
    private List<Long> inviteRecordIds;

    @Schema(title = "是否入账", description = "是否入账")
    private Boolean incomeFlag;

    @Schema(title = "叠加奖类型：1.达到10，2.达到20人，2.达到50人", description = "叠加奖类型：1.达到10，2.达到20人，3.达到50人,100.基础奖品")
    private Integer earningType;

    @Schema(title = "收益金额", description = "收益金额")
    private BigDecimal earningAmount;

    @Schema(title = "创建时间", description = "创建时间")
    private LocalDateTime createTime;
}
