package com.yaowu.heraapi.model.dto.content;

import com.freedom.web.annotation.handle.param.Trim;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 */
@Data
public class RemoteAddAiContentFlowDTO {

    @Schema(description = "信息流标题")
    @NotBlank(message = "信息流标题不能为空")
    private String title;

    @Schema(description = "信息流内容")
    @NotBlank(message = "信息流内容不能为空")
    private String content;

    @Schema(description = "内容标签")
    private List<String> contentTagContents;
}
