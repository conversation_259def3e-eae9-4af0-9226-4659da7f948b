package com.yaowu.heraapi.model.dto.omk;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.experimental.Accessors;

import jakarta.validation.constraints.NotNull;

/**
 * <AUTHOR>
 */
@Data
@Accessors(chain = true)
@Schema(title = "线索打标标签")
public class RemoteOmkClueTagPickDTO {

    @NotNull(message = "线索ID不能为空")
    @Schema(title = "线索ID", required = true)
    private Long clueId;

    @NotNull(message = "标签ID不能为空")
    @Schema(title = "标签ID", required = true)
    private Long tagId;

}
