package com.yaowu.heraapi.model.vo.mtl.pricing;

import com.yaowu.heraapi.model.pojo.business.SpuPriceQuotationConfig.Range;
import io.swagger.annotations.ApiModelProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.List;

/**
 * <AUTHOR>
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class RemoteSpuPriceInfoVO {

    @Schema(title = "二级品类id")
    private Long secondCategoryId;

    @Schema(title = "二级品类名称")
    private String secondCategoryName;

    @Schema(title = "台班价格信息")
    private PriceInfo shiftPriceInfo;

    @Schema(title = "月租价格信息")
    private PriceInfo monthlyRentalPriceInfo;

    @Schema(title = "趟租价格信息")
    private PriceInfo tripPriceInfo;

    @Data
    public static class PriceInfo {
        @Schema(title = "台班价格信息")
        private List<SpuPriceInfo> spuPriceInfoList;
    }

    @Data
    @Builder
    @AllArgsConstructor
    @NoArgsConstructor
    public static class SpuPriceInfo {

        @ApiModelProperty(value = "左区间")
        private Range leftRange;

        @ApiModelProperty(value = "右区间")
        private Range rightRange;

        @Schema(title = "spu类型描述:吨位:x吨、米数:x米")
        private String spuTypeDesc;

        @Schema(title = "浮动价格")
        private BigDecimal floatPrice;

        @Schema(title = "一级品类id")
        private Long firstCategoryId;

        @Schema(title = "一级品类名称")
        private String firstCategoryName;

        @Schema(title = "二级品类id")
        private Long secondCategoryId;

        @Schema(title = "二级品类名称")
        private String secondCategoryName;

        @Schema(title = "近7天价格走势图")
        private List<PriceTrendInfo> priceTrendInfoList;
    }

    @Data
    public static class PriceTrendInfo {
        @Schema(title = "日期")
        private LocalDate date;

        @Schema(title = "浮动价格")
        private BigDecimal floatPrice;
    }
}
