package com.yaowu.heraapi.model.dto.mtl.leadsdevice;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Schema(description = "SPU合集查询参数")
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class RemoteSpuCollectionQueryDTO {

    @Schema(description = "二级品类ID")
    private Long secondCategoryId;
}