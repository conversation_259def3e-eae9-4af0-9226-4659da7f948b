package com.yaowu.heraapi.model.vo.content;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;


/**
 * <AUTHOR>
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class RemoteContentBehaviorBaseInfoVO {

    @Schema(description = "曝光次数")
    private Long exposureCount;

    @Schema(description = "点赞人数")
    private Long likeCount;

    @Schema(description = "分享人数")
    private Long shareCount;

    @Schema(description = "分享次数")
    private Long shareTimes;

}
