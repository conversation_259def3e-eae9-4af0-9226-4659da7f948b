package com.yaowu.heraapi.model.vo.mtl.order;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.experimental.Accessors;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @email <EMAIL>
 * @creatTime 2024.10.10 10:09:00
 * @description:
 */

@Data
@Accessors(chain = true)
@Schema(title = "MTL订单简易信息")
public class HeraLeadsOrderSimpleVO {

    @Schema(title = "订单id")
    private Long id;

    @Schema(title = "订单编号")
    private String code;

    @Schema(title = "报价单线索id")
    private Long leadsInfoId;

    @Schema(title = "商户id")
    private Long merchantId;

    @Schema(title = "店铺id")
    private Long storeId;

    /**
     * @see com.yaowu.heraapi.enums.mtl.LeadsOrderStatusEnum
     */
    @Schema(title = "订单状态: 10-已支付 20-已退款")
    private Integer orderStatus;

    @Schema(title = "支付价格")
    private BigDecimal paymentPrice;

    @Schema(description = "券后价格")
    private BigDecimal couponPrice;

    @Schema(title = "优惠券折扣金额")
    private BigDecimal couponDiscountAmount;

    @Schema(title = "卡折扣金额")
    private BigDecimal cardDiscountAmount;

    @Schema(title = "支付时间")
    private LocalDateTime paymentTime;

    @Schema(title = "退款时间")
    private LocalDateTime refundTime;

    @Schema(title = "订单退款备注")
    private String refundRemark;

    @Schema(title = "创建时间")
    private LocalDateTime createTime;

    @Schema(title = "更新时间")
    private LocalDateTime updateTime;

    @Schema(title = "订单来源")
    private Integer orderSource;

}
