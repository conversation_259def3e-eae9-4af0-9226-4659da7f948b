package com.yaowu.heraapi.model.dto.mtl;

import com.yaowu.heraapi.enums.mtl.order.OrderContactSceneEnum;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Set;

@Data
@Schema(title = "商打客获取隐私号对象")
public class RemoteMerchantInquiryCustomerDTO implements Serializable {
    @Schema(title = "门店id")
    @NotNull(message = "门店id不能为空")
    private Long storeId;

    @Schema(title = "商户主叫号")
    @NotBlank(message = "主叫不能为空")
    private String merchantCallNumber;

    @Schema(title = "线索id")
    private Long leadsInfoId;



}