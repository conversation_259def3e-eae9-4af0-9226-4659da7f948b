package com.yaowu.heraapi.model.vo.common;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.extension.handlers.JacksonTypeHandler;
import com.yaowu.heraapi.model.dto.mtl.common.RemoteTakeOrderSaveOrUpdateDTO;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.util.List;

/**
 * @Author: liuzhenpeng
 * @Date: 2024/12/27 17:42
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Schema(title = "RemoteTakeOrderSettingDetailVO")
public class RemoteTakeOrderSettingDetailVO {

    @Schema(description = "门店id")
    private Long storeId;

    /**
     * @see com.yaowu.heraapi.enums.mtl.StoreTakeOrderStatusEnum#getCode()
     */
    @Schema(description = "接单宝状态（0：未开启，1：再次关闭，2：开启）")
    private Integer takeOrderStatus;

    @Schema(description = "设备品类")
    private List<CategoryInfoVO> categoryInfo;

    @Schema(description = "接单范围（单位：km）")
    private Integer scope;

    @Schema(description = "单日扣款金额上限")
    private BigDecimal amountLimit;

    @Data
    public static class CategoryInfoVO {
        @Schema(description = "设备一级品类")
        private Long firstCategoryId;

        @Schema(description = "设备二级品类名称")
        private String firstCategoryName;

        @Schema(description = "设备二级品类")
        private Long secondCategoryId;

        @Schema(description = "设备二级品类名称")
        private String secondCategoryName;
    }
}
