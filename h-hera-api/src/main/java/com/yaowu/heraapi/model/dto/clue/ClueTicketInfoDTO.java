package com.yaowu.heraapi.model.dto.clue;

import com.yaowu.heraapi.enums.omk.ClueTicketSourceType;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.experimental.Accessors;

import jakarta.validation.constraints.NotNull;

/**
 * <AUTHOR>
 * @description
 * @date 2023:07:18 13:47:48
 */
@Data
@Accessors(chain = true)
public class ClueTicketInfoDTO {


    @NotNull(message = "来源类型不能为空")
    @Schema(title = "来源类型：1 电销系统、2 天润系统、3、LTC 小程序")
    private ClueTicketSourceType sourceType;

    @Schema(title = "工单id")
    private String ticketId;

    @Schema(title = "提交时间")
    private String operatorTime;

    @Schema(title = "客户渠道")
    private String channelDesc;

    @Schema(title = "线索渠道(线索来源渠道 != null ? 线索来源渠道 : 客户渠道)")
    private String channel;

    @Schema(title = "线索来源渠道")
    private String clueChannelDesc;

    @Schema(title = "省份")
    private String provinceName;

    @Schema(title = "城市")
    private String cityName;

    @Schema(title = "区域")
    private String districtName;

    @Schema(title = "需求设备")
    private String deviceRequirement;

    @Schema(title = "预计使用时长")
    private String expectedUsageTime;

    @Schema(title = "预计使用时间")
    private String expectedEntryDate;

    @Schema(title = "联系人")
    private String name;

    @Schema(title = "联系号码")
    private String phone;

    @Schema(title = "备注")
    private String remark;

    @Schema(title = "租赁方式")
    private String leaseModel;

    @Schema(title = "身份，客户，商户")
    private Integer identityDesc;

    @Schema(title = "客服评级")
    private String clueLevelDesc;


    @Schema(title = "客服评级")
    private String clueLevel;


}
