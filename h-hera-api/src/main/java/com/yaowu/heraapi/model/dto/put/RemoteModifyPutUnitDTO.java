package com.yaowu.heraapi.model.dto.put;

import com.yaowu.heraapi.enums.put.RemoteLeadsCallbackEnum;
import com.yaowu.heraapi.enums.put.RemoteMediaTypeEnum;
import com.yaowu.heraapi.enums.put.RemoteRegCallbackEnum;
import com.yaowu.heraapi.enums.put.RemoteSourceCodeEnum;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.models.security.SecurityScheme.In;
import jakarta.validation.constraints.NotNull;
import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

/**
 * <AUTHOR>
 */
@Data
@Accessors(chain = true)
@Schema(title = "编辑渠道单元DTO")
public class RemoteModifyPutUnitDTO {

    @Schema(title = "投放单元id")
    @NotNull(message = "投放单元id不能为空")
    private Long putUnitId;

    @Schema(title = "适用类型：0-通用、1-商端、2-客端")
    private Integer applyType;

    @Schema(title = "推广计划")
    private String putPlan;

    @Schema(title = "推广单元")
    private String putUnitName;

    @Schema(title = "归类：一级归类、二级归类")
    private PutUnitClassificationModel classificationJson;

    @Schema(title = "投放渠道,支持多个投放渠道")
    private List<PutUnitChannelModel> putChannelJson;

    @Schema(title = "小程序投放路径")
    private String appletPath;

    @Schema(title = "h5投放路径")
    private String h5Path;

    @Schema(title = "推广计划id")
    @NotNull(message = "推广计划id不能为空")
    private Long putPlanId;

    @Schema(description = "投放平台：kuaishou-快手,douyin-抖音(巨量),tencent-(微信),xingtu-(抖音星图)")
    private Integer sourceCode;

    @Schema(description = "投放载体=投放媒介:1-微信小程序 2-快手小程序 3-支付宝小程序 4-抖音小程序 5-H5")
    private RemoteMediaTypeEnum mediaType;

    @Schema(description = "注册回传 1:回传 2:不回穿")
    private RemoteRegCallbackEnum regCallback;

    @Schema(description = "留资回传 1:回传 2:不回穿")
    private RemoteLeadsCallbackEnum leadsCallback;

    @Schema(description = "限制回传品类：二级品类ids")
    private List<Long> limitCategory;

    @Schema(description = "渠道备注")
    private String channelRemark;


    @NoArgsConstructor
    @AllArgsConstructor
    @Data
    public static class PutUnitClassificationModel {

        @Schema(title = "一级分类")
        private String firstClassification;

        @Schema(title = "二级分类")
        private String secondClassification;

    }

    @NoArgsConstructor
    @AllArgsConstructor
    @Data
    public static class PutUnitChannelModel {

        @Schema(title = "一级分类")
        private String channelKey;

        @Schema(title = "二级分类")
        private String channelValue;
    }


}
