package com.yaowu.heraapi.model.dto.content;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 */
@Data
public class RemoteEditContentPropertyDTO {

    @Schema(description = "信息流内容id")
    @NotNull(message = "信息流内容id不能为空")
    private Long id;

    @Schema(description = "内容标签")
    private List<Long> contentTagIds;

    @Schema(description = "推荐品类")
    private List<Long> recommendCategoryIds;
}
