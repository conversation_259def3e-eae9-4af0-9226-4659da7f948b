package com.yaowu.heraapi.model.dto.clue;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import jakarta.validation.constraints.NotEmpty;
import java.util.List;

/**
 * <AUTHOR>
 * @email <EMAIL>
 * @description 功能：
 * @creatTime 2024.05.27 20:52:00
 */

@Data
public class HeraEditRuleDTO {

    @Schema(title = "用户id")
    private Long userId;

    @Schema(title = "用户名称")
    private String userName;

    @NotEmpty(message = "规则集不能为空")
    @Schema(title = "规则集")
    private List<HeraRuleDTO> rules;
}
