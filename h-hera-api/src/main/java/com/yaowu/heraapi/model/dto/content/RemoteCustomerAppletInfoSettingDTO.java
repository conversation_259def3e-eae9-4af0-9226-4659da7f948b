package com.yaowu.heraapi.model.dto.content;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

/**
 * @Author: liuzhenpeng
 * @Date: 2024/11/4 15:14
 */
@Data
@Schema(description = "RemoteCustomerAppletInfoSettingDTO")
public class RemoteCustomerAppletInfoSettingDTO {

    @Schema(title ="用户id")
    private Long userId;

    @NotNull(message = "openid不能为空")
    @Schema(title ="openid")
    private String openId;

    @Schema(title ="用户昵称")
    private String nickName;

    @Schema(title ="头像")
    private String profilePhoto;
}
