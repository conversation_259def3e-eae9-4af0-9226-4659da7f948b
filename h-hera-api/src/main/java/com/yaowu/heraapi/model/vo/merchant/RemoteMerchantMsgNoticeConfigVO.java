package com.yaowu.heraapi.model.vo.merchant;


import com.yaowu.heraapi.enums.merchant.RemoteMerchantMsgConfigNoticeTypeEnum;
import com.yaowu.heraapi.model.pojo.common.RemoteCodeAndDescVO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

/**
 * @author: huangyuheng
 * @date: 2024-10-21 17:43
 * @desc:
 */
@Data
public class RemoteMerchantMsgNoticeConfigVO {


    /**
     * @see RemoteMerchantMsgConfigNoticeTypeEnum
     */
    @Schema(title = "通知类型：1:新线索通知")
    private Integer noticeType;

    @Schema(title = "通知类型描述")
    private String noticeTypeDesc;

    @Schema(title = "通知方式")
    private List<RemoteCodeAndDescVO> noticeWayConfigs;

    @Schema(title = "不通知时间段")
    private List<RemoteCodeAndDescVO> noNoticeTimeConfigs;

    @Schema(title = "允许通知")
    private Boolean allowNotice;

    @Schema(title = "夜间不通知")
    private Boolean noNoticeNight;
}
