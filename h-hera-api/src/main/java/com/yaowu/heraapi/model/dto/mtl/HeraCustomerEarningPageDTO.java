package com.yaowu.heraapi.model.dto.mtl;

import com.freedom.web.model.param.BasePageRequest;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

/**
 * <AUTHOR>
 * @email <EMAIL>
 * @creatTime 2024.07.26 16:48:00
 * @description:
 */

@Data
public class HeraCustomerEarningPageDTO extends BasePageRequest {
    @Schema
    @NotNull(message = "用户id不能为空")
    private Long userId;
}
