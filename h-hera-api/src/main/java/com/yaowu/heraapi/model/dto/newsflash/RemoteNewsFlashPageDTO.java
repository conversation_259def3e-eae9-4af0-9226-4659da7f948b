package com.yaowu.heraapi.model.dto.newsflash;

import com.freedom.web.model.param.BasePageRequest;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * @author: huang<PERSON><PERSON>g
 * @date: 2024-11-07 01:42
 * @desc:
 */

@Data
public class RemoteNewsFlashPageDTO extends BasePageRequest {
    @Schema(title = "快讯状态")
    private Integer status;

    private LocalDate queryDateStart;

    private LocalDate queryDateEnd;

    private LocalDateTime queryPublishTimeStart;

    private LocalDateTime queryPublishTimeEnd;
}
