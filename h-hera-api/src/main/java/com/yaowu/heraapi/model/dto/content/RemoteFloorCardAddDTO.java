package com.yaowu.heraapi.model.dto.content;

import com.yaowu.heraapi.enums.content.RemoteFloorCardTypeEnum;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2024/12/26-20:10
 */
@Data
public class RemoteFloorCardAddDTO {

    /**
     * 卡片标题
     */
    @Schema(description = "卡片标题", requiredMode = Schema.RequiredMode.REQUIRED)
    private String title;

    /**
     * 卡片类型
     */
    @Schema(description = "卡片类型,DEFAULT:默认类型", defaultValue = "DEFAULT")
    private RemoteFloorCardTypeEnum type;

    /**
     * 卡片使用范围
     */
    @Schema(description = "卡片使用范围,默认APP_INDEX:APP首页", defaultValue = "APP_INDEX")
    private String useScope;

    /**
     * 卡片内容，根据type进行路由
     * 根据type进行数据路由 {@link RemoteFloorCardTypeEnum}
     */
    @Schema(description = "卡片内容，json字符串")
    private String cardInfo;
}
